"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/ag-grid-react";
exports.ids = ["vendor-chunks/ag-grid-react"];
exports.modules = {

/***/ "(ssr)/./node_modules/ag-grid-react/dist/package/index.esm.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/ag-grid-react/dist/package/index.esm.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AgGridReact: () => (/* binding */ AgGridReact),\n/* harmony export */   CustomComponentContext: () => (/* binding */ CustomContext),\n/* harmony export */   getInstance: () => (/* binding */ getInstance),\n/* harmony export */   useGridCellEditor: () => (/* binding */ useGridCellEditor),\n/* harmony export */   useGridDate: () => (/* binding */ useGridDate),\n/* harmony export */   useGridFilter: () => (/* binding */ useGridFilter),\n/* harmony export */   useGridFloatingFilter: () => (/* binding */ useGridFloatingFilter),\n/* harmony export */   useGridMenuItem: () => (/* binding */ useGridMenuItem),\n/* harmony export */   warnReactiveCustomComponents: () => (/* binding */ warnReactiveCustomComponents)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var ag_grid_community__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ag-grid-community */ \"(ssr)/./node_modules/ag-grid-community/dist/package/main.esm.mjs\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n// packages/ag-grid-react/src/agGridReact.tsx\n\n\n// packages/ag-grid-react/src/reactUi/agGridReactUi.tsx\n\n\n\n// packages/ag-grid-react/src/reactUi/cellRenderer/groupCellRenderer.tsx\n\n\n\n// packages/ag-grid-react/src/reactUi/beansContext.tsx\n\nvar BeansContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext({});\n\n// packages/ag-grid-react/src/reactUi/jsComp.tsx\nvar showJsComp = (compDetails, context, eParent, ref) => {\n  const doNothing = !compDetails || compDetails.componentFromFramework || context.isDestroyed();\n  if (doNothing) {\n    return;\n  }\n  const promise = compDetails.newAgStackInstance();\n  let comp;\n  let compGui;\n  let destroyed = false;\n  promise.then((c) => {\n    if (destroyed) {\n      context.destroyBean(c);\n      return;\n    }\n    comp = c;\n    compGui = comp.getGui();\n    eParent.appendChild(compGui);\n    setRef(ref, comp);\n  });\n  return () => {\n    destroyed = true;\n    if (!comp) {\n      return;\n    }\n    compGui?.parentElement?.removeChild(compGui);\n    context.destroyBean(comp);\n    if (ref) {\n      setRef(ref, void 0);\n    }\n  };\n};\nvar setRef = (ref, value) => {\n  if (!ref) {\n    return;\n  }\n  if (ref instanceof Function) {\n    const refCallback = ref;\n    refCallback(value);\n  } else {\n    const refObj = ref;\n    refObj.current = value;\n  }\n};\n\n// packages/ag-grid-react/src/reactUi/utils.tsx\n\n\nvar classesList = (...list) => {\n  const filtered = list.filter((s) => s != null && s !== \"\");\n  return filtered.join(\" \");\n};\nvar CssClasses = class _CssClasses {\n  constructor(...initialClasses) {\n    this.classesMap = {};\n    initialClasses.forEach((className) => {\n      this.classesMap[className] = true;\n    });\n  }\n  setClass(className, on) {\n    const nothingHasChanged = !!this.classesMap[className] == on;\n    if (nothingHasChanged) {\n      return this;\n    }\n    const res = new _CssClasses();\n    res.classesMap = { ...this.classesMap };\n    res.classesMap[className] = on;\n    return res;\n  }\n  toString() {\n    const res = Object.keys(this.classesMap).filter((key) => this.classesMap[key]).join(\" \");\n    return res;\n  }\n};\nvar isComponentStateless = (Component2) => {\n  const hasSymbol = () => typeof Symbol === \"function\" && Symbol.for;\n  const getMemoType = () => hasSymbol() ? Symbol.for(\"react.memo\") : 60115;\n  return typeof Component2 === \"function\" && !(Component2.prototype && Component2.prototype.isReactComponent) || typeof Component2 === \"object\" && Component2.$$typeof === getMemoType();\n};\nvar reactVersion = react__WEBPACK_IMPORTED_MODULE_0__.version?.split(\".\")[0];\nvar isReactVersion17Minus = reactVersion === \"16\" || reactVersion === \"17\";\nfunction isReact19() {\n  return reactVersion === \"19\";\n}\nvar disableFlushSync = false;\nfunction runWithoutFlushSync(func) {\n  if (!disableFlushSync) {\n    setTimeout(() => disableFlushSync = false, 0);\n  }\n  disableFlushSync = true;\n  return func();\n}\nvar agFlushSync = (useFlushSync, fn) => {\n  if (!isReactVersion17Minus && useFlushSync && !disableFlushSync) {\n    react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(fn);\n  } else {\n    fn();\n  }\n};\nfunction getNextValueIfDifferent(prev, next, maintainOrder) {\n  if (next == null || prev == null) {\n    return next;\n  }\n  if (prev === next || next.length === 0 && prev.length === 0) {\n    return prev;\n  }\n  if (maintainOrder || prev.length === 0 && next.length > 0 || prev.length > 0 && next.length === 0) {\n    return next;\n  }\n  const oldValues = [];\n  const newValues = [];\n  const prevMap = /* @__PURE__ */ new Map();\n  const nextMap = /* @__PURE__ */ new Map();\n  for (let i = 0; i < next.length; i++) {\n    const c = next[i];\n    nextMap.set(c.instanceId, c);\n  }\n  for (let i = 0; i < prev.length; i++) {\n    const c = prev[i];\n    prevMap.set(c.instanceId, c);\n    if (nextMap.has(c.instanceId)) {\n      oldValues.push(c);\n    }\n  }\n  for (let i = 0; i < next.length; i++) {\n    const c = next[i];\n    const instanceId = c.instanceId;\n    if (!prevMap.has(instanceId)) {\n      newValues.push(c);\n    }\n  }\n  if (oldValues.length === prev.length && newValues.length === 0) {\n    return prev;\n  }\n  if (oldValues.length === 0 && newValues.length === next.length) {\n    return next;\n  }\n  if (oldValues.length === 0) {\n    return newValues;\n  }\n  if (newValues.length === 0) {\n    return oldValues;\n  }\n  return [...oldValues, ...newValues];\n}\n\n// packages/ag-grid-react/src/reactUi/cellRenderer/groupCellRenderer.tsx\nvar GroupCellRenderer = (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((props, ref) => {\n  const { registry, context } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eValueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eCheckboxRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eExpandedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eContractedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const ctrlRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const [innerCompDetails, setInnerCompDetails] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [childCount, setChildCount] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [value, setValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [cssClasses, setCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses());\n  const [expandedCssClasses, setExpandedCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses(\"ag-hidden\"));\n  const [contractedCssClasses, setContractedCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses(\"ag-hidden\"));\n  const [checkboxCssClasses, setCheckboxCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses(\"ag-invisible\"));\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(ref, () => {\n    return {\n      // force new instance when grid tries to refresh\n      refresh() {\n        return false;\n      }\n    };\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => {\n    return showJsComp(innerCompDetails, context, eValueRef.current);\n  }, [innerCompDetails]);\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGui.current = eRef;\n    if (!eRef) {\n      ctrlRef.current = context.destroyBean(ctrlRef.current);\n      return;\n    }\n    const compProxy = {\n      setInnerRenderer: (details, valueToDisplay) => {\n        setInnerCompDetails(details);\n        setValue(valueToDisplay);\n      },\n      setChildCount: (count) => setChildCount(count),\n      addOrRemoveCssClass: (name, on) => setCssClasses((prev) => prev.setClass(name, on)),\n      setContractedDisplayed: (displayed) => setContractedCssClasses((prev) => prev.setClass(\"ag-hidden\", !displayed)),\n      setExpandedDisplayed: (displayed) => setExpandedCssClasses((prev) => prev.setClass(\"ag-hidden\", !displayed)),\n      setCheckboxVisible: (visible) => setCheckboxCssClasses((prev) => prev.setClass(\"ag-invisible\", !visible)),\n      setCheckboxSpacing: (add) => setCheckboxCssClasses((prev) => prev.setClass(\"ag-group-checkbox-spacing\", add))\n    };\n    const groupCellRendererCtrl = registry.createDynamicBean(\"groupCellRendererCtrl\", true);\n    if (groupCellRendererCtrl) {\n      ctrlRef.current = context.createBean(groupCellRendererCtrl);\n      ctrlRef.current.init(\n        compProxy,\n        eRef,\n        eCheckboxRef.current,\n        eExpandedRef.current,\n        eContractedRef.current,\n        GroupCellRenderer,\n        props\n      );\n    }\n  }, []);\n  const className = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => `ag-cell-wrapper ${cssClasses.toString()}`, [cssClasses]);\n  const expandedClassName = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => `ag-group-expanded ${expandedCssClasses.toString()}`, [expandedCssClasses]);\n  const contractedClassName = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => `ag-group-contracted ${contractedCssClasses.toString()}`,\n    [contractedCssClasses]\n  );\n  const checkboxClassName = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => `ag-group-checkbox ${checkboxCssClasses.toString()}`, [checkboxCssClasses]);\n  const useFwRenderer = innerCompDetails && innerCompDetails.componentFromFramework;\n  const FwRenderer = useFwRenderer ? innerCompDetails.componentClass : void 0;\n  const useValue = innerCompDetails == null && value != null;\n  const escapedValue = (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._escapeString)(value, true);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    \"span\",\n    {\n      className,\n      ref: setRef2,\n      ...!props.colDef ? { role: ctrlRef.current?.getCellAriaRole() } : {}\n    },\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", { className: expandedClassName, ref: eExpandedRef }),\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", { className: contractedClassName, ref: eContractedRef }),\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", { className: checkboxClassName, ref: eCheckboxRef }),\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", { className: \"ag-group-value\", ref: eValueRef }, useValue && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, escapedValue), useFwRenderer && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(FwRenderer, { ...innerCompDetails.params })),\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", { className: \"ag-group-child-count\" }, childCount)\n  );\n});\nvar groupCellRenderer_default = GroupCellRenderer;\n\n// packages/ag-grid-react/src/shared/customComp/customComponentWrapper.ts\n\n\n// packages/ag-grid-react/src/reactUi/customComp/customWrapperComp.tsx\n\n\n// packages/ag-grid-react/src/shared/customComp/customContext.ts\n\nvar CustomContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({\n  setMethods: () => {\n  }\n});\n\n// packages/ag-grid-react/src/reactUi/customComp/customWrapperComp.tsx\nvar CustomWrapperComp = (params) => {\n  const { initialProps, addUpdateCallback, CustomComponentClass, setMethods } = params;\n  const [{ key, ...props }, setProps] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialProps);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    addUpdateCallback((newProps) => setProps(newProps));\n  }, []);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CustomContext.Provider, { value: { setMethods } }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CustomComponentClass, { key, ...props }));\n};\nvar customWrapperComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(CustomWrapperComp);\n\n// packages/ag-grid-react/src/shared/reactComponent.ts\n\n\n\n\n// packages/ag-grid-react/src/shared/keyGenerator.ts\nvar counter = 0;\nfunction generateNewKey() {\n  return `agPortalKey_${++counter}`;\n}\n\n// packages/ag-grid-react/src/shared/reactComponent.ts\nvar ReactComponent = class {\n  constructor(reactComponent, portalManager, componentType, suppressFallbackMethods) {\n    this.portal = null;\n    this.oldPortal = null;\n    this.reactComponent = reactComponent;\n    this.portalManager = portalManager;\n    this.componentType = componentType;\n    this.suppressFallbackMethods = !!suppressFallbackMethods;\n    this.statelessComponent = this.isStateless(this.reactComponent);\n    this.key = generateNewKey();\n    this.portalKey = generateNewKey();\n    this.instanceCreated = this.isStatelessComponent() ? ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.AgPromise.resolve(false) : new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.AgPromise((resolve) => {\n      this.resolveInstanceCreated = resolve;\n    });\n  }\n  getGui() {\n    return this.eParentElement;\n  }\n  /** `getGui()` returns the parent element. This returns the actual root element. */\n  getRootElement() {\n    const firstChild = this.eParentElement.firstChild;\n    return firstChild;\n  }\n  destroy() {\n    if (this.componentInstance && typeof this.componentInstance.destroy == \"function\") {\n      this.componentInstance.destroy();\n    }\n    const portal = this.portal;\n    if (portal) {\n      this.portalManager.destroyPortal(portal);\n    }\n  }\n  createParentElement(params) {\n    const componentWrappingElement = this.portalManager.getComponentWrappingElement();\n    const eParentElement = document.createElement(componentWrappingElement || \"div\");\n    eParentElement.classList.add(\"ag-react-container\");\n    params.reactContainer = eParentElement;\n    return eParentElement;\n  }\n  statelessComponentRendered() {\n    return this.eParentElement.childElementCount > 0 || this.eParentElement.childNodes.length > 0;\n  }\n  getFrameworkComponentInstance() {\n    return this.componentInstance;\n  }\n  isStatelessComponent() {\n    return this.statelessComponent;\n  }\n  getReactComponentName() {\n    return this.reactComponent.name;\n  }\n  getMemoType() {\n    return this.hasSymbol() ? Symbol.for(\"react.memo\") : 60115;\n  }\n  hasSymbol() {\n    return typeof Symbol === \"function\" && Symbol.for;\n  }\n  isStateless(Component2) {\n    return typeof Component2 === \"function\" && !(Component2.prototype && Component2.prototype.isReactComponent) || typeof Component2 === \"object\" && Component2.$$typeof === this.getMemoType();\n  }\n  hasMethod(name) {\n    const frameworkComponentInstance = this.getFrameworkComponentInstance();\n    return !!frameworkComponentInstance && frameworkComponentInstance[name] != null || this.fallbackMethodAvailable(name);\n  }\n  callMethod(name, args) {\n    const frameworkComponentInstance = this.getFrameworkComponentInstance();\n    if (this.isStatelessComponent()) {\n      return this.fallbackMethod(name, !!args && args[0] ? args[0] : {});\n    } else if (!frameworkComponentInstance) {\n      setTimeout(() => this.callMethod(name, args));\n      return;\n    }\n    const method = frameworkComponentInstance[name];\n    if (method) {\n      return method.apply(frameworkComponentInstance, args);\n    }\n    if (this.fallbackMethodAvailable(name)) {\n      return this.fallbackMethod(name, !!args && args[0] ? args[0] : {});\n    }\n  }\n  addMethod(name, callback) {\n    this[name] = callback;\n  }\n  init(params) {\n    this.eParentElement = this.createParentElement(params);\n    this.createOrUpdatePortal(params);\n    return new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.AgPromise((resolve) => this.createReactComponent(resolve));\n  }\n  createOrUpdatePortal(params) {\n    if (!this.isStatelessComponent()) {\n      this.ref = (element) => {\n        this.componentInstance = element;\n        this.resolveInstanceCreated?.(true);\n        this.resolveInstanceCreated = void 0;\n      };\n      params.ref = this.ref;\n    }\n    this.reactElement = this.createElement(this.reactComponent, { ...params, key: this.key });\n    this.portal = (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)(\n      this.reactElement,\n      this.eParentElement,\n      this.portalKey\n      // fixed deltaRowModeRefreshCompRenderer\n    );\n  }\n  createElement(reactComponent, props) {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(reactComponent, props);\n  }\n  createReactComponent(resolve) {\n    this.portalManager.mountReactPortal(this.portal, this, resolve);\n  }\n  rendered() {\n    return this.isStatelessComponent() && this.statelessComponentRendered() || !!(!this.isStatelessComponent() && this.getFrameworkComponentInstance());\n  }\n  /*\n   * fallback methods - these will be invoked if a corresponding instance method is not present\n   * for example if refresh is called and is not available on the component instance, then refreshComponent on this\n   * class will be invoked instead\n   *\n   * Currently only refresh is supported\n   */\n  refreshComponent(args) {\n    this.oldPortal = this.portal;\n    this.createOrUpdatePortal(args);\n    this.portalManager.updateReactPortal(this.oldPortal, this.portal);\n  }\n  fallbackMethod(name, params) {\n    const method = this[`${name}Component`];\n    if (!this.suppressFallbackMethods && !!method) {\n      return method.bind(this)(params);\n    }\n  }\n  fallbackMethodAvailable(name) {\n    if (this.suppressFallbackMethods) {\n      return false;\n    }\n    const method = this[`${name}Component`];\n    return !!method;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/customComponentWrapper.ts\nfunction addOptionalMethods(optionalMethodNames, providedMethods, component) {\n  optionalMethodNames.forEach((methodName) => {\n    const providedMethod = providedMethods[methodName];\n    if (providedMethod) {\n      component[methodName] = providedMethod;\n    }\n  });\n}\nvar CustomComponentWrapper = class extends ReactComponent {\n  constructor() {\n    super(...arguments);\n    this.awaitUpdateCallback = new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.AgPromise((resolve) => {\n      this.resolveUpdateCallback = resolve;\n    });\n    this.wrapperComponent = customWrapperComp_default;\n  }\n  init(params) {\n    this.sourceParams = params;\n    return super.init(this.getProps());\n  }\n  addMethod() {\n  }\n  getInstance() {\n    return this.instanceCreated.then(() => this.componentInstance);\n  }\n  getFrameworkComponentInstance() {\n    return this;\n  }\n  createElement(reactComponent, props) {\n    return super.createElement(this.wrapperComponent, {\n      initialProps: props,\n      CustomComponentClass: reactComponent,\n      setMethods: (methods) => this.setMethods(methods),\n      addUpdateCallback: (callback) => {\n        this.updateCallback = () => {\n          callback(this.getProps());\n          return new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.AgPromise((resolve) => {\n            setTimeout(() => {\n              resolve();\n            });\n          });\n        };\n        this.resolveUpdateCallback();\n      }\n    });\n  }\n  setMethods(methods) {\n    this.providedMethods = methods;\n    addOptionalMethods(this.getOptionalMethods(), this.providedMethods, this);\n  }\n  getOptionalMethods() {\n    return [];\n  }\n  getProps() {\n    return {\n      ...this.sourceParams,\n      key: this.key,\n      ref: this.ref\n    };\n  }\n  refreshProps() {\n    if (this.updateCallback) {\n      return this.updateCallback();\n    }\n    return new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.AgPromise(\n      (resolve) => this.awaitUpdateCallback.then(() => {\n        this.updateCallback().then(() => resolve());\n      })\n    );\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/cellRendererComponentWrapper.ts\nvar CellRendererComponentWrapper = class extends CustomComponentWrapper {\n  refresh(params) {\n    this.sourceParams = params;\n    this.refreshProps();\n    return true;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/dateComponentWrapper.ts\nvar DateComponentWrapper = class extends CustomComponentWrapper {\n  constructor() {\n    super(...arguments);\n    this.date = null;\n    this.onDateChange = (date) => this.updateDate(date);\n  }\n  getDate() {\n    return this.date;\n  }\n  setDate(date) {\n    this.date = date;\n    this.refreshProps();\n  }\n  refresh(params) {\n    this.sourceParams = params;\n    this.refreshProps();\n  }\n  getOptionalMethods() {\n    return [\"afterGuiAttached\", \"setInputPlaceholder\", \"setInputAriaLabel\", \"setDisabled\"];\n  }\n  updateDate(date) {\n    this.setDate(date);\n    this.sourceParams.onDateChanged();\n  }\n  getProps() {\n    const props = super.getProps();\n    props.date = this.date;\n    props.onDateChange = this.onDateChange;\n    delete props.onDateChanged;\n    return props;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/dragAndDropImageComponentWrapper.ts\nvar DragAndDropImageComponentWrapper = class extends CustomComponentWrapper {\n  constructor() {\n    super(...arguments);\n    this.label = \"\";\n    this.icon = null;\n    this.shake = false;\n  }\n  setIcon(iconName, shake) {\n    this.icon = iconName;\n    this.shake = shake;\n    this.refreshProps();\n  }\n  setLabel(label) {\n    this.label = label;\n    this.refreshProps();\n  }\n  getProps() {\n    const props = super.getProps();\n    const { label, icon, shake } = this;\n    props.label = label;\n    props.icon = icon;\n    props.shake = shake;\n    return props;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/filterComponentWrapper.ts\n\nvar FilterComponentWrapper = class extends CustomComponentWrapper {\n  constructor() {\n    super(...arguments);\n    this.model = null;\n    this.onModelChange = (model) => this.updateModel(model);\n    this.onUiChange = () => this.sourceParams.filterModifiedCallback();\n    this.expectingNewMethods = true;\n    this.hasBeenActive = false;\n    this.awaitSetMethodsCallback = new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.AgPromise((resolve) => {\n      this.resolveSetMethodsCallback = resolve;\n    });\n  }\n  isFilterActive() {\n    return this.model != null;\n  }\n  doesFilterPass(params) {\n    return this.providedMethods.doesFilterPass(params);\n  }\n  getModel() {\n    return this.model;\n  }\n  setModel(model) {\n    this.expectingNewMethods = true;\n    this.model = model;\n    this.hasBeenActive || (this.hasBeenActive = this.isFilterActive());\n    return this.refreshProps();\n  }\n  refresh(newParams) {\n    this.sourceParams = newParams;\n    this.refreshProps();\n    return true;\n  }\n  afterGuiAttached(params) {\n    const providedMethods = this.providedMethods;\n    if (!providedMethods) {\n      this.awaitSetMethodsCallback.then(() => this.providedMethods?.afterGuiAttached?.(params));\n    } else {\n      providedMethods.afterGuiAttached?.(params);\n    }\n  }\n  getOptionalMethods() {\n    return [\"afterGuiAttached\", \"afterGuiDetached\", \"onNewRowsLoaded\", \"getModelAsString\", \"onAnyFilterChanged\"];\n  }\n  setMethods(methods) {\n    if (this.expectingNewMethods === false && this.hasBeenActive && this.providedMethods?.doesFilterPass !== methods?.doesFilterPass) {\n      setTimeout(() => {\n        this.sourceParams.filterChangedCallback();\n      });\n    }\n    this.expectingNewMethods = false;\n    super.setMethods(methods);\n    this.resolveSetMethodsCallback();\n  }\n  updateModel(model) {\n    this.setModel(model).then(() => this.sourceParams.filterChangedCallback());\n  }\n  getProps() {\n    const props = super.getProps();\n    props.model = this.model;\n    props.onModelChange = this.onModelChange;\n    props.onUiChange = this.onUiChange;\n    delete props.filterChangedCallback;\n    delete props.filterModifiedCallback;\n    return props;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/floatingFilterComponentProxy.ts\n\nfunction updateFloatingFilterParent(params, model) {\n  params.parentFilterInstance((instance) => {\n    (instance.setModel(model) || ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.AgPromise.resolve()).then(() => {\n      params.filterParams.filterChangedCallback();\n    });\n  });\n}\nvar FloatingFilterComponentProxy = class {\n  constructor(floatingFilterParams, refreshProps) {\n    this.floatingFilterParams = floatingFilterParams;\n    this.refreshProps = refreshProps;\n    this.model = null;\n    this.onModelChange = (model) => this.updateModel(model);\n  }\n  getProps() {\n    return {\n      ...this.floatingFilterParams,\n      model: this.model,\n      onModelChange: this.onModelChange\n    };\n  }\n  onParentModelChanged(parentModel) {\n    this.model = parentModel;\n    this.refreshProps();\n  }\n  refresh(params) {\n    this.floatingFilterParams = params;\n    this.refreshProps();\n  }\n  setMethods(methods) {\n    addOptionalMethods(this.getOptionalMethods(), methods, this);\n  }\n  getOptionalMethods() {\n    return [\"afterGuiAttached\"];\n  }\n  updateModel(model) {\n    this.model = model;\n    this.refreshProps();\n    updateFloatingFilterParent(this.floatingFilterParams, model);\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/floatingFilterComponentWrapper.ts\nvar FloatingFilterComponentWrapper = class extends CustomComponentWrapper {\n  constructor() {\n    super(...arguments);\n    this.model = null;\n    this.onModelChange = (model) => this.updateModel(model);\n  }\n  onParentModelChanged(parentModel) {\n    this.model = parentModel;\n    this.refreshProps();\n  }\n  refresh(newParams) {\n    this.sourceParams = newParams;\n    this.refreshProps();\n  }\n  getOptionalMethods() {\n    return [\"afterGuiAttached\"];\n  }\n  updateModel(model) {\n    this.model = model;\n    this.refreshProps();\n    updateFloatingFilterParent(this.sourceParams, model);\n  }\n  getProps() {\n    const props = super.getProps();\n    props.model = this.model;\n    props.onModelChange = this.onModelChange;\n    return props;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/innerHeaderComponentWrapper.ts\nvar InnerHeaderComponentWrapper = class extends CustomComponentWrapper {\n  refresh(params) {\n    this.sourceParams = params;\n    this.refreshProps();\n    return true;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/loadingOverlayComponentWrapper.ts\nvar LoadingOverlayComponentWrapper = class extends CustomComponentWrapper {\n  refresh(params) {\n    this.sourceParams = params;\n    this.refreshProps();\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/menuItemComponentWrapper.ts\nvar MenuItemComponentWrapper = class extends CustomComponentWrapper {\n  constructor() {\n    super(...arguments);\n    this.active = false;\n    this.expanded = false;\n    this.onActiveChange = (active) => this.updateActive(active);\n  }\n  setActive(active) {\n    this.awaitSetActive(active);\n  }\n  setExpanded(expanded) {\n    this.expanded = expanded;\n    this.refreshProps();\n  }\n  getOptionalMethods() {\n    return [\"select\", \"configureDefaults\"];\n  }\n  awaitSetActive(active) {\n    this.active = active;\n    return this.refreshProps();\n  }\n  updateActive(active) {\n    const result = this.awaitSetActive(active);\n    if (active) {\n      result.then(() => this.sourceParams.onItemActivated());\n    }\n  }\n  getProps() {\n    const props = super.getProps();\n    props.active = this.active;\n    props.expanded = this.expanded;\n    props.onActiveChange = this.onActiveChange;\n    delete props.onItemActivated;\n    return props;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/noRowsOverlayComponentWrapper.ts\nvar NoRowsOverlayComponentWrapper = class extends CustomComponentWrapper {\n  refresh(params) {\n    this.sourceParams = params;\n    this.refreshProps();\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/statusPanelComponentWrapper.ts\nvar StatusPanelComponentWrapper = class extends CustomComponentWrapper {\n  refresh(params) {\n    this.sourceParams = params;\n    this.refreshProps();\n    return true;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/toolPanelComponentWrapper.ts\nvar ToolPanelComponentWrapper = class extends CustomComponentWrapper {\n  constructor() {\n    super(...arguments);\n    this.onStateChange = (state) => this.updateState(state);\n  }\n  refresh(params) {\n    this.sourceParams = params;\n    this.refreshProps();\n    return true;\n  }\n  getState() {\n    return this.state;\n  }\n  updateState(state) {\n    this.state = state;\n    this.refreshProps();\n    this.sourceParams.onStateUpdated();\n  }\n  getProps() {\n    const props = super.getProps();\n    props.state = this.state;\n    props.onStateChange = this.onStateChange;\n    return props;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/util.ts\n\nfunction getInstance(wrapperComponent, callback) {\n  const promise = wrapperComponent?.getInstance?.() ?? ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.AgPromise.resolve(void 0);\n  promise.then((comp) => callback(comp));\n}\nfunction warnReactiveCustomComponents() {\n  (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._warn)(231);\n}\n\n// packages/ag-grid-react/src/shared/portalManager.ts\nvar MAX_COMPONENT_CREATION_TIME_IN_MS = 1e3;\nvar PortalManager = class {\n  constructor(refresher, wrappingElement, maxComponentCreationTimeMs) {\n    this.destroyed = false;\n    this.portals = [];\n    this.hasPendingPortalUpdate = false;\n    this.wrappingElement = wrappingElement ? wrappingElement : \"div\";\n    this.refresher = refresher;\n    this.maxComponentCreationTimeMs = maxComponentCreationTimeMs ? maxComponentCreationTimeMs : MAX_COMPONENT_CREATION_TIME_IN_MS;\n  }\n  getPortals() {\n    return this.portals;\n  }\n  destroy() {\n    this.destroyed = true;\n  }\n  destroyPortal(portal) {\n    this.portals = this.portals.filter((curPortal) => curPortal !== portal);\n    this.batchUpdate();\n  }\n  getComponentWrappingElement() {\n    return this.wrappingElement;\n  }\n  mountReactPortal(portal, reactComponent, resolve) {\n    this.portals = [...this.portals, portal];\n    this.waitForInstance(reactComponent, resolve);\n    this.batchUpdate();\n  }\n  updateReactPortal(oldPortal, newPortal) {\n    this.portals[this.portals.indexOf(oldPortal)] = newPortal;\n    this.batchUpdate();\n  }\n  batchUpdate() {\n    if (this.hasPendingPortalUpdate) {\n      return;\n    }\n    setTimeout(() => {\n      if (!this.destroyed) {\n        this.refresher();\n        this.hasPendingPortalUpdate = false;\n      }\n    });\n    this.hasPendingPortalUpdate = true;\n  }\n  waitForInstance(reactComponent, resolve, startTime = Date.now()) {\n    if (this.destroyed) {\n      resolve(null);\n      return;\n    }\n    if (reactComponent.rendered()) {\n      resolve(reactComponent);\n    } else {\n      if (Date.now() - startTime >= this.maxComponentCreationTimeMs && !this.hasPendingPortalUpdate) {\n        return;\n      }\n      window.setTimeout(() => {\n        this.waitForInstance(reactComponent, resolve, startTime);\n      });\n    }\n  }\n};\n\n// packages/ag-grid-react/src/reactUi/gridComp.tsx\n\n\n\n// packages/ag-grid-react/src/reactUi/gridBodyComp.tsx\n\n\n\n// packages/ag-grid-react/src/reactUi/header/gridHeaderComp.tsx\n\n\n\n// packages/ag-grid-react/src/reactUi/header/headerRowContainerComp.tsx\n\n\n\n// packages/ag-grid-react/src/reactUi/header/headerRowComp.tsx\n\n\n\n// packages/ag-grid-react/src/reactUi/header/headerCellComp.tsx\n\n\nvar HeaderCellComp = ({ ctrl }) => {\n  const isAlive = ctrl.isAlive();\n  const { context } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const colId = isAlive ? ctrl.column.getColId() : void 0;\n  const [userCompDetails, setUserCompDetails] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [userStyles, setUserStyles] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const compBean = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eResize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eHeaderCompWrapper = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const userCompRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const cssClassManager = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  if (isAlive && !cssClassManager.current) {\n    cssClassManager.current = new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.CssClassManager(() => eGui.current);\n  }\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGui.current = eRef;\n    compBean.current = eRef ? context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._EmptyBean()) : context.destroyBean(compBean.current);\n    if (!eRef || !ctrl.isAlive()) {\n      return;\n    }\n    const compProxy = {\n      setWidth: (width) => {\n        if (eGui.current) {\n          eGui.current.style.width = width;\n        }\n      },\n      addOrRemoveCssClass: (name, on) => cssClassManager.current.addOrRemoveCssClass(name, on),\n      setUserStyles: (styles) => setUserStyles(styles),\n      setAriaSort: (sort) => {\n        if (eGui.current) {\n          sort ? (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._setAriaSort)(eGui.current, sort) : (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._removeAriaSort)(eGui.current);\n        }\n      },\n      setUserCompDetails: (compDetails) => setUserCompDetails(compDetails),\n      getUserCompInstance: () => userCompRef.current || void 0\n    };\n    ctrl.setComp(compProxy, eRef, eResize.current, eHeaderCompWrapper.current, compBean.current);\n    const selectAllGui = ctrl.getSelectAllGui();\n    if (selectAllGui) {\n      eResize.current?.insertAdjacentElement(\"afterend\", selectAllGui);\n      compBean.current.addDestroyFunc(() => selectAllGui.remove());\n    }\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(\n    () => showJsComp(userCompDetails, context, eHeaderCompWrapper.current, userCompRef),\n    [userCompDetails]\n  );\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    ctrl.setDragSource(eGui.current);\n  }, [userCompDetails]);\n  const userCompStateless = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const res = userCompDetails?.componentFromFramework && isComponentStateless(userCompDetails.componentClass);\n    return !!res;\n  }, [userCompDetails]);\n  const reactUserComp = userCompDetails && userCompDetails.componentFromFramework;\n  const UserCompClass = userCompDetails && userCompDetails.componentClass;\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setRef2, style: userStyles, className: \"ag-header-cell\", \"col-id\": colId, role: \"columnheader\" }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: eResize, className: \"ag-header-cell-resize\", role: \"presentation\" }), /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: eHeaderCompWrapper, className: \"ag-header-cell-comp-wrapper\", role: \"presentation\" }, reactUserComp && userCompStateless && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(UserCompClass, { ...userCompDetails.params }), reactUserComp && !userCompStateless && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(UserCompClass, { ...userCompDetails.params, ref: userCompRef })));\n};\nvar headerCellComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(HeaderCellComp);\n\n// packages/ag-grid-react/src/reactUi/header/headerFilterCellComp.tsx\n\n\nvar HeaderFilterCellComp = ({ ctrl }) => {\n  const { context, gos } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const [userStyles, setUserStyles] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [cssClasses, setCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\n    () => new CssClasses(\"ag-header-cell\", \"ag-floating-filter\")\n  );\n  const [cssBodyClasses, setBodyCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses());\n  const [cssButtonWrapperClasses, setButtonWrapperCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\n    () => new CssClasses(\"ag-floating-filter-button\", \"ag-hidden\")\n  );\n  const [buttonWrapperAriaHidden, setButtonWrapperAriaHidden] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"false\");\n  const [userCompDetails, setUserCompDetails] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [, setRenderKey] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(1);\n  const compBean = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eFloatingFilterBody = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eButtonWrapper = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eButtonShowMainFilter = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const userCompResolve = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const userCompPromise = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const userCompRef = (value) => {\n    if (value == null) {\n      return;\n    }\n    userCompResolve.current && userCompResolve.current(value);\n  };\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGui.current = eRef;\n    compBean.current = eRef ? context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._EmptyBean()) : context.destroyBean(compBean.current);\n    if (!eRef) {\n      return;\n    }\n    userCompPromise.current = new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.AgPromise((resolve) => {\n      userCompResolve.current = resolve;\n    });\n    const compProxy = {\n      addOrRemoveCssClass: (name, on) => setCssClasses((prev) => prev.setClass(name, on)),\n      setUserStyles: (styles) => setUserStyles(styles),\n      addOrRemoveBodyCssClass: (name, on) => setBodyCssClasses((prev) => prev.setClass(name, on)),\n      setButtonWrapperDisplayed: (displayed) => {\n        setButtonWrapperCssClasses((prev) => prev.setClass(\"ag-hidden\", !displayed));\n        setButtonWrapperAriaHidden(!displayed ? \"true\" : \"false\");\n      },\n      setWidth: (width) => {\n        if (eGui.current) {\n          eGui.current.style.width = width;\n        }\n      },\n      setCompDetails: (compDetails) => setUserCompDetails(compDetails),\n      getFloatingFilterComp: () => userCompPromise.current ? userCompPromise.current : null,\n      setMenuIcon: (eIcon) => eButtonShowMainFilter.current?.appendChild(eIcon)\n    };\n    ctrl.setComp(compProxy, eRef, eButtonShowMainFilter.current, eFloatingFilterBody.current, compBean.current);\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(\n    () => showJsComp(userCompDetails, context, eFloatingFilterBody.current, userCompRef),\n    [userCompDetails]\n  );\n  const className = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => cssClasses.toString(), [cssClasses]);\n  const bodyClassName = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => cssBodyClasses.toString(), [cssBodyClasses]);\n  const buttonWrapperClassName = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => cssButtonWrapperClasses.toString(), [cssButtonWrapperClasses]);\n  const userCompStateless = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const res = userCompDetails && userCompDetails.componentFromFramework && isComponentStateless(userCompDetails.componentClass);\n    return !!res;\n  }, [userCompDetails]);\n  const reactiveCustomComponents = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => gos.get(\"reactiveCustomComponents\"), []);\n  const floatingFilterCompProxy = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (userCompDetails) {\n      if (reactiveCustomComponents) {\n        const compProxy = new FloatingFilterComponentProxy(\n          userCompDetails.params,\n          () => setRenderKey((prev) => prev + 1)\n        );\n        userCompRef(compProxy);\n        return compProxy;\n      } else if (userCompDetails.componentFromFramework) {\n        warnReactiveCustomComponents();\n      }\n    }\n    return void 0;\n  }, [userCompDetails]);\n  const floatingFilterProps = floatingFilterCompProxy?.getProps();\n  const reactUserComp = userCompDetails && userCompDetails.componentFromFramework;\n  const UserCompClass = userCompDetails && userCompDetails.componentClass;\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setRef2, style: userStyles, className, role: \"gridcell\" }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: eFloatingFilterBody, className: bodyClassName, role: \"presentation\" }, reactUserComp && !reactiveCustomComponents && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(UserCompClass, { ...userCompDetails.params, ref: userCompStateless ? () => {\n  } : userCompRef }), reactUserComp && reactiveCustomComponents && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    CustomContext.Provider,\n    {\n      value: {\n        setMethods: (methods) => floatingFilterCompProxy.setMethods(methods)\n      }\n    },\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(UserCompClass, { ...floatingFilterProps })\n  )), /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    \"div\",\n    {\n      ref: eButtonWrapper,\n      \"aria-hidden\": buttonWrapperAriaHidden,\n      className: buttonWrapperClassName,\n      role: \"presentation\"\n    },\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n      \"button\",\n      {\n        ref: eButtonShowMainFilter,\n        type: \"button\",\n        className: \"ag-button ag-floating-filter-button-button\",\n        tabIndex: -1\n      }\n    )\n  ));\n};\nvar headerFilterCellComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(HeaderFilterCellComp);\n\n// packages/ag-grid-react/src/reactUi/header/headerGroupCellComp.tsx\n\n\nvar HeaderGroupCellComp = ({ ctrl }) => {\n  const { context } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const [userStyles, setUserStyles] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [cssClasses, setCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses());\n  const [cssResizableClasses, setResizableCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses());\n  const [resizableAriaHidden, setResizableAriaHidden] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"false\");\n  const [ariaExpanded, setAriaExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [userCompDetails, setUserCompDetails] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const colId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ctrl.column.getUniqueId(), []);\n  const compBean = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eResize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eHeaderCompWrapper = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const userCompRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGui.current = eRef;\n    compBean.current = eRef ? context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._EmptyBean()) : context.destroyBean(compBean.current);\n    if (!eRef) {\n      return;\n    }\n    const compProxy = {\n      setWidth: (width) => {\n        if (eGui.current) {\n          eGui.current.style.width = width;\n        }\n      },\n      addOrRemoveCssClass: (name, on) => setCssClasses((prev) => prev.setClass(name, on)),\n      setUserStyles: (styles) => setUserStyles(styles),\n      setHeaderWrapperHidden: (hidden) => {\n        const headerCompWrapper = eHeaderCompWrapper.current;\n        if (!headerCompWrapper) {\n          return;\n        }\n        if (hidden) {\n          headerCompWrapper.style.setProperty(\"display\", \"none\");\n        } else {\n          headerCompWrapper.style.removeProperty(\"display\");\n        }\n      },\n      setHeaderWrapperMaxHeight: (value) => {\n        const headerCompWrapper = eHeaderCompWrapper.current;\n        if (!headerCompWrapper) {\n          return;\n        }\n        if (value != null) {\n          headerCompWrapper.style.setProperty(\"max-height\", `${value}px`);\n        } else {\n          headerCompWrapper.style.removeProperty(\"max-height\");\n        }\n        headerCompWrapper.classList.toggle(\"ag-header-cell-comp-wrapper-limited-height\", value != null);\n      },\n      setUserCompDetails: (compDetails) => setUserCompDetails(compDetails),\n      setResizableDisplayed: (displayed) => {\n        setResizableCssClasses((prev) => prev.setClass(\"ag-hidden\", !displayed));\n        setResizableAriaHidden(!displayed ? \"true\" : \"false\");\n      },\n      setAriaExpanded: (expanded) => setAriaExpanded(expanded),\n      getUserCompInstance: () => userCompRef.current || void 0\n    };\n    ctrl.setComp(compProxy, eRef, eResize.current, eHeaderCompWrapper.current, compBean.current);\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => showJsComp(userCompDetails, context, eHeaderCompWrapper.current), [userCompDetails]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (eGui.current) {\n      ctrl.setDragSource(eGui.current);\n    }\n  }, [userCompDetails]);\n  const userCompStateless = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const res = userCompDetails?.componentFromFramework && isComponentStateless(userCompDetails.componentClass);\n    return !!res;\n  }, [userCompDetails]);\n  const className = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => \"ag-header-group-cell \" + cssClasses.toString(), [cssClasses]);\n  const resizableClassName = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => \"ag-header-cell-resize \" + cssResizableClasses.toString(),\n    [cssResizableClasses]\n  );\n  const reactUserComp = userCompDetails && userCompDetails.componentFromFramework;\n  const UserCompClass = userCompDetails && userCompDetails.componentClass;\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    \"div\",\n    {\n      ref: setRef2,\n      style: userStyles,\n      className,\n      \"col-id\": colId,\n      role: \"columnheader\",\n      \"aria-expanded\": ariaExpanded\n    },\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: eHeaderCompWrapper, className: \"ag-header-cell-comp-wrapper\", role: \"presentation\" }, reactUserComp && userCompStateless && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(UserCompClass, { ...userCompDetails.params }), reactUserComp && !userCompStateless && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(UserCompClass, { ...userCompDetails.params, ref: userCompRef })),\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: eResize, \"aria-hidden\": resizableAriaHidden, className: resizableClassName })\n  );\n};\nvar headerGroupCellComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(HeaderGroupCellComp);\n\n// packages/ag-grid-react/src/reactUi/header/headerRowComp.tsx\nvar HeaderRowComp = ({ ctrl }) => {\n  const { context } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const { topOffset, rowHeight } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ctrl.getTopAndHeight(), []);\n  const ariaRowIndex = ctrl.getAriaRowIndex();\n  const className = ctrl.headerRowClass;\n  const [height, setHeight] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => rowHeight + \"px\");\n  const [top, setTop] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => topOffset + \"px\");\n  const cellCtrlsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const prevCellCtrlsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const [cellCtrls, setCellCtrls] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => ctrl.getUpdatedHeaderCtrls());\n  const compBean = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGui.current = eRef;\n    compBean.current = eRef ? context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._EmptyBean()) : context.destroyBean(compBean.current);\n    if (!eRef) {\n      return;\n    }\n    const compProxy = {\n      setHeight: (height2) => setHeight(height2),\n      setTop: (top2) => setTop(top2),\n      setHeaderCtrls: (ctrls, forceOrder, afterScroll) => {\n        prevCellCtrlsRef.current = cellCtrlsRef.current;\n        cellCtrlsRef.current = ctrls;\n        const next = getNextValueIfDifferent(prevCellCtrlsRef.current, ctrls, forceOrder);\n        if (next !== prevCellCtrlsRef.current) {\n          agFlushSync(afterScroll, () => setCellCtrls(next));\n        }\n      },\n      setWidth: (width) => {\n        if (eGui.current) {\n          eGui.current.style.width = width;\n        }\n      }\n    };\n    ctrl.setComp(compProxy, compBean.current, false);\n  }, []);\n  const style = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => ({\n      height,\n      top\n    }),\n    [height, top]\n  );\n  const createCellJsx = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((cellCtrl) => {\n    switch (ctrl.type) {\n      case \"group\":\n        return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(headerGroupCellComp_default, { ctrl: cellCtrl, key: cellCtrl.instanceId });\n      case \"filter\":\n        return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(headerFilterCellComp_default, { ctrl: cellCtrl, key: cellCtrl.instanceId });\n      default:\n        return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(headerCellComp_default, { ctrl: cellCtrl, key: cellCtrl.instanceId });\n    }\n  }, []);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setRef2, className, role: \"row\", style, \"aria-rowindex\": ariaRowIndex }, cellCtrls.map(createCellJsx));\n};\nvar headerRowComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(HeaderRowComp);\n\n// packages/ag-grid-react/src/reactUi/header/headerRowContainerComp.tsx\nvar HeaderRowContainerComp = ({ pinned }) => {\n  const [displayed, setDisplayed] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n  const [headerRowCtrls, setHeaderRowCtrls] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const { context } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eCenterContainer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const headerRowCtrlRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const pinnedLeft = pinned === \"left\";\n  const pinnedRight = pinned === \"right\";\n  const centre = !pinnedLeft && !pinnedRight;\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGui.current = eRef;\n    headerRowCtrlRef.current = eRef ? context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.HeaderRowContainerCtrl(pinned)) : context.destroyBean(headerRowCtrlRef.current);\n    if (!eRef) {\n      return;\n    }\n    const compProxy = {\n      setDisplayed,\n      setCtrls: (ctrls) => setHeaderRowCtrls(ctrls),\n      // centre only\n      setCenterWidth: (width) => {\n        if (eCenterContainer.current) {\n          eCenterContainer.current.style.width = width;\n        }\n      },\n      setViewportScrollLeft: (left) => {\n        if (eGui.current) {\n          eGui.current.scrollLeft = left;\n        }\n      },\n      // pinned only\n      setPinnedContainerWidth: (width) => {\n        if (eGui.current) {\n          eGui.current.style.width = width;\n          eGui.current.style.minWidth = width;\n          eGui.current.style.maxWidth = width;\n        }\n      }\n    };\n    headerRowCtrlRef.current.setComp(compProxy, eGui.current);\n  }, []);\n  const className = !displayed ? \"ag-hidden\" : \"\";\n  const insertRowsJsx = () => headerRowCtrls.map((ctrl) => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(headerRowComp_default, { ctrl, key: ctrl.instanceId }));\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, pinnedLeft && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    \"div\",\n    {\n      ref: setRef2,\n      className: \"ag-pinned-left-header \" + className,\n      \"aria-hidden\": !displayed,\n      role: \"rowgroup\"\n    },\n    insertRowsJsx()\n  ), pinnedRight && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    \"div\",\n    {\n      ref: setRef2,\n      className: \"ag-pinned-right-header \" + className,\n      \"aria-hidden\": !displayed,\n      role: \"rowgroup\"\n    },\n    insertRowsJsx()\n  ), centre && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setRef2, className: \"ag-header-viewport \" + className, role: \"presentation\", tabIndex: -1 }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: eCenterContainer, className: \"ag-header-container\", role: \"rowgroup\" }, insertRowsJsx())));\n};\nvar headerRowContainerComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(HeaderRowContainerComp);\n\n// packages/ag-grid-react/src/reactUi/header/gridHeaderComp.tsx\nvar GridHeaderComp = () => {\n  const [cssClasses, setCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses());\n  const [height, setHeight] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const { context } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const gridCtrlRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGui.current = eRef;\n    gridCtrlRef.current = eRef ? context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.GridHeaderCtrl()) : context.destroyBean(gridCtrlRef.current);\n    if (!eRef)\n      return;\n    const compProxy = {\n      addOrRemoveCssClass: (name, on) => setCssClasses((prev) => prev.setClass(name, on)),\n      setHeightAndMinHeight: (height2) => setHeight(height2)\n    };\n    gridCtrlRef.current.setComp(compProxy, eRef, eRef);\n  }, []);\n  const className = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const res = cssClasses.toString();\n    return \"ag-header \" + res;\n  }, [cssClasses]);\n  const style = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => ({\n      height,\n      minHeight: height\n    }),\n    [height]\n  );\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setRef2, className, style, role: \"presentation\" }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(headerRowContainerComp_default, { pinned: \"left\" }), /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(headerRowContainerComp_default, { pinned: null }), /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(headerRowContainerComp_default, { pinned: \"right\" }));\n};\nvar gridHeaderComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(GridHeaderComp);\n\n// packages/ag-grid-react/src/reactUi/reactComment.tsx\n\nvar useReactCommentEffect = (comment, eForCommentRef) => {\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const eForComment = eForCommentRef.current;\n    if (eForComment) {\n      const eParent = eForComment.parentElement;\n      if (eParent) {\n        const eComment = document.createComment(comment);\n        eParent.insertBefore(eComment, eForComment);\n        return () => {\n          eParent.removeChild(eComment);\n        };\n      }\n    }\n  }, [comment]);\n};\nvar reactComment_default = useReactCommentEffect;\n\n// packages/ag-grid-react/src/reactUi/rows/rowContainerComp.tsx\n\n\n\n// packages/ag-grid-react/src/reactUi/rows/rowComp.tsx\n\n\n\n// packages/ag-grid-react/src/reactUi/cells/cellComp.tsx\n\n\n\n// packages/ag-grid-react/src/shared/customComp/cellEditorComponentProxy.ts\n\nvar CellEditorComponentProxy = class {\n  constructor(cellEditorParams, refreshProps) {\n    this.cellEditorParams = cellEditorParams;\n    this.refreshProps = refreshProps;\n    this.instanceCreated = new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.AgPromise((resolve) => {\n      this.resolveInstanceCreated = resolve;\n    });\n    this.onValueChange = (value) => this.updateValue(value);\n    this.value = cellEditorParams.value;\n  }\n  getProps() {\n    return {\n      ...this.cellEditorParams,\n      initialValue: this.cellEditorParams.value,\n      value: this.value,\n      onValueChange: this.onValueChange\n    };\n  }\n  getValue() {\n    return this.value;\n  }\n  refresh(params) {\n    this.cellEditorParams = params;\n    this.refreshProps();\n  }\n  setMethods(methods) {\n    addOptionalMethods(this.getOptionalMethods(), methods, this);\n  }\n  getInstance() {\n    return this.instanceCreated.then(() => this.componentInstance);\n  }\n  setRef(componentInstance) {\n    this.componentInstance = componentInstance;\n    this.resolveInstanceCreated?.();\n    this.resolveInstanceCreated = void 0;\n  }\n  getOptionalMethods() {\n    return [\"isCancelBeforeStart\", \"isCancelAfterEnd\", \"focusIn\", \"focusOut\", \"afterGuiAttached\"];\n  }\n  updateValue(value) {\n    this.value = value;\n    this.refreshProps();\n  }\n};\n\n// packages/ag-grid-react/src/reactUi/cells/popupEditorComp.tsx\n\n\n\n\n// packages/ag-grid-react/src/reactUi/useEffectOnce.tsx\n\nvar useEffectOnce = (effect) => {\n  const effectFn = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(effect);\n  const destroyFn = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const effectCalled = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  const rendered = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  const [, setVal] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  if (effectCalled.current) {\n    rendered.current = true;\n  }\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!effectCalled.current) {\n      destroyFn.current = effectFn.current();\n      effectCalled.current = true;\n    }\n    setVal((val) => val + 1);\n    return () => {\n      if (!rendered.current) {\n        return;\n      }\n      destroyFn.current?.();\n    };\n  }, []);\n};\n\n// packages/ag-grid-react/src/reactUi/cells/popupEditorComp.tsx\nvar PopupEditorComp = (props) => {\n  const [popupEditorWrapper, setPopupEditorWrapper] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const beans = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const { context, popupSvc, localeSvc, gos, editSvc } = beans;\n  const { editDetails, cellCtrl, eParentCell } = props;\n  useEffectOnce(() => {\n    const { compDetails } = editDetails;\n    const useModelPopup = gos.get(\"stopEditingWhenCellsLoseFocus\");\n    const wrapper = context.createBean(editSvc.createPopupEditorWrapper(compDetails.params));\n    const ePopupGui = wrapper.getGui();\n    if (props.jsChildComp) {\n      const eChildGui = props.jsChildComp.getGui();\n      if (eChildGui) {\n        ePopupGui.appendChild(eChildGui);\n      }\n    }\n    const { column, rowNode } = cellCtrl;\n    const positionParams = {\n      column,\n      rowNode,\n      type: \"popupCellEditor\",\n      eventSource: eParentCell,\n      ePopup: ePopupGui,\n      position: editDetails.popupPosition,\n      keepWithinBounds: true\n    };\n    const positionCallback = popupSvc?.positionPopupByComponent.bind(popupSvc, positionParams);\n    const translate = (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._getLocaleTextFunc)(localeSvc);\n    const addPopupRes = popupSvc?.addPopup({\n      modal: useModelPopup,\n      eChild: ePopupGui,\n      closeOnEsc: true,\n      closedCallback: () => {\n        cellCtrl.onPopupEditorClosed();\n      },\n      anchorToElement: eParentCell,\n      positionCallback,\n      ariaLabel: translate(\"ariaLabelCellEditor\", \"Cell Editor\")\n    });\n    const hideEditorPopup = addPopupRes ? addPopupRes.hideFunc : void 0;\n    setPopupEditorWrapper(wrapper);\n    props.jsChildComp?.afterGuiAttached?.();\n    return () => {\n      hideEditorPopup?.();\n      context.destroyBean(wrapper);\n    };\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => {\n    return () => {\n      if (cellCtrl.isCellFocused() && popupEditorWrapper?.getGui().contains((0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._getActiveDomElement)(beans))) {\n        eParentCell.focus({ preventScroll: true });\n      }\n    };\n  }, [popupEditorWrapper]);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, popupEditorWrapper && props.wrappedContent && (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)(props.wrappedContent, popupEditorWrapper.getGui()));\n};\nvar popupEditorComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(PopupEditorComp);\n\n// packages/ag-grid-react/src/reactUi/cells/showJsRenderer.tsx\n\nvar useJsCellRenderer = (showDetails, showTools, eCellValue, cellValueVersion, jsCellRendererRef, eGui) => {\n  const { context } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const destroyCellRenderer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    const comp = jsCellRendererRef.current;\n    if (!comp) {\n      return;\n    }\n    const compGui = comp.getGui();\n    if (compGui && compGui.parentElement) {\n      compGui.parentElement.removeChild(compGui);\n    }\n    context.destroyBean(comp);\n    jsCellRendererRef.current = void 0;\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const showValue = showDetails != null;\n    const jsCompDetails = showDetails?.compDetails && !showDetails.compDetails.componentFromFramework;\n    const waitingForToolsSetup = showTools && eCellValue == null;\n    const showComp = showValue && jsCompDetails && !waitingForToolsSetup;\n    if (!showComp) {\n      destroyCellRenderer();\n      return;\n    }\n    const compDetails = showDetails.compDetails;\n    if (jsCellRendererRef.current) {\n      const comp = jsCellRendererRef.current;\n      const attemptRefresh = comp.refresh != null && showDetails.force == false;\n      const refreshResult = attemptRefresh ? comp.refresh(compDetails.params) : false;\n      const refreshWorked = refreshResult === true || refreshResult === void 0;\n      if (refreshWorked) {\n        return;\n      }\n      destroyCellRenderer();\n    }\n    const promise = compDetails.newAgStackInstance();\n    promise.then((comp) => {\n      if (!comp) {\n        return;\n      }\n      const compGui = comp.getGui();\n      if (!compGui) {\n        return;\n      }\n      const parent = showTools ? eCellValue : eGui.current;\n      parent.appendChild(compGui);\n      jsCellRendererRef.current = comp;\n    });\n  }, [showDetails, showTools, cellValueVersion]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    return destroyCellRenderer;\n  }, []);\n};\nvar showJsRenderer_default = useJsCellRenderer;\n\n// packages/ag-grid-react/src/reactUi/cells/cellComp.tsx\nvar jsxEditorProxy = (editDetails, CellEditorClass, setRef2) => {\n  const { compProxy } = editDetails;\n  setRef2(compProxy);\n  const props = compProxy.getProps();\n  const isStateless = isComponentStateless(CellEditorClass);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    CustomContext.Provider,\n    {\n      value: {\n        setMethods: (methods) => compProxy.setMethods(methods)\n      }\n    },\n    isStateless ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CellEditorClass, { ...props }) : /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CellEditorClass, { ...props, ref: (ref) => compProxy.setRef(ref) })\n  );\n};\nvar jsxEditor = (editDetails, CellEditorClass, setRef2) => {\n  const newFormat = editDetails.compProxy;\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, newFormat ? jsxEditorProxy(editDetails, CellEditorClass, setRef2) : /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CellEditorClass, { ...editDetails.compDetails.params, ref: setRef2 }));\n};\nvar jsxEditValue = (editDetails, setCellEditorRef, eGui, cellCtrl, jsEditorComp) => {\n  const compDetails = editDetails.compDetails;\n  const CellEditorClass = compDetails.componentClass;\n  const reactInlineEditor = compDetails.componentFromFramework && !editDetails.popup;\n  const reactPopupEditor = compDetails.componentFromFramework && editDetails.popup;\n  const jsPopupEditor = !compDetails.componentFromFramework && editDetails.popup;\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, reactInlineEditor && jsxEditor(editDetails, CellEditorClass, setCellEditorRef), reactPopupEditor && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    popupEditorComp_default,\n    {\n      editDetails,\n      cellCtrl,\n      eParentCell: eGui,\n      wrappedContent: jsxEditor(editDetails, CellEditorClass, setCellEditorRef)\n    }\n  ), jsPopupEditor && jsEditorComp && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    popupEditorComp_default,\n    {\n      editDetails,\n      cellCtrl,\n      eParentCell: eGui,\n      jsChildComp: jsEditorComp\n    }\n  ));\n};\nvar jsxShowValue = (showDetails, key, parentId, cellRendererRef, showCellWrapper, reactCellRendererStateless, setECellValue) => {\n  const { compDetails, value } = showDetails;\n  const noCellRenderer = !compDetails;\n  const reactCellRenderer = compDetails && compDetails.componentFromFramework;\n  const CellRendererClass = compDetails && compDetails.componentClass;\n  const valueForNoCellRenderer = value?.toString ? value.toString() : value;\n  const bodyJsxFunc = () => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, noCellRenderer && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, valueForNoCellRenderer), reactCellRenderer && !reactCellRendererStateless && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CellRendererClass, { ...compDetails.params, key, ref: cellRendererRef }), reactCellRenderer && reactCellRendererStateless && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CellRendererClass, { ...compDetails.params, key }));\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, showCellWrapper ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", { role: \"presentation\", id: `cell-${parentId}`, className: \"ag-cell-value\", ref: setECellValue }, bodyJsxFunc()) : bodyJsxFunc());\n};\nvar CellComp = ({\n  cellCtrl,\n  printLayout,\n  editingRow\n}) => {\n  const beans = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const { context } = beans;\n  const { colIdSanitised, instanceId } = cellCtrl;\n  const compBean = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const [renderDetails, setRenderDetails] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\n    () => cellCtrl.isCellRenderer() ? void 0 : { compDetails: void 0, value: cellCtrl.getValueToDisplay(), force: false }\n  );\n  const [editDetails, setEditDetails] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [renderKey, setRenderKey] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(1);\n  const [userStyles, setUserStyles] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [includeSelection, setIncludeSelection] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const [includeRowDrag, setIncludeRowDrag] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const [includeDndSource, setIncludeDndSource] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const [jsEditorComp, setJsEditorComp] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const forceWrapper = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => cellCtrl.isForceWrapper(), [cellCtrl]);\n  const cellAriaRole = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => cellCtrl.getCellAriaRole(), [cellCtrl]);\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eWrapper = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const cellRendererRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const jsCellRendererRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const cellEditorRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const eCellWrapper = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const cellWrapperDestroyFuncs = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  const eCellValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const [cellValueVersion, setCellValueVersion] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  const setCellValueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((ref) => {\n    eCellValue.current = ref;\n    setCellValueVersion((v) => v + 1);\n  }, []);\n  const showTools = renderDetails != null && (includeSelection || includeDndSource || includeRowDrag);\n  const showCellWrapper = forceWrapper || showTools;\n  const setCellEditorRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (cellEditor) => {\n      cellEditorRef.current = cellEditor;\n      if (cellEditor) {\n        const editingCancelledByUserComp = cellEditor.isCancelBeforeStart && cellEditor.isCancelBeforeStart();\n        setTimeout(() => {\n          if (editingCancelledByUserComp) {\n            cellCtrl.stopEditing(true);\n            cellCtrl.focusCell(true);\n          } else {\n            cellCtrl.cellEditorAttached();\n          }\n        });\n      }\n    },\n    [cellCtrl]\n  );\n  const cssClassManager = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  if (!cssClassManager.current) {\n    cssClassManager.current = new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.CssClassManager(() => eGui.current);\n  }\n  showJsRenderer_default(renderDetails, showCellWrapper, eCellValue.current, cellValueVersion, jsCellRendererRef, eGui);\n  const lastRenderDetails = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => {\n    const oldDetails = lastRenderDetails.current;\n    const newDetails = renderDetails;\n    lastRenderDetails.current = renderDetails;\n    if (oldDetails == null || oldDetails.compDetails == null || newDetails == null || newDetails.compDetails == null) {\n      return;\n    }\n    const oldCompDetails = oldDetails.compDetails;\n    const newCompDetails = newDetails.compDetails;\n    if (oldCompDetails.componentClass != newCompDetails.componentClass) {\n      return;\n    }\n    if (cellRendererRef.current?.refresh == null) {\n      return;\n    }\n    const result = cellRendererRef.current.refresh(newCompDetails.params);\n    if (result != true) {\n      setRenderKey((prev) => prev + 1);\n    }\n  }, [renderDetails]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => {\n    const doingJsEditor = editDetails && !editDetails.compDetails.componentFromFramework;\n    if (!doingJsEditor) {\n      return;\n    }\n    const compDetails = editDetails.compDetails;\n    const isPopup = editDetails.popup === true;\n    const cellEditorPromise = compDetails.newAgStackInstance();\n    cellEditorPromise.then((cellEditor) => {\n      if (!cellEditor) {\n        return;\n      }\n      const compGui = cellEditor.getGui();\n      setCellEditorRef(cellEditor);\n      if (!isPopup) {\n        const parentEl = (forceWrapper ? eCellWrapper : eGui).current;\n        parentEl?.appendChild(compGui);\n        cellEditor.afterGuiAttached && cellEditor.afterGuiAttached();\n      }\n      setJsEditorComp(cellEditor);\n    });\n    return () => {\n      cellEditorPromise.then((cellEditor) => {\n        const compGui = cellEditor.getGui();\n        context.destroyBean(cellEditor);\n        setCellEditorRef(void 0);\n        setJsEditorComp(void 0);\n        compGui?.parentElement?.removeChild(compGui);\n      });\n    };\n  }, [editDetails]);\n  const setCellWrapperRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (eRef) => {\n      eCellWrapper.current = eRef;\n      if (!eRef) {\n        cellWrapperDestroyFuncs.current.forEach((f) => f());\n        cellWrapperDestroyFuncs.current = [];\n        return;\n      }\n      const addComp = (comp) => {\n        if (comp) {\n          const eGui2 = comp.getGui();\n          eRef.insertAdjacentElement(\"afterbegin\", eGui2);\n          cellWrapperDestroyFuncs.current.push(() => {\n            context.destroyBean(comp);\n            (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._removeFromParent)(eGui2);\n          });\n        }\n        return comp;\n      };\n      if (includeSelection) {\n        const checkboxSelectionComp = cellCtrl.createSelectionCheckbox();\n        addComp(checkboxSelectionComp);\n      }\n      if (includeDndSource) {\n        addComp(cellCtrl.createDndSource());\n      }\n      if (includeRowDrag) {\n        addComp(cellCtrl.createRowDragComp());\n      }\n    },\n    [cellCtrl, context, includeDndSource, includeRowDrag, includeSelection]\n  );\n  const init = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    const spanReady = !cellCtrl.isCellSpanning() || eWrapper.current;\n    const eRef = eGui.current;\n    compBean.current = eRef ? context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._EmptyBean()) : context.destroyBean(compBean.current);\n    if (!eRef || !spanReady || !cellCtrl) {\n      return;\n    }\n    const compProxy = {\n      addOrRemoveCssClass: (name, on) => cssClassManager.current.addOrRemoveCssClass(name, on),\n      setUserStyles: (styles) => setUserStyles(styles),\n      getFocusableElement: () => eGui.current,\n      setIncludeSelection: (include) => setIncludeSelection(include),\n      setIncludeRowDrag: (include) => setIncludeRowDrag(include),\n      setIncludeDndSource: (include) => setIncludeDndSource(include),\n      getCellEditor: () => cellEditorRef.current || null,\n      getCellRenderer: () => cellRendererRef.current ?? jsCellRendererRef.current,\n      getParentOfValue: () => eCellValue.current ?? eCellWrapper.current ?? eGui.current,\n      setRenderDetails: (compDetails, value, force) => {\n        setRenderDetails((prev) => {\n          if (prev?.compDetails !== compDetails || prev?.value !== value || prev?.force !== force) {\n            return {\n              value,\n              compDetails,\n              force\n            };\n          } else {\n            return prev;\n          }\n        });\n      },\n      setEditDetails: (compDetails, popup, popupPosition, reactiveCustomComponents) => {\n        if (compDetails) {\n          let compProxy2 = void 0;\n          if (reactiveCustomComponents) {\n            compProxy2 = new CellEditorComponentProxy(\n              compDetails.params,\n              () => setRenderKey((prev) => prev + 1)\n            );\n          } else if (compDetails.componentFromFramework) {\n            warnReactiveCustomComponents();\n          }\n          setEditDetails({\n            compDetails,\n            popup,\n            popupPosition,\n            compProxy: compProxy2\n          });\n          if (!popup) {\n            setRenderDetails(void 0);\n          }\n        } else {\n          const recoverFocus = cellCtrl.hasBrowserFocus();\n          if (recoverFocus) {\n            compProxy.getFocusableElement().focus({ preventScroll: true });\n          }\n          setEditDetails((editDetails2) => {\n            if (editDetails2?.compProxy) {\n              cellEditorRef.current = void 0;\n            }\n            return void 0;\n          });\n        }\n      }\n    };\n    const cellWrapperOrUndefined = eCellWrapper.current || void 0;\n    cellCtrl.setComp(\n      compProxy,\n      eRef,\n      eWrapper.current ?? void 0,\n      cellWrapperOrUndefined,\n      printLayout,\n      editingRow,\n      compBean.current\n    );\n  }, []);\n  const setGuiRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((ref) => {\n    eGui.current = ref;\n    init();\n  }, []);\n  const setWrapperRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((ref) => {\n    eWrapper.current = ref;\n    init();\n  }, []);\n  const reactCellRendererStateless = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const res = renderDetails?.compDetails?.componentFromFramework && isComponentStateless(renderDetails.compDetails.componentClass);\n    return !!res;\n  }, [renderDetails]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => {\n    if (!eGui.current) {\n      return;\n    }\n    cssClassManager.current.addOrRemoveCssClass(\"ag-cell-value\", !showCellWrapper);\n    cssClassManager.current.addOrRemoveCssClass(\"ag-cell-inline-editing\", !!editDetails && !editDetails.popup);\n    cssClassManager.current.addOrRemoveCssClass(\"ag-cell-popup-editing\", !!editDetails && !!editDetails.popup);\n    cssClassManager.current.addOrRemoveCssClass(\"ag-cell-not-inline-editing\", !editDetails || !!editDetails.popup);\n    cellCtrl.setInlineEditingCss();\n  });\n  const showContents = () => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, renderDetails != null && jsxShowValue(\n    renderDetails,\n    renderKey,\n    instanceId,\n    cellRendererRef,\n    showCellWrapper,\n    reactCellRendererStateless,\n    setCellValueRef\n  ), editDetails != null && jsxEditValue(editDetails, setCellEditorRef, eGui.current, cellCtrl, jsEditorComp));\n  const renderCell = () => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setGuiRef, style: userStyles, role: cellAriaRole, \"col-id\": colIdSanitised }, showCellWrapper ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: \"ag-cell-wrapper\", role: \"presentation\", ref: setCellWrapperRef }, showContents()) : showContents());\n  if (cellCtrl.isCellSpanning()) {\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setWrapperRef, className: \"ag-spanned-cell-wrapper\", role: \"presentation\" }, renderCell());\n  }\n  return renderCell();\n};\nvar cellComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(CellComp);\n\n// packages/ag-grid-react/src/reactUi/rows/rowComp.tsx\nvar RowComp = ({ rowCtrl, containerType }) => {\n  const { context, gos } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const compBean = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const domOrderRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(rowCtrl.getDomOrder());\n  const isFullWidth = rowCtrl.isFullWidth();\n  const isDisplayed = rowCtrl.rowNode.displayed;\n  const [rowIndex, setRowIndex] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\n    () => isDisplayed ? rowCtrl.rowNode.getRowIndexString() : null\n  );\n  const [rowId, setRowId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => rowCtrl.rowId);\n  const [rowBusinessKey, setRowBusinessKey] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => rowCtrl.businessKey);\n  const [userStyles, setUserStyles] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => rowCtrl.rowStyles);\n  const cellCtrlsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const prevCellCtrlsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const [cellCtrls, setCellCtrls] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => null);\n  const [fullWidthCompDetails, setFullWidthCompDetails] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [top, setTop] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\n    () => isDisplayed ? rowCtrl.getInitialRowTop(containerType) : void 0\n  );\n  const [transform, setTransform] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\n    () => isDisplayed ? rowCtrl.getInitialTransform(containerType) : void 0\n  );\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const fullWidthCompRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const autoHeightSetup = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  const [autoHeightSetupAttempt, setAutoHeightSetupAttempt] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (autoHeightSetup.current || !fullWidthCompDetails || autoHeightSetupAttempt > 10) {\n      return;\n    }\n    const eChild = eGui.current?.firstChild;\n    if (eChild) {\n      rowCtrl.setupDetailRowAutoHeight(eChild);\n      autoHeightSetup.current = true;\n    } else {\n      setAutoHeightSetupAttempt((prev) => prev + 1);\n    }\n  }, [fullWidthCompDetails, autoHeightSetupAttempt]);\n  const cssClassManager = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  if (!cssClassManager.current) {\n    cssClassManager.current = new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.CssClassManager(() => eGui.current);\n  }\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGui.current = eRef;\n    compBean.current = eRef ? context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._EmptyBean()) : context.destroyBean(compBean.current);\n    if (!eRef) {\n      rowCtrl.unsetComp(containerType);\n      return;\n    }\n    if (!rowCtrl.isAlive()) {\n      return;\n    }\n    const compProxy = {\n      // the rowTop is managed by state, instead of direct style manipulation by rowCtrl (like all the other styles)\n      // as we need to have an initial value when it's placed into he DOM for the first time, for animation to work.\n      setTop,\n      setTransform,\n      // i found using React for managing classes at the row level was to slow, as modifying classes caused a lot of\n      // React code to execute, so avoiding React for managing CSS Classes made the grid go much faster.\n      addOrRemoveCssClass: (name, on) => cssClassManager.current.addOrRemoveCssClass(name, on),\n      setDomOrder: (domOrder) => domOrderRef.current = domOrder,\n      setRowIndex,\n      setRowId,\n      setRowBusinessKey,\n      setUserStyles,\n      // if we don't maintain the order, then cols will be ripped out and into the dom\n      // when cols reordered, which would stop the CSS transitions from working\n      setCellCtrls: (next, useFlushSync) => {\n        prevCellCtrlsRef.current = cellCtrlsRef.current;\n        cellCtrlsRef.current = next;\n        const nextCells = getNextValueIfDifferent(prevCellCtrlsRef.current, next, domOrderRef.current);\n        if (nextCells !== prevCellCtrlsRef.current) {\n          agFlushSync(useFlushSync, () => setCellCtrls(nextCells));\n        }\n      },\n      showFullWidth: (compDetails) => setFullWidthCompDetails(compDetails),\n      getFullWidthCellRenderer: () => fullWidthCompRef.current,\n      refreshFullWidth: (getUpdatedParams) => {\n        if (canRefreshFullWidthRef.current) {\n          setFullWidthCompDetails((prevFullWidthCompDetails) => ({\n            ...prevFullWidthCompDetails,\n            params: getUpdatedParams()\n          }));\n          return true;\n        } else {\n          if (!fullWidthCompRef.current || !fullWidthCompRef.current.refresh) {\n            return false;\n          }\n          return fullWidthCompRef.current.refresh(getUpdatedParams());\n        }\n      }\n    };\n    rowCtrl.setComp(compProxy, eRef, containerType, compBean.current);\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(\n    () => showJsComp(fullWidthCompDetails, context, eGui.current, fullWidthCompRef),\n    [fullWidthCompDetails]\n  );\n  const rowStyles = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const res = { top, transform };\n    Object.assign(res, userStyles);\n    return res;\n  }, [top, transform, userStyles]);\n  const showFullWidthFramework = isFullWidth && fullWidthCompDetails?.componentFromFramework;\n  const showCells = !isFullWidth && cellCtrls != null;\n  const reactFullWidthCellRendererStateless = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const res = fullWidthCompDetails?.componentFromFramework && isComponentStateless(fullWidthCompDetails.componentClass);\n    return !!res;\n  }, [fullWidthCompDetails]);\n  const canRefreshFullWidthRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    canRefreshFullWidthRef.current = reactFullWidthCellRendererStateless && !!fullWidthCompDetails && !!gos.get(\"reactiveCustomComponents\");\n  }, [reactFullWidthCellRendererStateless, fullWidthCompDetails]);\n  const showCellsJsx = () => cellCtrls?.map((cellCtrl) => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    cellComp_default,\n    {\n      cellCtrl,\n      editingRow: rowCtrl.editing,\n      printLayout: rowCtrl.printLayout,\n      key: cellCtrl.instanceId\n    }\n  ));\n  const showFullWidthFrameworkJsx = () => {\n    const FullWidthComp = fullWidthCompDetails.componentClass;\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, reactFullWidthCellRendererStateless ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(FullWidthComp, { ...fullWidthCompDetails.params }) : /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(FullWidthComp, { ...fullWidthCompDetails.params, ref: fullWidthCompRef }));\n  };\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    \"div\",\n    {\n      ref: setRef2,\n      role: \"row\",\n      style: rowStyles,\n      \"row-index\": rowIndex,\n      \"row-id\": rowId,\n      \"row-business-key\": rowBusinessKey\n    },\n    showCells && showCellsJsx(),\n    showFullWidthFramework && showFullWidthFrameworkJsx()\n  );\n};\nvar rowComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(RowComp);\n\n// packages/ag-grid-react/src/reactUi/rows/rowContainerComp.tsx\nvar RowContainerComp = ({ name }) => {\n  const { context, gos } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const containerOptions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._getRowContainerOptions)(name), [name]);\n  const eViewport = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eContainer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eSpanContainer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const rowCtrlsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  const prevRowCtrlsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  const [rowCtrlsOrdered, setRowCtrlsOrdered] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => []);\n  const isSpanning = !!gos.get(\"enableCellSpan\") && !!containerOptions.getSpannedRowCtrls;\n  const spannedRowCtrlsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  const prevSpannedRowCtrlsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  const [spannedRowCtrlsOrdered, setSpannedRowCtrlsOrdered] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => []);\n  const domOrderRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  const rowContainerCtrlRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const viewportClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => classesList(\"ag-viewport\", (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._getRowViewportClass)(name)), [name]);\n  const containerClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => classesList((0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._getRowContainerClass)(name)), [name]);\n  const spanClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => classesList(\"ag-spanning-container\", (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._getRowSpanContainerClass)(name)), [name]);\n  const shouldRenderViewport = containerOptions.type === \"center\" || isSpanning;\n  const topLevelRef = shouldRenderViewport ? eViewport : eContainer;\n  reactComment_default(\" AG Row Container \" + name + \" \", topLevelRef);\n  const areElementsReady = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    const viewportReady = !shouldRenderViewport || eViewport.current != null;\n    const containerReady = eContainer.current != null;\n    const spanContainerReady = !isSpanning || eSpanContainer.current != null;\n    return viewportReady && containerReady && spanContainerReady;\n  }, []);\n  const areElementsRemoved = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    return eViewport.current == null && eContainer.current == null && eSpanContainer.current == null;\n  }, []);\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (areElementsRemoved()) {\n      rowContainerCtrlRef.current = context.destroyBean(rowContainerCtrlRef.current);\n    }\n    if (areElementsReady()) {\n      const updateRowCtrlsOrdered = (useFlushSync) => {\n        const next = getNextValueIfDifferent(\n          prevRowCtrlsRef.current,\n          rowCtrlsRef.current,\n          domOrderRef.current\n        );\n        if (next !== prevRowCtrlsRef.current) {\n          prevRowCtrlsRef.current = next;\n          agFlushSync(useFlushSync, () => setRowCtrlsOrdered(next));\n        }\n      };\n      const updateSpannedRowCtrlsOrdered = (useFlushSync) => {\n        const next = getNextValueIfDifferent(\n          prevSpannedRowCtrlsRef.current,\n          spannedRowCtrlsRef.current,\n          domOrderRef.current\n        );\n        if (next !== prevSpannedRowCtrlsRef.current) {\n          prevSpannedRowCtrlsRef.current = next;\n          agFlushSync(useFlushSync, () => setSpannedRowCtrlsOrdered(next));\n        }\n      };\n      const compProxy = {\n        setHorizontalScroll: (offset) => {\n          if (eViewport.current) {\n            eViewport.current.scrollLeft = offset;\n          }\n        },\n        setViewportHeight: (height) => {\n          if (eViewport.current) {\n            eViewport.current.style.height = height;\n          }\n        },\n        setRowCtrls: ({ rowCtrls, useFlushSync }) => {\n          const useFlush = !!useFlushSync && rowCtrlsRef.current.length > 0 && rowCtrls.length > 0;\n          rowCtrlsRef.current = rowCtrls;\n          updateRowCtrlsOrdered(useFlush);\n        },\n        setSpannedRowCtrls: (rowCtrls, useFlushSync) => {\n          const useFlush = !!useFlushSync && spannedRowCtrlsRef.current.length > 0 && rowCtrls.length > 0;\n          spannedRowCtrlsRef.current = rowCtrls;\n          updateSpannedRowCtrlsOrdered(useFlush);\n        },\n        setDomOrder: (domOrder) => {\n          if (domOrderRef.current != domOrder) {\n            domOrderRef.current = domOrder;\n            updateRowCtrlsOrdered(false);\n          }\n        },\n        setContainerWidth: (width) => {\n          if (eContainer.current) {\n            eContainer.current.style.width = width;\n          }\n        },\n        setOffsetTop: (offset) => {\n          if (eContainer.current) {\n            eContainer.current.style.transform = `translateY(${offset})`;\n          }\n        }\n      };\n      rowContainerCtrlRef.current = context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.RowContainerCtrl(name));\n      rowContainerCtrlRef.current.setComp(\n        compProxy,\n        eContainer.current,\n        eSpanContainer.current ?? void 0,\n        eViewport.current\n      );\n    }\n  }, [areElementsReady, areElementsRemoved]);\n  const setContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (e) => {\n      eContainer.current = e;\n      setRef2();\n    },\n    [setRef2]\n  );\n  const setSpanContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (e) => {\n      eSpanContainer.current = e;\n      setRef2();\n    },\n    [setRef2]\n  );\n  const setViewportRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (e) => {\n      eViewport.current = e;\n      setRef2();\n    },\n    [setRef2]\n  );\n  const buildContainer = () => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: containerClasses, ref: setContainerRef, role: \"rowgroup\" }, rowCtrlsOrdered.map((rowCtrl) => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(rowComp_default, { rowCtrl, containerType: containerOptions.type, key: rowCtrl.instanceId })));\n  if (!shouldRenderViewport) {\n    return buildContainer();\n  }\n  const buildSpanContainer = () => isSpanning && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: spanClasses, ref: setSpanContainerRef, role: \"rowgroup\" }, spannedRowCtrlsOrdered.map((rowCtrl) => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(rowComp_default, { rowCtrl, containerType: containerOptions.type, key: rowCtrl.instanceId })));\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: viewportClasses, ref: setViewportRef, role: \"presentation\" }, buildContainer(), buildSpanContainer());\n};\nvar rowContainerComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(RowContainerComp);\n\n// packages/ag-grid-react/src/reactUi/gridBodyComp.tsx\nvar GridBodyComp = () => {\n  const beans = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const { context, overlays } = beans;\n  const [rowAnimationClass, setRowAnimationClass] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n  const [topHeight, setTopHeight] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  const [bottomHeight, setBottomHeight] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  const [stickyTopHeight, setStickyTopHeight] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"0px\");\n  const [stickyTopTop, setStickyTopTop] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"0px\");\n  const [stickyTopWidth, setStickyTopWidth] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"100%\");\n  const [stickyBottomHeight, setStickyBottomHeight] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"0px\");\n  const [stickyBottomBottom, setStickyBottomBottom] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"0px\");\n  const [stickyBottomWidth, setStickyBottomWidth] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"100%\");\n  const [topDisplay, setTopDisplay] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n  const [bottomDisplay, setBottomDisplay] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n  const [forceVerticalScrollClass, setForceVerticalScrollClass] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [topAndBottomOverflowY, setTopAndBottomOverflowY] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n  const [cellSelectableCss, setCellSelectableCss] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [layoutClass, setLayoutClass] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"ag-layout-normal\");\n  const cssClassManager = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  if (!cssClassManager.current) {\n    cssClassManager.current = new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.CssClassManager(() => eRoot.current);\n  }\n  const eRoot = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eTop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eStickyTop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eStickyBottom = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eBody = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eBodyViewport = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eBottom = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const beansToDestroy = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  const destroyFuncs = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  reactComment_default(\" AG Grid Body \", eRoot);\n  reactComment_default(\" AG Pinned Top \", eTop);\n  reactComment_default(\" AG Sticky Top \", eStickyTop);\n  reactComment_default(\" AG Middle \", eBodyViewport);\n  reactComment_default(\" AG Pinned Bottom \", eBottom);\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eRoot.current = eRef;\n    if (!eRef) {\n      beansToDestroy.current = context.destroyBeans(beansToDestroy.current);\n      destroyFuncs.current.forEach((f) => f());\n      destroyFuncs.current = [];\n      return;\n    }\n    if (!context) {\n      return;\n    }\n    const attachToDom = (eParent, eChild) => {\n      eParent.appendChild(eChild);\n      destroyFuncs.current.push(() => eParent.removeChild(eChild));\n    };\n    const newComp = (compClass) => {\n      const comp = context.createBean(new compClass());\n      beansToDestroy.current.push(comp);\n      return comp;\n    };\n    const addComp = (eParent, compClass, comment) => {\n      attachToDom(eParent, document.createComment(comment));\n      attachToDom(eParent, newComp(compClass).getGui());\n    };\n    addComp(eRef, ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.FakeHScrollComp, \" AG Fake Horizontal Scroll \");\n    const overlayComp = overlays?.getOverlayWrapperCompClass();\n    if (overlayComp) {\n      addComp(eRef, overlayComp, \" AG Overlay Wrapper \");\n    }\n    if (eBody.current) {\n      addComp(eBody.current, ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.FakeVScrollComp, \" AG Fake Vertical Scroll \");\n    }\n    const compProxy = {\n      setRowAnimationCssOnBodyViewport: setRowAnimationClass,\n      setColumnCount: (count) => {\n        if (eRoot.current) {\n          (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._setAriaColCount)(eRoot.current, count);\n        }\n      },\n      setRowCount: (count) => {\n        if (eRoot.current) {\n          (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._setAriaRowCount)(eRoot.current, count);\n        }\n      },\n      setTopHeight,\n      setBottomHeight,\n      setStickyTopHeight,\n      setStickyTopTop,\n      setStickyTopWidth,\n      setTopDisplay,\n      setBottomDisplay,\n      setColumnMovingCss: (cssClass, flag) => cssClassManager.current.addOrRemoveCssClass(cssClass, flag),\n      updateLayoutClasses: setLayoutClass,\n      setAlwaysVerticalScrollClass: setForceVerticalScrollClass,\n      setPinnedTopBottomOverflowY: setTopAndBottomOverflowY,\n      setCellSelectableCss: (cssClass, flag) => setCellSelectableCss(flag ? cssClass : null),\n      setBodyViewportWidth: (width) => {\n        if (eBodyViewport.current) {\n          eBodyViewport.current.style.width = width;\n        }\n      },\n      registerBodyViewportResizeListener: (listener) => {\n        if (eBodyViewport.current) {\n          const unsubscribeFromResize = (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._observeResize)(beans, eBodyViewport.current, listener);\n          destroyFuncs.current.push(() => unsubscribeFromResize());\n        }\n      },\n      setStickyBottomHeight,\n      setStickyBottomBottom,\n      setStickyBottomWidth,\n      setGridRootRole: (role) => eRef.setAttribute(\"role\", role)\n    };\n    const ctrl = context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.GridBodyCtrl());\n    beansToDestroy.current.push(ctrl);\n    ctrl.setComp(\n      compProxy,\n      eRef,\n      eBodyViewport.current,\n      eTop.current,\n      eBottom.current,\n      eStickyTop.current,\n      eStickyBottom.current\n    );\n  }, []);\n  const rootClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => classesList(\"ag-root\", \"ag-unselectable\", layoutClass), [layoutClass]);\n  const bodyViewportClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => classesList(\n      \"ag-body-viewport\",\n      rowAnimationClass,\n      layoutClass,\n      forceVerticalScrollClass,\n      cellSelectableCss\n    ),\n    [rowAnimationClass, layoutClass, forceVerticalScrollClass, cellSelectableCss]\n  );\n  const bodyClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => classesList(\"ag-body\", layoutClass), [layoutClass]);\n  const topClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => classesList(\"ag-floating-top\", cellSelectableCss), [cellSelectableCss]);\n  const stickyTopClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => classesList(\"ag-sticky-top\", cellSelectableCss), [cellSelectableCss]);\n  const stickyBottomClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => classesList(\"ag-sticky-bottom\", stickyBottomHeight === \"0px\" ? \"ag-hidden\" : null, cellSelectableCss),\n    [cellSelectableCss, stickyBottomHeight]\n  );\n  const bottomClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => classesList(\"ag-floating-bottom\", cellSelectableCss), [cellSelectableCss]);\n  const topStyle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => ({\n      height: topHeight,\n      minHeight: topHeight,\n      display: topDisplay,\n      overflowY: topAndBottomOverflowY\n    }),\n    [topHeight, topDisplay, topAndBottomOverflowY]\n  );\n  const stickyTopStyle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => ({\n      height: stickyTopHeight,\n      top: stickyTopTop,\n      width: stickyTopWidth\n    }),\n    [stickyTopHeight, stickyTopTop, stickyTopWidth]\n  );\n  const stickyBottomStyle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => ({\n      height: stickyBottomHeight,\n      bottom: stickyBottomBottom,\n      width: stickyBottomWidth\n    }),\n    [stickyBottomHeight, stickyBottomBottom, stickyBottomWidth]\n  );\n  const bottomStyle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => ({\n      height: bottomHeight,\n      minHeight: bottomHeight,\n      display: bottomDisplay,\n      overflowY: topAndBottomOverflowY\n    }),\n    [bottomHeight, bottomDisplay, topAndBottomOverflowY]\n  );\n  const createRowContainer = (container) => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(rowContainerComp_default, { name: container, key: `${container}-container` });\n  const createSection = ({\n    section,\n    children,\n    className,\n    style\n  }) => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: section, className, role: \"presentation\", style }, children.map(createRowContainer));\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setRef2, className: rootClasses }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(gridHeaderComp_default, null), createSection({\n    section: eTop,\n    className: topClasses,\n    style: topStyle,\n    children: [\"topLeft\", \"topCenter\", \"topRight\", \"topFullWidth\"]\n  }), /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: bodyClasses, ref: eBody, role: \"presentation\" }, createSection({\n    section: eBodyViewport,\n    className: bodyViewportClasses,\n    children: [\"left\", \"center\", \"right\", \"fullWidth\"]\n  })), createSection({\n    section: eStickyTop,\n    className: stickyTopClasses,\n    style: stickyTopStyle,\n    children: [\"stickyTopLeft\", \"stickyTopCenter\", \"stickyTopRight\", \"stickyTopFullWidth\"]\n  }), createSection({\n    section: eStickyBottom,\n    className: stickyBottomClasses,\n    style: stickyBottomStyle,\n    children: [\"stickyBottomLeft\", \"stickyBottomCenter\", \"stickyBottomRight\", \"stickyBottomFullWidth\"]\n  }), createSection({\n    section: eBottom,\n    className: bottomClasses,\n    style: bottomStyle,\n    children: [\"bottomLeft\", \"bottomCenter\", \"bottomRight\", \"bottomFullWidth\"]\n  }));\n};\nvar gridBodyComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(GridBodyComp);\n\n// packages/ag-grid-react/src/reactUi/tabGuardComp.tsx\n\n\nvar TabGuardCompRef = (props, forwardRef4) => {\n  const { children, eFocusableElement, onTabKeyDown, gridCtrl, forceFocusOutWhenTabGuardsAreEmpty, isEmpty } = props;\n  const { context } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const topTabGuardRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const bottomTabGuardRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const tabGuardCtrlRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const setTabIndex = (value) => {\n    const processedValue = value == null ? void 0 : parseInt(value, 10).toString();\n    [topTabGuardRef, bottomTabGuardRef].forEach((tabGuard) => {\n      if (processedValue === void 0) {\n        tabGuard.current?.removeAttribute(\"tabindex\");\n      } else {\n        tabGuard.current?.setAttribute(\"tabindex\", processedValue);\n      }\n    });\n  };\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(forwardRef4, () => ({\n    forceFocusOutOfContainer(up) {\n      tabGuardCtrlRef.current?.forceFocusOutOfContainer(up);\n    }\n  }));\n  const setupCtrl = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    const topTabGuard = topTabGuardRef.current;\n    const bottomTabGuard = bottomTabGuardRef.current;\n    if (!topTabGuard && !bottomTabGuard) {\n      tabGuardCtrlRef.current = context.destroyBean(tabGuardCtrlRef.current);\n      return;\n    }\n    if (topTabGuard && bottomTabGuard) {\n      const compProxy = {\n        setTabIndex\n      };\n      tabGuardCtrlRef.current = context.createBean(\n        new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.TabGuardCtrl({\n          comp: compProxy,\n          eTopGuard: topTabGuard,\n          eBottomGuard: bottomTabGuard,\n          eFocusableElement,\n          onTabKeyDown,\n          forceFocusOutWhenTabGuardsAreEmpty,\n          focusInnerElement: (fromBottom) => gridCtrl.focusInnerElement(fromBottom),\n          isEmpty\n        })\n      );\n    }\n  }, []);\n  const setTopRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (e) => {\n      topTabGuardRef.current = e;\n      setupCtrl();\n    },\n    [setupCtrl]\n  );\n  const setBottomRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (e) => {\n      bottomTabGuardRef.current = e;\n      setupCtrl();\n    },\n    [setupCtrl]\n  );\n  const createTabGuard = (side) => {\n    const className = side === \"top\" ? ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.TabGuardClassNames.TAB_GUARD_TOP : ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.TabGuardClassNames.TAB_GUARD_BOTTOM;\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n      \"div\",\n      {\n        className: `${ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.TabGuardClassNames.TAB_GUARD} ${className}`,\n        role: \"presentation\",\n        ref: side === \"top\" ? setTopRef : setBottomRef\n      }\n    );\n  };\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, createTabGuard(\"top\"), children, createTabGuard(\"bottom\"));\n};\nvar TabGuardComp = (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(TabGuardCompRef);\nvar tabGuardComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(TabGuardComp);\n\n// packages/ag-grid-react/src/reactUi/gridComp.tsx\nvar GridComp = ({ context }) => {\n  const [rtlClass, setRtlClass] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n  const [layoutClass, setLayoutClass] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n  const [cursor, setCursor] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [userSelect, setUserSelect] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [initialised, setInitialised] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const [tabGuardReady, setTabGuardReady] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const gridCtrlRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const eRootWrapperRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const tabGuardRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const [eGridBodyParent, setGridBodyParent] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const focusInnerElementRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(() => void 0);\n  const paginationCompRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const focusableContainersRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  const onTabKeyDown = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => void 0, []);\n  const beans = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (context.isDestroyed()) {\n      return null;\n    }\n    return context.getBeans();\n  }, [context]);\n  reactComment_default(\" AG Grid \", eRootWrapperRef);\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eRootWrapperRef.current = eRef;\n    gridCtrlRef.current = eRef ? context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.GridCtrl()) : context.destroyBean(gridCtrlRef.current);\n    if (!eRef || context.isDestroyed()) {\n      return;\n    }\n    const gridCtrl = gridCtrlRef.current;\n    focusInnerElementRef.current = gridCtrl.focusInnerElement.bind(gridCtrl);\n    const compProxy = {\n      destroyGridUi: () => {\n      },\n      // do nothing, as framework users destroy grid by removing the comp\n      setRtlClass,\n      forceFocusOutOfContainer: (up) => {\n        if (!up && paginationCompRef.current?.isDisplayed()) {\n          paginationCompRef.current.forceFocusOutOfContainer(up);\n          return;\n        }\n        tabGuardRef.current?.forceFocusOutOfContainer(up);\n      },\n      updateLayoutClasses: setLayoutClass,\n      getFocusableContainers: () => {\n        const comps = [];\n        const gridBodyCompEl = eRootWrapperRef.current?.querySelector(\".ag-root\");\n        if (gridBodyCompEl) {\n          comps.push({ getGui: () => gridBodyCompEl });\n        }\n        focusableContainersRef.current.forEach((comp) => {\n          if (comp.isDisplayed()) {\n            comps.push(comp);\n          }\n        });\n        return comps;\n      },\n      setCursor,\n      setUserSelect\n    };\n    gridCtrl.setComp(compProxy, eRef, eRef);\n    setInitialised(true);\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const gridCtrl = gridCtrlRef.current;\n    const eRootWrapper = eRootWrapperRef.current;\n    if (!tabGuardReady || !beans || !gridCtrl || !eGridBodyParent || !eRootWrapper) {\n      return;\n    }\n    const beansToDestroy = [];\n    const {\n      watermarkSelector,\n      paginationSelector,\n      sideBarSelector,\n      statusBarSelector,\n      gridHeaderDropZonesSelector\n    } = gridCtrl.getOptionalSelectors();\n    const additionalEls = [];\n    if (gridHeaderDropZonesSelector) {\n      const headerDropZonesComp = context.createBean(new gridHeaderDropZonesSelector.component());\n      const eGui = headerDropZonesComp.getGui();\n      eRootWrapper.insertAdjacentElement(\"afterbegin\", eGui);\n      additionalEls.push(eGui);\n      beansToDestroy.push(headerDropZonesComp);\n    }\n    if (sideBarSelector) {\n      const sideBarComp = context.createBean(new sideBarSelector.component());\n      const eGui = sideBarComp.getGui();\n      const bottomTabGuard = eGridBodyParent.querySelector(\".ag-tab-guard-bottom\");\n      if (bottomTabGuard) {\n        bottomTabGuard.insertAdjacentElement(\"beforebegin\", eGui);\n        additionalEls.push(eGui);\n      }\n      beansToDestroy.push(sideBarComp);\n      focusableContainersRef.current.push(sideBarComp);\n    }\n    const addComponentToDom = (component) => {\n      const comp = context.createBean(new component());\n      const eGui = comp.getGui();\n      eRootWrapper.insertAdjacentElement(\"beforeend\", eGui);\n      additionalEls.push(eGui);\n      beansToDestroy.push(comp);\n      return comp;\n    };\n    if (statusBarSelector) {\n      addComponentToDom(statusBarSelector.component);\n    }\n    if (paginationSelector) {\n      const paginationComp = addComponentToDom(paginationSelector.component);\n      paginationCompRef.current = paginationComp;\n      focusableContainersRef.current.push(paginationComp);\n    }\n    if (watermarkSelector) {\n      addComponentToDom(watermarkSelector.component);\n    }\n    return () => {\n      context.destroyBeans(beansToDestroy);\n      additionalEls.forEach((el) => {\n        el.parentElement?.removeChild(el);\n      });\n    };\n  }, [tabGuardReady, eGridBodyParent, beans]);\n  const rootWrapperClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => classesList(\"ag-root-wrapper\", rtlClass, layoutClass),\n    [rtlClass, layoutClass]\n  );\n  const rootWrapperBodyClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => classesList(\"ag-root-wrapper-body\", \"ag-focus-managed\", layoutClass),\n    [layoutClass]\n  );\n  const topStyle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => ({\n      userSelect: userSelect != null ? userSelect : \"\",\n      WebkitUserSelect: userSelect != null ? userSelect : \"\",\n      cursor: cursor != null ? cursor : \"\"\n    }),\n    [userSelect, cursor]\n  );\n  const setTabGuardCompRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((ref) => {\n    tabGuardRef.current = ref;\n    setTabGuardReady(ref !== null);\n  }, []);\n  const isFocusable = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => !gridCtrlRef.current?.isFocusable(), []);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setRef2, className: rootWrapperClasses, style: topStyle, role: \"presentation\" }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: rootWrapperBodyClasses, ref: setGridBodyParent, role: \"presentation\" }, initialised && eGridBodyParent && beans && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(BeansContext.Provider, { value: beans }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    tabGuardComp_default,\n    {\n      ref: setTabGuardCompRef,\n      eFocusableElement: eGridBodyParent,\n      onTabKeyDown,\n      gridCtrl: gridCtrlRef.current,\n      forceFocusOutWhenTabGuardsAreEmpty: true,\n      isEmpty: isFocusable\n    },\n    // we wait for initialised before rending the children, so GridComp has created and registered with it's\n    // GridCtrl before we create the child GridBodyComp. Otherwise the GridBodyComp would initialise first,\n    // before we have set the the Layout CSS classes, causing the GridBodyComp to render rows to a grid that\n    // doesn't have it's height specified, which would result if all the rows getting rendered (and if many rows,\n    // hangs the UI)\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(gridBodyComp_default, null)\n  ))));\n};\nvar gridComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(GridComp);\n\n// packages/ag-grid-react/src/reactUi/renderStatusService.tsx\n\nvar RenderStatusService = class extends ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.BeanStub {\n  wireBeans(beans) {\n    this.ctrlsSvc = beans.ctrlsSvc;\n  }\n  areHeaderCellsRendered() {\n    return this.ctrlsSvc.getHeaderRowContainerCtrls().every((container) => container.getAllCtrls().every((ctrl) => ctrl.areCellsRendered()));\n  }\n};\n\n// packages/ag-grid-react/src/reactUi/agGridReactUi.tsx\nvar reactPropsNotGridOptions = {\n  gridOptions: void 0,\n  modules: void 0,\n  containerStyle: void 0,\n  className: void 0,\n  setGridApi: void 0,\n  componentWrappingElement: void 0,\n  maxComponentCreationTimeMs: void 0,\n  children: void 0\n};\nvar excludeReactCompProps = new Set(Object.keys(reactPropsNotGridOptions));\nvar AgGridReactUi = (props) => {\n  const apiRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const portalManager = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const destroyFuncs = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  const whenReadyFuncs = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  const prevProps = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(props);\n  const frameworkOverridesRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const gridIdRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const ready = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  const [context, setContext] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(void 0);\n  const [, setPortalRefresher] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGui.current = eRef;\n    if (!eRef) {\n      destroyFuncs.current.forEach((f) => f());\n      destroyFuncs.current.length = 0;\n      return;\n    }\n    const modules = props.modules || [];\n    if (!portalManager.current) {\n      portalManager.current = new PortalManager(\n        () => setPortalRefresher((prev) => prev + 1),\n        props.componentWrappingElement,\n        props.maxComponentCreationTimeMs\n      );\n      destroyFuncs.current.push(() => {\n        portalManager.current?.destroy();\n        portalManager.current = null;\n      });\n    }\n    const mergedGridOps = (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._combineAttributesAndGridOptions)(\n      props.gridOptions,\n      props,\n      Object.keys(props).filter((key) => !excludeReactCompProps.has(key))\n    );\n    const processQueuedUpdates = () => {\n      if (ready.current) {\n        const getFn = () => frameworkOverridesRef.current?.shouldQueueUpdates() ? void 0 : whenReadyFuncs.current.shift();\n        let fn = getFn();\n        while (fn) {\n          fn();\n          fn = getFn();\n        }\n      }\n    };\n    const frameworkOverrides = new ReactFrameworkOverrides(processQueuedUpdates);\n    frameworkOverridesRef.current = frameworkOverrides;\n    const renderStatus = new RenderStatusService();\n    const gridParams = {\n      providedBeanInstances: {\n        frameworkCompWrapper: new ReactFrameworkComponentWrapper(\n          portalManager.current,\n          mergedGridOps.reactiveCustomComponents ?? (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._getGlobalGridOption)(\"reactiveCustomComponents\") ?? true\n        ),\n        renderStatus\n      },\n      modules,\n      frameworkOverrides,\n      setThemeOnGridDiv: true\n    };\n    const createUiCallback = (context2) => {\n      setContext(context2);\n      context2.createBean(renderStatus);\n      destroyFuncs.current.push(() => {\n        context2.destroy();\n      });\n      context2.getBean(\"ctrlsSvc\").whenReady(\n        {\n          addDestroyFunc: (func) => {\n            destroyFuncs.current.push(func);\n          }\n        },\n        () => {\n          if (context2.isDestroyed()) {\n            return;\n          }\n          const api = apiRef.current;\n          if (api) {\n            props.setGridApi?.(api);\n          }\n        }\n      );\n    };\n    const acceptChangesCallback = (context2) => {\n      context2.getBean(\"ctrlsSvc\").whenReady(\n        {\n          addDestroyFunc: (func) => {\n            destroyFuncs.current.push(func);\n          }\n        },\n        () => {\n          whenReadyFuncs.current.forEach((f) => f());\n          whenReadyFuncs.current.length = 0;\n          ready.current = true;\n        }\n      );\n    };\n    const gridCoreCreator = new ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.GridCoreCreator();\n    mergedGridOps.gridId ?? (mergedGridOps.gridId = gridIdRef.current);\n    apiRef.current = gridCoreCreator.create(\n      eRef,\n      mergedGridOps,\n      createUiCallback,\n      acceptChangesCallback,\n      gridParams\n    );\n    destroyFuncs.current.push(() => {\n      apiRef.current = void 0;\n    });\n    if (apiRef.current) {\n      gridIdRef.current = apiRef.current.getGridId();\n    }\n  }, []);\n  const style = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    return {\n      height: \"100%\",\n      ...props.containerStyle || {}\n    };\n  }, [props.containerStyle]);\n  const processWhenReady = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((func) => {\n    if (ready.current && !frameworkOverridesRef.current?.shouldQueueUpdates()) {\n      func();\n    } else {\n      whenReadyFuncs.current.push(func);\n    }\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const changes = extractGridPropertyChanges(prevProps.current, props);\n    prevProps.current = props;\n    processWhenReady(() => {\n      if (apiRef.current) {\n        (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._processOnChange)(changes, apiRef.current);\n      }\n    });\n  }, [props]);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { style, className: props.className, ref: setRef2 }, context && !context.isDestroyed() ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(gridComp_default, { context }) : null, portalManager.current?.getPortals() ?? null);\n};\nfunction extractGridPropertyChanges(prevProps, nextProps) {\n  const changes = {};\n  Object.keys(nextProps).forEach((propKey) => {\n    if (excludeReactCompProps.has(propKey)) {\n      return;\n    }\n    const propValue = nextProps[propKey];\n    if (prevProps[propKey] !== propValue) {\n      changes[propKey] = propValue;\n    }\n  });\n  return changes;\n}\nvar ReactFrameworkComponentWrapper = class extends ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.BaseComponentWrapper {\n  constructor(parent, reactiveCustomComponents) {\n    super();\n    this.parent = parent;\n    this.reactiveCustomComponents = reactiveCustomComponents;\n  }\n  createWrapper(UserReactComponent, componentType) {\n    if (this.reactiveCustomComponents) {\n      const getComponentClass = (propertyName) => {\n        switch (propertyName) {\n          case \"filter\":\n            return FilterComponentWrapper;\n          case \"floatingFilterComponent\":\n            return FloatingFilterComponentWrapper;\n          case \"dateComponent\":\n            return DateComponentWrapper;\n          case \"dragAndDropImageComponent\":\n            return DragAndDropImageComponentWrapper;\n          case \"loadingOverlayComponent\":\n            return LoadingOverlayComponentWrapper;\n          case \"noRowsOverlayComponent\":\n            return NoRowsOverlayComponentWrapper;\n          case \"statusPanel\":\n            return StatusPanelComponentWrapper;\n          case \"toolPanel\":\n            return ToolPanelComponentWrapper;\n          case \"menuItem\":\n            return MenuItemComponentWrapper;\n          case \"cellRenderer\":\n            return CellRendererComponentWrapper;\n          case \"innerHeaderComponent\":\n            return InnerHeaderComponentWrapper;\n        }\n      };\n      const ComponentClass = getComponentClass(componentType.name);\n      if (ComponentClass) {\n        return new ComponentClass(UserReactComponent, this.parent, componentType);\n      }\n    } else {\n      switch (componentType.name) {\n        case \"filter\":\n        case \"floatingFilterComponent\":\n        case \"dateComponent\":\n        case \"dragAndDropImageComponent\":\n        case \"loadingOverlayComponent\":\n        case \"noRowsOverlayComponent\":\n        case \"statusPanel\":\n        case \"toolPanel\":\n        case \"menuItem\":\n        case \"cellRenderer\":\n          warnReactiveCustomComponents();\n          break;\n      }\n    }\n    const suppressFallbackMethods = !componentType.cellRenderer && componentType.name !== \"toolPanel\";\n    return new ReactComponent(UserReactComponent, this.parent, componentType, suppressFallbackMethods);\n  }\n};\nvar DetailCellRenderer = (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((props, ref) => {\n  const beans = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const { registry, context, gos, rowModel } = beans;\n  const [cssClasses, setCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses());\n  const [gridCssClasses, setGridCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses());\n  const [detailGridOptions, setDetailGridOptions] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [detailRowData, setDetailRowData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const ctrlRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const eGuiRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const resizeObserverDestroyFunc = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const parentModules = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._getGridRegisteredModules)(props.api.getGridId(), detailGridOptions?.rowModelType ?? \"clientSide\"),\n    [props]\n  );\n  const topClassName = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => cssClasses.toString() + \" ag-details-row\", [cssClasses]);\n  const gridClassName = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => gridCssClasses.toString() + \" ag-details-grid\", [gridCssClasses]);\n  if (ref) {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(ref, () => ({\n      refresh() {\n        return ctrlRef.current?.refresh() ?? false;\n      }\n    }));\n  }\n  if (props.template) {\n    (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._warn)(230);\n  }\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGuiRef.current = eRef;\n    if (!eRef) {\n      ctrlRef.current = context.destroyBean(ctrlRef.current);\n      resizeObserverDestroyFunc.current?.();\n      return;\n    }\n    const compProxy = {\n      addOrRemoveCssClass: (name, on) => setCssClasses((prev) => prev.setClass(name, on)),\n      addOrRemoveDetailGridCssClass: (name, on) => setGridCssClasses((prev) => prev.setClass(name, on)),\n      setDetailGrid: (gridOptions) => setDetailGridOptions(gridOptions),\n      setRowData: (rowData) => setDetailRowData(rowData),\n      getGui: () => eGuiRef.current\n    };\n    const ctrl = registry.createDynamicBean(\"detailCellRendererCtrl\", true);\n    if (!ctrl) {\n      return;\n    }\n    context.createBean(ctrl);\n    ctrl.init(compProxy, props);\n    ctrlRef.current = ctrl;\n    if (gos.get(\"detailRowAutoHeight\")) {\n      const checkRowSizeFunc = () => {\n        if (eGuiRef.current == null) {\n          return;\n        }\n        const clientHeight = eGuiRef.current.clientHeight;\n        if (clientHeight != null && clientHeight > 0) {\n          const updateRowHeightFunc = () => {\n            props.node.setRowHeight(clientHeight);\n            if ((0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._isClientSideRowModel)(gos, rowModel) || (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._isServerSideRowModel)(gos, rowModel)) {\n              rowModel.onRowHeightChanged();\n            }\n          };\n          setTimeout(updateRowHeightFunc, 0);\n        }\n      };\n      resizeObserverDestroyFunc.current = (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_2__._observeResize)(beans, eRef, checkRowSizeFunc);\n      checkRowSizeFunc();\n    }\n  }, []);\n  const setGridApi = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((api) => {\n    ctrlRef.current?.registerDetailWithMaster(api);\n  }, []);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: topClassName, ref: setRef2 }, detailGridOptions && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    AgGridReactUi,\n    {\n      className: gridClassName,\n      ...detailGridOptions,\n      modules: parentModules,\n      rowData: detailRowData,\n      setGridApi\n    }\n  ));\n});\nvar ReactFrameworkOverrides = class extends ag_grid_community__WEBPACK_IMPORTED_MODULE_2__.VanillaFrameworkOverrides {\n  constructor(processQueuedUpdates) {\n    super(\"react\");\n    this.processQueuedUpdates = processQueuedUpdates;\n    this.queueUpdates = false;\n    this.renderingEngine = \"react\";\n    this.frameworkComponents = {\n      agGroupCellRenderer: groupCellRenderer_default,\n      agGroupRowRenderer: groupCellRenderer_default,\n      agDetailCellRenderer: DetailCellRenderer\n    };\n    this.wrapIncoming = (callback, source) => {\n      if (source === \"ensureVisible\") {\n        return runWithoutFlushSync(callback);\n      }\n      return callback();\n    };\n  }\n  frameworkComponent(name) {\n    return this.frameworkComponents[name];\n  }\n  isFrameworkComponent(comp) {\n    if (!comp) {\n      return false;\n    }\n    const prototype = comp.prototype;\n    const isJsComp = prototype && \"getGui\" in prototype;\n    return !isJsComp;\n  }\n  getLockOnRefresh() {\n    this.queueUpdates = true;\n  }\n  releaseLockOnRefresh() {\n    this.queueUpdates = false;\n    this.processQueuedUpdates();\n  }\n  shouldQueueUpdates() {\n    return this.queueUpdates;\n  }\n  runWhenReadyAsync() {\n    return isReact19();\n  }\n};\n\n// packages/ag-grid-react/src/agGridReact.tsx\nvar AgGridReact = class extends react__WEBPACK_IMPORTED_MODULE_0__.Component {\n  constructor() {\n    super(...arguments);\n    this.apiListeners = [];\n    this.setGridApi = (api) => {\n      this.api = api;\n      this.apiListeners.forEach((listener) => listener(api));\n    };\n  }\n  registerApiListener(listener) {\n    this.apiListeners.push(listener);\n  }\n  componentWillUnmount() {\n    this.apiListeners.length = 0;\n  }\n  render() {\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(AgGridReactUi, { ...this.props, setGridApi: this.setGridApi });\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/interfaces.ts\n\nfunction useGridCustomComponent(methods) {\n  const { setMethods } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(CustomContext);\n  setMethods(methods);\n}\nfunction useGridCellEditor(callbacks) {\n  useGridCustomComponent(callbacks);\n}\nfunction useGridDate(callbacks) {\n  return useGridCustomComponent(callbacks);\n}\nfunction useGridFilter(callbacks) {\n  return useGridCustomComponent(callbacks);\n}\nfunction useGridFloatingFilter(callbacks) {\n  useGridCustomComponent(callbacks);\n}\nfunction useGridMenuItem(callbacks) {\n  useGridCustomComponent(callbacks);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ag-grid-react/dist/package/index.esm.mjs\n");

/***/ })

};
;