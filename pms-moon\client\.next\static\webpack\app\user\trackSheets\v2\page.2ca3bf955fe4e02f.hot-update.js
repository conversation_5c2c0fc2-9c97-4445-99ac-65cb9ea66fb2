"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/trackSheets/v2/page",{

/***/ "(app-pages-browser)/./app/user/trackSheets/v2/ManageTrackSheet.tsx":
/*!******************************************************!*\
  !*** ./app/user/trackSheets/v2/ManageTrackSheet.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _createTrackSheet__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./createTrackSheet */ \"(app-pages-browser)/./app/user/trackSheets/v2/createTrackSheet.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _TrackSheetContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./TrackSheetContext */ \"(app-pages-browser)/./app/user/trackSheets/v2/TrackSheetContext.tsx\");\n/* harmony import */ var _ClientSelectPage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ClientSelectPage */ \"(app-pages-browser)/./app/user/trackSheets/v2/ClientSelectPage.tsx\");\n/* harmony import */ var _components_sidebar_Sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/sidebar/Sidebar */ \"(app-pages-browser)/./components/sidebar/Sidebar.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./components/ui/sidebar.tsx\");\n/* harmony import */ var _app_component_BreadCrumbs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/_component/BreadCrumbs */ \"(app-pages-browser)/./app/_component/BreadCrumbs.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Building2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/_component/SearchSelect */ \"(app-pages-browser)/./app/_component/SearchSelect.tsx\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/lib/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ManageWorkSheet = (param)=>{\n    let { permissions, client, carrier, associate, userData, actions } = param;\n    _s();\n    const [filterdata, setFilterData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [deleteData, setDeletedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [recievedFDate, setRecievedFDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [recievedTDate, setRecievedTDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [invoiceFDate, setInvoiceFDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [invoiceTDate, setInvoiceTDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [shipmentFDate, setShipmentFDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [shipmentTDate, setShipmentTDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [activeView, setActiveView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"view\");\n    const [initialAssociateId, setInitialAssociateId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [initialClientId, setInitialClientId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showFullForm, setShowFullForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const trackSheetSchema = zod__WEBPACK_IMPORTED_MODULE_10__.z.object({\n        clientId: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().min(1, \"Client is required\"),\n        entries: zod__WEBPACK_IMPORTED_MODULE_10__.z.array(zod__WEBPACK_IMPORTED_MODULE_10__.z.object({\n            company: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().min(1, \"Company is required\"),\n            division: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().optional(),\n            invoice: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().min(1, \"Invoice is required\"),\n            masterInvoice: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().optional(),\n            bol: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().min(1, \"BOL is required\"),\n            invoiceDate: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().min(1, \"Invoice date is required\"),\n            receivedDate: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().min(1, \"Received date is required\"),\n            shipmentDate: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().min(1, \"Shipment date is required\"),\n            carrierName: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().min(1, \"Carrier name is required\"),\n            invoiceStatus: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().min(1, \"Invoice status is required\"),\n            manualMatching: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().min(1, \"Manual matching is required\"),\n            invoiceType: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().min(1, \"Invoice type is required\"),\n            currency: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().min(1, \"Currency is required\"),\n            qtyShipped: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().min(1, \"Quantity shipped is required\"),\n            weightUnitName: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().min(1, \"Weight unit is required\"),\n            quantityBilledText: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().optional(),\n            invoiceTotal: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().min(1, \"Invoice total is required\"),\n            savings: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().optional(),\n            ftpFileName: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().min(1, \"FTP File Name is required\"),\n            ftpPage: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().min(1, \"FTP Page is required\").refine((value)=>validateFtpPageFormat(value), (value)=>{\n                if (!value || value.trim() === \"\") {\n                    return {\n                        message: \"FTP Page is required\"\n                    };\n                }\n                const ftpPageRegex = /^(\\d+)\\s+of\\s+(\\d+)$/i;\n                const match = value.match(ftpPageRegex);\n                if (!match) {\n                    return {\n                        message: \"\"\n                    };\n                }\n                const currentPage = parseInt(match[1], 10);\n                const totalPages = parseInt(match[2], 10);\n                if (currentPage <= 0 || totalPages <= 0) {\n                    return {\n                        message: \"Page numbers must be positive (greater than 0)\"\n                    };\n                }\n                if (currentPage > totalPages) {\n                    return {\n                        message: \"Please enter a page number between \".concat(totalPages, \" and \").concat(currentPage, \" \")\n                    };\n                }\n                return {\n                    message: \"Invalid page format\"\n                };\n            }),\n            docAvailable: zod__WEBPACK_IMPORTED_MODULE_10__.z.array(zod__WEBPACK_IMPORTED_MODULE_10__.z.string()).optional().default([]),\n            notes: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().optional(),\n            mistake: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().optional(),\n            legrandAlias: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().optional(),\n            legrandCompanyName: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().optional(),\n            legrandAddress: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().optional(),\n            legrandZipcode: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().optional(),\n            customFields: zod__WEBPACK_IMPORTED_MODULE_10__.z.array(zod__WEBPACK_IMPORTED_MODULE_10__.z.object({\n                id: zod__WEBPACK_IMPORTED_MODULE_10__.z.string(),\n                name: zod__WEBPACK_IMPORTED_MODULE_10__.z.string(),\n                type: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().optional(),\n                value: zod__WEBPACK_IMPORTED_MODULE_10__.z.string().optional()\n            })).default([])\n        }))\n    });\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_11__.useForm)({\n        defaultValues: {\n            associateId: \"\",\n            clientId: \"\"\n        }\n    });\n    const validateClientForAssociate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((associateId, currentClientId)=>{\n        if (associateId && currentClientId) {\n            var _currentClient_associateId;\n            const currentClient = client === null || client === void 0 ? void 0 : client.find((c)=>{\n                var _c_id;\n                return ((_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString()) === currentClientId;\n            });\n            if (currentClient && ((_currentClient_associateId = currentClient.associateId) === null || _currentClient_associateId === void 0 ? void 0 : _currentClient_associateId.toString()) !== associateId) {\n                form.setValue(\"clientId\", \"\");\n                setInitialClientId(\"\");\n                return false;\n            }\n        }\n        return true;\n    }, [\n        client,\n        form\n    ]);\n    const clearEntrySpecificClients = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const currentEntries = form.getValues(\"entries\") || [];\n        if (currentEntries.length > 0) {\n            const hasEntrySpecificClients = Array.isArray(currentEntries) && currentEntries.some((entry)=>entry.clientId);\n            if (hasEntrySpecificClients) {\n                const updatedEntries = currentEntries.map((entry)=>({\n                        ...entry,\n                        clientId: \"\"\n                    }));\n                form.setValue(\"entries\", updatedEntries);\n            }\n        }\n    }, [\n        form\n    ]);\n    const handleInitialSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((associateId, clientId)=>{\n        const finalAssociateId = associateId || initialAssociateId;\n        const finalClientId = clientId || initialClientId;\n        if (!finalAssociateId || !finalClientId) {\n            toast.error(\"Please select both Associate and Client to continue\");\n            return;\n        }\n        form.setValue(\"associateId\", finalAssociateId);\n        form.setValue(\"clientId\", finalClientId);\n        setTimeout(()=>{\n            handleCompanyAutoPopulation(0, finalClientId);\n            handleCustomFieldsFetch(0, finalClientId);\n            updateFilenames();\n            const formValues = form.getValues();\n            if (formValues.entries && Array.isArray(formValues.entries)) {\n                formValues.entries.forEach((entry, index)=>{\n                    if (index > 0) {\n                        const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || finalClientId;\n                        if (entryClientId) {\n                            handleCustomFieldsFetch(index, entryClientId);\n                        }\n                    }\n                });\n            }\n        }, 50);\n        setShowFullForm(true);\n    }, [\n        initialAssociateId,\n        initialClientId,\n        form,\n        handleCompanyAutoPopulation,\n        handleCustomFieldsFetch,\n        updateFilenames\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TrackSheetContext__WEBPACK_IMPORTED_MODULE_4__.TrackSheetContext.Provider, {\n        value: {\n            filterdata,\n            setFilterData,\n            deleteData,\n            setDeletedData,\n            recievedFDate,\n            recievedTDate,\n            setRecievedFDate,\n            setRecievedTDate,\n            invoiceFDate,\n            setInvoiceFDate,\n            invoiceTDate,\n            setInvoiceTDate,\n            shipmentFDate,\n            setShipmentFDate,\n            shipmentTDate,\n            setShipmentTDate\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_7__.SidebarProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex w-full min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sidebar_Sidebar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        permissions: permissions,\n                        profile: userData\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 w-full pl-3 overflow-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_BreadCrumbs__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    breadcrumblist: [\n                                        {\n                                            link: \"/user/trackSheets\",\n                                            name: \"TrackSheet\"\n                                        }\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-4 pl-3 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"default\",\n                                        onClick: ()=>setActiveView(\"view\"),\n                                        className: \"w-40 shadow-md rounded-xl text-base transition-all duration-200 \".concat(activeView === \"view\" ? \"bg-neutral-800 hover:bg-neutral-900 text-white\" : \"bg-white text-neutral-800 border border-neutral-300 hover:bg-neutral-100\"),\n                                        children: \"View TrackSheet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"default\",\n                                        onClick: ()=>setActiveView(\"create\"),\n                                        className: \"w-40 shadow-md rounded-xl text-base transition-all duration-200 \".concat(activeView === \"create\" ? \"bg-neutral-800 hover:bg-neutral-900 text-white\" : \"bg-white text-neutral-800 border border-neutral-300 hover:bg-neutral-100\"),\n                                        children: \"Create TrackSheet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-5 h-5 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 31\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-900\",\n                                                children: \"Create TrackSheet\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 31\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_11__.Form, {\n                                        ...form,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        form: form,\n                                                        name: \"associateId\",\n                                                        label: \"Select Associate\",\n                                                        placeholder: \"Search Associate...\",\n                                                        isRequired: true,\n                                                        options: (associate === null || associate === void 0 ? void 0 : associate.map((a)=>{\n                                                            var _a_id;\n                                                            return {\n                                                                value: (_a_id = a.id) === null || _a_id === void 0 ? void 0 : _a_id.toString(),\n                                                                label: a.name,\n                                                                name: a.name\n                                                            };\n                                                        })) || [],\n                                                        onValueChange: (value)=>{\n                                                            setInitialAssociateId(value);\n                                                            if (value && initialClientId) {\n                                                                validateClientForAssociate(value, initialClientId);\n                                                            } else {\n                                                                setInitialClientId(\"\");\n                                                                form.setValue(\"clientId\", \"\");\n                                                            }\n                                                            setShowFullForm(false);\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        form: form,\n                                                        name: \"clientId\",\n                                                        label: \"Select Client\",\n                                                        placeholder: \"Search Client...\",\n                                                        isRequired: true,\n                                                        disabled: !initialAssociateId,\n                                                        options: client.filter((c)=>{\n                                                            var _c_associate_id, _c_associate;\n                                                            return !initialAssociateId || ((_c_associate = c.associate) === null || _c_associate === void 0 ? void 0 : (_c_associate_id = _c_associate.id) === null || _c_associate_id === void 0 ? void 0 : _c_associate_id.toString()) === initialAssociateId;\n                                                        }).map((c)=>{\n                                                            var _c_id;\n                                                            return {\n                                                                value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n                                                                label: c.client_name,\n                                                                name: c.client_name\n                                                            };\n                                                        }) || [],\n                                                        onValueChange: (value)=>{\n                                                            setInitialClientId(value);\n                                                            if (showFullForm) {\n                                                                clearEntrySpecificClients();\n                                                            }\n                                                            if (value && initialAssociateId) {\n                                                                setTimeout(()=>{\n                                                                    handleInitialSelection(initialAssociateId, value);\n                                                                }, 100);\n                                                            } else {\n                                                                setShowFullForm(false);\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 31\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 27\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full animate-in fade-in duration-500 rounded-2xl shadow-sm  dark:bg-gray-800 p-1\",\n                                children: activeView === \"create\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_createTrackSheet__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    client: client,\n                                    carrier: carrier,\n                                    associate: associate,\n                                    userData: userData\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClientSelectPage__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    permissions: actions,\n                                    client: client\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                lineNumber: 216,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n            lineNumber: 215,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n        lineNumber: 195,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ManageWorkSheet, \"016kC22Ll7LYlShRaLKO3VLgbrs=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_11__.useForm\n    ];\n});\n_c = ManageWorkSheet;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ManageWorkSheet);\nvar _c;\n$RefreshReg$(_c, \"ManageWorkSheet\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/trackSheets/v2/ManageTrackSheet.tsx\n"));

/***/ })

});