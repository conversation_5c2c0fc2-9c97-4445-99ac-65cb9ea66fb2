"use client";
import React, { useState, useRef } from "react";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Checkbox } from "@/components/ui/checkbox";
import { cn } from "@/lib/utils";
import { ChevronDown, Check, X } from "lucide-react";
import { Badge } from "@/components/ui/badge";

type Option = {
  label: string;
  value: string;
};

type FormCheckboxGroupProps = {
  form: any;
  name: string;
  label: string;
  options: Option[];
  className?: string;
  isRequired?: boolean;
  isEntryPage?: boolean;
  disable?: boolean;
};

const FormCheckboxGroup = ({
  form,
  name,
  label,
  options,
  className,
  isRequired,
  isEntryPage,
  disable,
}: FormCheckboxGroupProps) => {
  const [open, setOpen] = useState(false);
  const [focusedIndex, setFocusedIndex] = useState(-1);
  const [searchTerm, setSearchTerm] = useState("");
  const inputRef = useRef<HTMLInputElement | null>(null);
  const optionRefs = useRef<(HTMLDivElement | null)[]>([]);
  const blurTimeout = useRef<NodeJS.Timeout | null>(null);

  const filteredOptions = options.filter((option) =>
    option.label.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => {
        const values: string[] = Array.isArray(field.value)
          ? field.value
          : field.value
          ? [field.value]
          : [];

        const selectedOptions = options.filter((option) =>
          values.includes(option.value)
        );

        return (
          <FormItem
            className={cn(
              isEntryPage ? "mb-1 space-y-0.5" : "md:mb-3 space-y-0.5",
              className
            )}
          >
            <FormLabel
              className={cn(
                isEntryPage ? "md:text-xs" : "md:text-base",
                "text-gray-800 dark:text-gray-300 whitespace-nowrap cursor-text"
              )}
            >
              {label}
              {isRequired && <span className="text-red-500">*</span>}
            </FormLabel>

            <div className="relative w-full">
              <div
                className={cn(
                  "w-full border border-none rounded-md bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-gray-100 outline-none focus-within:!outline-main-color px-3 py-2 flex flex-wrap items-center gap-1",
                  isEntryPage ? "h-auto min-h-7 text-xs" : "min-h-10"
                )}
                onClick={() => {
                  if (!disable) inputRef.current?.focus();
                }}
              >
                {selectedOptions.map((option) => (
                  <Badge
                    key={option.value}
                    variant="secondary"
                    className="flex items-center gap-1 py-0.5 pl-2 pr-1"
                  >
                    <span className="text-xs">{option.label}</span>
                    <X
                      className="h-3 w-3 cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation();
                        field.onChange(
                          values.filter((v) => v !== option.value)
                        );
                      }}
                    />
                  </Badge>
                ))}

                <input
                  type="text"
                  ref={inputRef}
                  value={searchTerm}
                  onChange={(e) => {
                    setSearchTerm(e.target.value);
                    setOpen(true);
                    setFocusedIndex(0);
                  }}
                  onFocus={() => {
                    if (blurTimeout.current) clearTimeout(blurTimeout.current);
                    setOpen(true);
                  }}
                  onBlur={() => {
                    blurTimeout.current = setTimeout(() => {
                      setOpen(false);
                    }, 100);
                  }}
                  className="bg-transparent border-none outline-none flex-1 min-w-[50px] text-gray-900 dark:text-gray-100 placeholder:text-gray-500"
                  disabled={disable}
                  role="combobox"
                  aria-expanded={open}
                  onKeyDown={(e) => {
                    if (e.key === "ArrowDown") {
                      e.preventDefault();
                      setFocusedIndex((prev) =>
                        Math.min(prev + 1, filteredOptions.length - 1)
                      );
                    } else if (e.key === "ArrowUp") {
                      e.preventDefault();
                      setFocusedIndex((prev) => Math.max(prev - 1, 0));
                    } else if (e.key === "Enter") {
                      if (
                        open &&
                        filteredOptions.length > 0 &&
                        focusedIndex >= 0
                      ) {
                        e.preventDefault();
                        const option = filteredOptions[focusedIndex];
                        const isSelected = values.includes(option.value);
                        const newValues = isSelected
                          ? values.filter((v) => v !== option.value)
                          : [...values, option.value];
                        field.onChange(newValues);
                        setSearchTerm("");
                      }
                    } else if (e.key === "Backspace") {
                      if (searchTerm === "" && values.length > 0) {
                        const newValues = [...values];
                        newValues.pop();
                        field.onChange(newValues);
                      }
                    } else if (e.key === "Tab") {
                      setOpen(false);
                    }
                  }}
                />
              </div>

              <div className="absolute right-2 top-1/2 -translate-y-1/2 pointer-events-none">
                <ChevronDown className="h-4 w-4 opacity-50" />
              </div>

              {open && (
                <div className="absolute z-10 w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded shadow-lg mt-1 max-h-60 overflow-auto">
                  {filteredOptions.length === 0 ? (
                    <div className="text-muted-foreground text-center py-4">
                      No options found
                    </div>
                  ) : (
                    filteredOptions.map((option, index) => {
                      const isSelected = values.includes(option.value);
                      return (
                        <div
                          key={option.value}
                          ref={(el) => {
                            optionRefs.current[index] = el;
                          }}
                          className={cn(
                            "flex items-center space-x-2 p-2 rounded-md cursor-pointer",
                            "hover:bg-accent hover:text-accent-foreground",
                            focusedIndex === index
                              ? "bg-accent text-accent-foreground font-medium"
                              : ""
                          )}
                          onMouseEnter={() => setFocusedIndex(index)}
                          onMouseDown={(e) => {
                            e.preventDefault();
                            const newValues = isSelected
                              ? values.filter((v) => v !== option.value)
                              : [...values, option.value];
                            field.onChange(newValues);
                            setSearchTerm("");
                            setOpen(true);
                          }}
                          role="option"
                          aria-selected={isSelected}
                          tabIndex={-1}
                        >
                          <FormControl>
                            <Checkbox
                              checked={isSelected}
                              disabled={disable}
                              className="pointer-events-none"
                              tabIndex={-1}
                            />
                          </FormControl>
                          <span className="text-sm font-normal cursor-pointer">
                            {option.label}
                          </span>
                          {isSelected && <Check className="h-4 w-4 ml-auto" />}
                        </div>
                      );
                    })
                  )}
                </div>
              )}
            </div>

            <FormMessage />
          </FormItem>
        );
      }}
    />
  );
};

export default FormCheckboxGroup;
