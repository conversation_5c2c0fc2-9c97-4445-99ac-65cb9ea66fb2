"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/trackSheets/v2/page",{

/***/ "(app-pages-browser)/./app/user/trackSheets/v2/ManageTrackSheet.tsx":
/*!******************************************************!*\
  !*** ./app/user/trackSheets/v2/ManageTrackSheet.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _createTrackSheet__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./createTrackSheet */ \"(app-pages-browser)/./app/user/trackSheets/v2/createTrackSheet.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _TrackSheetContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./TrackSheetContext */ \"(app-pages-browser)/./app/user/trackSheets/v2/TrackSheetContext.tsx\");\n/* harmony import */ var _ClientSelectPage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ClientSelectPage */ \"(app-pages-browser)/./app/user/trackSheets/v2/ClientSelectPage.tsx\");\n/* harmony import */ var _components_sidebar_Sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/sidebar/Sidebar */ \"(app-pages-browser)/./components/sidebar/Sidebar.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./components/ui/sidebar.tsx\");\n/* harmony import */ var _app_component_BreadCrumbs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/_component/BreadCrumbs */ \"(app-pages-browser)/./app/_component/BreadCrumbs.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst ManageWorkSheet = (param)=>{\n    let { permissions, client, carrier, associate, userData, actions } = param;\n    _s();\n    const [filterdata, setFilterData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [deleteData, setDeletedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [recievedFDate, setRecievedFDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [recievedTDate, setRecievedTDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [invoiceFDate, setInvoiceFDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [invoiceTDate, setInvoiceTDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [shipmentFDate, setShipmentFDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [shipmentTDate, setShipmentTDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [activeView, setActiveView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"view\"); // Default to view mode\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TrackSheetContext__WEBPACK_IMPORTED_MODULE_4__.TrackSheetContext.Provider, {\n        value: {\n            filterdata,\n            setFilterData,\n            deleteData,\n            setDeletedData,\n            recievedFDate,\n            recievedTDate,\n            setRecievedFDate,\n            setRecievedTDate,\n            invoiceFDate,\n            setInvoiceFDate,\n            invoiceTDate,\n            setInvoiceTDate,\n            shipmentFDate,\n            setShipmentFDate,\n            shipmentTDate,\n            setShipmentTDate\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_7__.SidebarProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex w-full min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sidebar_Sidebar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        permissions: permissions,\n                        profile: userData\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 w-full pl-3 overflow-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_BreadCrumbs__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    breadcrumblist: [\n                                        {\n                                            link: \"/user/trackSheets\",\n                                            name: \"TrackSheet\"\n                                        }\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-4 pl-3 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"default\",\n                                        onClick: ()=>setActiveView(\"view\"),\n                                        className: \"w-40 shadow-md rounded-xl text-base transition-all duration-200 \".concat(activeView === \"view\" ? \"bg-neutral-800 hover:bg-neutral-900 text-white\" : \"bg-white text-neutral-800 border border-neutral-300 hover:bg-neutral-100\"),\n                                        children: \"View TrackSheet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"default\",\n                                        onClick: ()=>setActiveView(\"create\"),\n                                        className: \"w-40 shadow-md rounded-xl text-base transition-all duration-200 \".concat(activeView === \"create\" ? \"bg-neutral-800 hover:bg-neutral-900 text-white\" : \"bg-white text-neutral-800 border border-neutral-300 hover:bg-neutral-100\"),\n                                        children: \"Create TrackSheet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full animate-in fade-in duration-500 rounded-2xl shadow-sm  dark:bg-gray-800 p-1\",\n                                children: activeView === \"create\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_createTrackSheet__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    client: client,\n                                    carrier: carrier,\n                                    associate: associate,\n                                    userData: userData\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClientSelectPage__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    permissions: actions,\n                                    client: client\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                lineNumber: 52,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ManageWorkSheet, \"+KVmLnuhHPupsM9QIHJko75BB/g=\");\n_c = ManageWorkSheet;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ManageWorkSheet);\nvar _c;\n$RefreshReg$(_c, \"ManageWorkSheet\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/trackSheets/v2/ManageTrackSheet.tsx\n"));

/***/ })

});