"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCurrentUserProfile = exports.viewSession = exports.viewUser = exports.viewCorporation = void 0;
const helpers_1 = require("../../utils/helpers");
const viewCorporation = async (req, res) => {
    try {
        const data = await prisma.corporation.findMany();
        if (data) {
            return res.status(200).json(data);
        }
        return res.status(400).json([]);
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.viewCorporation = viewCorporation;
const viewUser = async (req, res) => {
    try {
        const { page, pageSize, Username, FirstName, LastName, Email, Role, Manager, Title, } = req.query;
        const take = Number(pageSize);
        const skip = (Number(page) - 1) * Number(pageSize);
        const searchConditions = [];
        if (Username) {
            const userList = Username.split(",").map((item) => item.trim());
            searchConditions.push({
                OR: userList.map((user) => ({
                    username: {
                        contains: user,
                        mode: "insensitive",
                    },
                })),
            });
        }
        if (FirstName) {
            const firstNameList = FirstName.split(",").map((item) => item.trim());
            searchConditions.push({
                OR: firstNameList.map((firstName) => ({
                    firstName: {
                        contains: firstName,
                        mode: "insensitive",
                    },
                })),
            });
        }
        if (LastName) {
            const lastNameList = LastName.split(",").map((item) => item.trim());
            searchConditions.push({
                OR: lastNameList.map((lastName) => ({
                    lastName: {
                        contains: lastName,
                        mode: "insensitive",
                    },
                })),
            });
        }
        if (Email) {
            const emailList = Email.split(",").map((item) => item.trim());
            searchConditions.push({
                OR: emailList.map((email) => ({
                    email: {
                        contains: email,
                        mode: "insensitive",
                    },
                })),
            });
        }
        if (Role) {
            const roleList = Role.split(",").map((item) => item.trim());
            searchConditions.push({
                OR: roleList.map((role) => ({
                    role: {
                        name: {
                            contains: role,
                            mode: "insensitive",
                        },
                    },
                })),
            });
        }
        if (Manager) {
            const managerList = Manager.split(",").map((item) => item.trim().toLowerCase());
            // First find all users whose username matches any of the search terms
            const parentUsers = await prisma.user.findMany({
                where: {
                    OR: managerList.map((manager) => ({
                        username: {
                            contains: manager,
                            mode: "insensitive",
                        },
                    })),
                },
            });
            // Search for users who either:
            // 1. Have any of the search terms in their username
            // 2. Have a parent (manager) whose username contains any of the search terms
            searchConditions.push({
                OR: [
                    {
                        OR: managerList.map((manager) => ({
                            username: {
                                contains: manager,
                                mode: "insensitive",
                            },
                        })),
                    },
                    {
                        parent_id: {
                            in: parentUsers.map((p) => p.id),
                        },
                    },
                ],
            });
        }
        if (Title) {
            const titleList = Title.split(",").map((item) => item.trim());
            // First get the levels that match the titles
            const matchingTitles = await prisma.userTitle.findMany({
                where: {
                    OR: titleList.map((title) => ({
                        title: {
                            contains: title,
                            mode: "insensitive",
                        },
                    })),
                },
                select: {
                    level: true,
                },
            });
            const levels = matchingTitles
                .map((t) => t.level)
                .filter((l) => l !== null);
            if (levels.length > 0) {
                searchConditions.push({
                    level: {
                        in: levels,
                    },
                });
            }
        }
        const whereClause = {};
        if (searchConditions.length > 0) {
            whereClause.AND = searchConditions;
        }
        const data = await prisma.user.findMany({
            where: whereClause,
            take: page ? take : undefined,
            skip: page ? skip : undefined,
            include: {
                role: true,
                userClients: {
                    include: {
                        client: true,
                    },
                },
            },
            orderBy: { id: "desc" },
        });
        const datalength = await prisma.user.count({
            where: whereClause,
        });
        if (data.length > 0) {
            return res.status(200).json({ data, datalength });
        }
        return res.status(400).json([]);
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.viewUser = viewUser;
const viewSession = async (req, res) => {
    try {
        const data = await prisma.session.findMany({
            orderBy: { user_id: "desc" },
        });
        if (data) {
            return res.status(200).json(data);
        }
        return res.status(400).json([]);
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.viewSession = viewSession;
const getCurrentUserProfile = async (req, res) => {
    try {
        const userId = req.user_id;
        const TracksheetUser = req.query.TracksheetUser;
        if (!userId) {
            return res.status(200).json({
                success: true,
                message: "No user profile available for corporations.",
            });
        }
        const select = TracksheetUser === "true"
            ? {
                firstName: true,
                lastName: true,
                username: true,
                role: {
                    select: {
                        role_permission: {
                            select: {
                                permission: {
                                    select: {
                                        module: true,
                                        action: true,
                                    },
                                },
                            },
                        },
                    },
                },
            }
            : {
                id: true,
                username: true,
                firstName: true,
                lastName: true,
                email: true,
                session: true,
                parent_id: true,
                level: true,
                userClients: {
                    include: { client: true },
                },
                role: {
                    select: {
                        name: true,
                        role_permission: {
                            select: {
                                permission: {
                                    select: {
                                        module: true,
                                        action: true,
                                    },
                                },
                            },
                        },
                    },
                },
                created_at: true,
            };
        const data = await prisma.user.findUnique({
            where: {
                id: userId,
            },
            select,
        });
        if (data) {
            return res.status(200).json(data);
        }
        //  (res,"data")
        return res.status(400).json([]);
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.getCurrentUserProfile = getCurrentUserProfile;
//# sourceMappingURL=view.js.map