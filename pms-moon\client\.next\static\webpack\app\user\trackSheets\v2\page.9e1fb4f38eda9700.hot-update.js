"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/trackSheets/v2/page",{

/***/ "(app-pages-browser)/./app/user/trackSheets/v2/ManageTrackSheet.tsx":
/*!******************************************************!*\
  !*** ./app/user/trackSheets/v2/ManageTrackSheet.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _createTrackSheet__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./createTrackSheet */ \"(app-pages-browser)/./app/user/trackSheets/v2/createTrackSheet.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _TrackSheetContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./TrackSheetContext */ \"(app-pages-browser)/./app/user/trackSheets/v2/TrackSheetContext.tsx\");\n/* harmony import */ var _ClientSelectPage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ClientSelectPage */ \"(app-pages-browser)/./app/user/trackSheets/v2/ClientSelectPage.tsx\");\n/* harmony import */ var _components_sidebar_Sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/sidebar/Sidebar */ \"(app-pages-browser)/./components/sidebar/Sidebar.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./components/ui/sidebar.tsx\");\n/* harmony import */ var _app_component_BreadCrumbs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/_component/BreadCrumbs */ \"(app-pages-browser)/./app/_component/BreadCrumbs.tsx\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./components/ui/form.tsx\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/_component/SearchSelect */ \"(app-pages-browser)/./app/_component/SearchSelect.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst ManageWorkSheet = (param)=>{\n    let { permissions, client, carrier, associate, userData, actions } = param;\n    _s();\n    const [filterdata, setFilterData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [deleteData, setDeletedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [recievedFDate, setRecievedFDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [recievedTDate, setRecievedTDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [invoiceFDate, setInvoiceFDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [invoiceTDate, setInvoiceTDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [shipmentFDate, setShipmentFDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [shipmentTDate, setShipmentTDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [activeView, setActiveView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"view\"); // Default to view mode\n    // Add these states for the selection form\n    const [showFullForm, setShowFullForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [initialAssociateId, setInitialAssociateId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [initialClientId, setInitialClientId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const selectionForm = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_11__.useForm)({\n        defaultValues: {\n            associateId: \"\",\n            clientId: \"\"\n        }\n    });\n    const associateOptions = associate === null || associate === void 0 ? void 0 : associate.map((a)=>{\n        var _a_id;\n        return {\n            value: (_a_id = a.id) === null || _a_id === void 0 ? void 0 : _a_id.toString(),\n            label: a.name,\n            name: a.name\n        };\n    });\n    const getFilteredClientOptions = ()=>{\n        if (!initialAssociateId) {\n            return (client === null || client === void 0 ? void 0 : client.map((c)=>{\n                var _c_id;\n                return {\n                    value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n                    label: c.client_name,\n                    name: c.client_name\n                };\n            })) || [];\n        }\n        const filteredClients = (client === null || client === void 0 ? void 0 : client.filter((c)=>{\n            var _c_associateId;\n            return ((_c_associateId = c.associateId) === null || _c_associateId === void 0 ? void 0 : _c_associateId.toString()) === initialAssociateId;\n        })) || [];\n        return filteredClients.map((c)=>{\n            var _c_id;\n            return {\n                value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n                label: c.client_name,\n                name: c.client_name\n            };\n        });\n    };\n    const clientOptions = getFilteredClientOptions();\n    const validateClientForAssociate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((associateId, currentClientId)=>{\n        if (associateId && currentClientId) {\n            var _currentClient_associateId;\n            const currentClient = client === null || client === void 0 ? void 0 : client.find((c)=>{\n                var _c_id;\n                return ((_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString()) === currentClientId;\n            });\n            if (currentClient && ((_currentClient_associateId = currentClient.associateId) === null || _currentClient_associateId === void 0 ? void 0 : _currentClient_associateId.toString()) !== associateId) {\n                selectionForm.setValue(\"clientId\", \"\");\n                setInitialClientId(\"\");\n                return false;\n            }\n        }\n        return true;\n    }, [\n        client,\n        selectionForm\n    ]);\n    const clearEntrySpecificClients = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n    // This is a placeholder - the actual implementation would be in CreateTrackSheet\n    // We'll pass this as a prop to CreateTrackSheet\n    }, []);\n    const handleInitialSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((associateId, clientId)=>{\n        // This is a placeholder - the actual implementation would be in CreateTrackSheet\n        // We'll pass these values as props to CreateTrackSheet\n        setShowFullForm(true);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TrackSheetContext__WEBPACK_IMPORTED_MODULE_4__.TrackSheetContext.Provider, {\n        value: {\n            filterdata,\n            setFilterData,\n            deleteData,\n            setDeletedData,\n            recievedFDate,\n            recievedTDate,\n            setRecievedFDate,\n            setRecievedTDate,\n            invoiceFDate,\n            setInvoiceFDate,\n            invoiceTDate,\n            setInvoiceTDate,\n            shipmentFDate,\n            setShipmentFDate,\n            shipmentTDate,\n            setShipmentTDate\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_7__.SidebarProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex w-full min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sidebar_Sidebar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        permissions: permissions,\n                        profile: userData\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 w-full pl-3 overflow-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_BreadCrumbs__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    breadcrumblist: [\n                                        {\n                                            link: \"/user/trackSheets\",\n                                            name: \"TrackSheet\"\n                                        }\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4 pl-3 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"default\",\n                                        onClick: ()=>setActiveView(\"view\"),\n                                        className: \"w-40 shadow-md rounded-xl text-base transition-all duration-200 \".concat(activeView === \"view\" ? \"bg-neutral-800 hover:bg-neutral-900 text-white\" : \"bg-white text-neutral-800 border border-neutral-300 hover:bg-neutral-100\"),\n                                        children: \"View TrackSheet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"default\",\n                                        onClick: ()=>setActiveView(\"create\"),\n                                        className: \"w-40 shadow-md rounded-xl text-base transition-all duration-200 \".concat(activeView === \"create\" ? \"bg-neutral-800 hover:bg-neutral-900 text-white\" : \"bg-white text-neutral-800 border border-neutral-300 hover:bg-neutral-100\"),\n                                        children: \"Create TrackSheet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    activeView === \"create\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 bg-white rounded-lg shadow-sm border border-gray-200 p-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_9__.Form, {\n                                            ...selectionForm,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            form: selectionForm,\n                                                            name: \"associateId\",\n                                                            label: \"Select Associate\",\n                                                            placeholder: \"Search Associate...\",\n                                                            isRequired: true,\n                                                            options: associateOptions || [],\n                                                            onValueChange: (value)=>{\n                                                                setInitialAssociateId(value);\n                                                                if (value && initialClientId) {\n                                                                    validateClientForAssociate(value, initialClientId);\n                                                                } else {\n                                                                    setInitialClientId(\"\");\n                                                                    selectionForm.setValue(\"clientId\", \"\");\n                                                                }\n                                                                setShowFullForm(false);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            form: selectionForm,\n                                                            name: \"clientId\",\n                                                            label: \"Select Client\",\n                                                            placeholder: \"Search Client...\",\n                                                            isRequired: true,\n                                                            disabled: !initialAssociateId,\n                                                            options: clientOptions || [],\n                                                            onValueChange: (value)=>{\n                                                                setInitialClientId(value);\n                                                                if (showFullForm) {\n                                                                    clearEntrySpecificClients();\n                                                                }\n                                                                if (value && initialAssociateId) {\n                                                                    setTimeout(()=>{\n                                                                        handleInitialSelection(initialAssociateId, value);\n                                                                    }, 100);\n                                                                } else {\n                                                                    setShowFullForm(false);\n                                                                }\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full animate-in fade-in duration-500 rounded-2xl shadow-sm dark:bg-gray-800 p-1\",\n                                children: activeView === \"create\" && initialAssociateId && initialClientId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_createTrackSheet__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    client: client,\n                                    carrier: carrier,\n                                    associate: associate,\n                                    userData: userData,\n                                    initialAssociateId: initialAssociateId,\n                                    initialClientId: initialClientId\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 17\n                                }, undefined) : activeView === \"view\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClientSelectPage__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    permissions: actions,\n                                    client: client\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 17\n                                }, undefined) : null\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                lineNumber: 119,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n            lineNumber: 118,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ManageWorkSheet, \"N8CE5J0ob4eJypEFhc5HP89xZbQ=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_11__.useForm\n    ];\n});\n_c = ManageWorkSheet;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ManageWorkSheet);\nvar _c;\n$RefreshReg$(_c, \"ManageWorkSheet\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/trackSheets/v2/ManageTrackSheet.tsx\n"));

/***/ })

});