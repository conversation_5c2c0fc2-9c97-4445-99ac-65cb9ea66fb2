"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/trackSheets/v2/page",{

/***/ "(app-pages-browser)/./app/user/trackSheets/v2/createTrackSheet.tsx":
/*!******************************************************!*\
  !*** ./app/user/trackSheets/v2/createTrackSheet.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./components/ui/form.tsx\");\n/* harmony import */ var _app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/_component/FormInput */ \"(app-pages-browser)/./app/_component/FormInput.tsx\");\n/* harmony import */ var _app_component_FormCheckboxGroup__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/_component/FormCheckboxGroup */ \"(app-pages-browser)/./app/_component/FormCheckboxGroup.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-minus.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-plus.js\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/helpers */ \"(app-pages-browser)/./lib/helpers.ts\");\n/* harmony import */ var _lib_routePath__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/routePath */ \"(app-pages-browser)/./lib/routePath.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var _app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/_component/SearchSelect */ \"(app-pages-browser)/./app/_component/SearchSelect.tsx\");\n/* harmony import */ var _app_component_PageInput__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/_component/PageInput */ \"(app-pages-browser)/./app/_component/PageInput.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./components/ui/tooltip.tsx\");\n/* harmony import */ var _LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./LegrandDetailsComponent */ \"(app-pages-browser)/./app/user/trackSheets/v2/LegrandDetailsComponent.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst validateFtpPageFormat = (value)=>{\n    if (!value || value.trim() === \"\") return false;\n    const ftpPageRegex = /^(\\d+)\\s+of\\s+(\\d+)$/i;\n    const match = value.match(ftpPageRegex);\n    if (!match) return false;\n    const currentPage = parseInt(match[1], 10);\n    const totalPages = parseInt(match[2], 10);\n    return currentPage > 0 && totalPages > 0 && currentPage <= totalPages;\n};\nconst trackSheetSchema = zod__WEBPACK_IMPORTED_MODULE_15__.z.object({\n    clientId: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Client is required\"),\n    entries: zod__WEBPACK_IMPORTED_MODULE_15__.z.array(zod__WEBPACK_IMPORTED_MODULE_15__.z.object({\n        company: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Company is required\"),\n        division: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        invoice: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Invoice is required\"),\n        masterInvoice: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        bol: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"BOL is required\"),\n        invoiceDate: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Invoice date is required\"),\n        receivedDate: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Received date is required\"),\n        shipmentDate: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Shipment date is required\"),\n        carrierName: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Carrier name is required\"),\n        invoiceStatus: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Invoice status is required\"),\n        manualMatching: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Manual matching is required\"),\n        invoiceType: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Invoice type is required\"),\n        currency: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Currency is required\"),\n        qtyShipped: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Quantity shipped is required\"),\n        weightUnitName: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Weight unit is required\"),\n        quantityBilledText: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        invoiceTotal: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Invoice total is required\"),\n        savings: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        ftpFileName: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"FTP File Name is required\"),\n        ftpPage: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"FTP Page is required\").refine((value)=>validateFtpPageFormat(value), (value)=>{\n            if (!value || value.trim() === \"\") {\n                return {\n                    message: \"FTP Page is required\"\n                };\n            }\n            const ftpPageRegex = /^(\\d+)\\s+of\\s+(\\d+)$/i;\n            const match = value.match(ftpPageRegex);\n            if (!match) {\n                return {\n                    message: \"\"\n                };\n            }\n            const currentPage = parseInt(match[1], 10);\n            const totalPages = parseInt(match[2], 10);\n            if (currentPage <= 0 || totalPages <= 0) {\n                return {\n                    message: \"Page numbers must be positive (greater than 0)\"\n                };\n            }\n            if (currentPage > totalPages) {\n                return {\n                    message: \"Please enter a page number between \".concat(totalPages, \" and \").concat(currentPage, \" \")\n                };\n            }\n            return {\n                message: \"Invalid page format\"\n            };\n        }),\n        docAvailable: zod__WEBPACK_IMPORTED_MODULE_15__.z.array(zod__WEBPACK_IMPORTED_MODULE_15__.z.string()).optional().default([]),\n        notes: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        mistake: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        legrandAlias: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        legrandCompanyName: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        legrandAddress: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        legrandZipcode: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        shipperAlias: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        shipperAddress: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        shipperZipcode: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        consigneeAlias: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        consigneeAddress: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        consigneeZipcode: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        billtoAlias: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        billtoAddress: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        billtoZipcode: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        customFields: zod__WEBPACK_IMPORTED_MODULE_15__.z.array(zod__WEBPACK_IMPORTED_MODULE_15__.z.object({\n            id: zod__WEBPACK_IMPORTED_MODULE_15__.z.string(),\n            name: zod__WEBPACK_IMPORTED_MODULE_15__.z.string(),\n            type: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n            value: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional()\n        })).default([])\n    }))\n});\nconst CreateTrackSheet = (param)=>{\n    let { client, carrier, associate, userData, initialAssociateId, initialClientId, showFullForm } = param;\n    _s();\n    const companyFieldRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const [customFields, setCustomFields] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [generatedFilenames, setGeneratedFilenames] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filenameValidation, setFilenameValidation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [missingFields, setMissingFields] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [legrandData, setLegrandData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [customFieldsRefresh, setCustomFieldsRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Remove these states as they're now passed as props\n    // const [showFullForm, setShowFullForm] = useState(false);\n    // const [initialAssociateId, setInitialAssociateId] = useState(\"\");\n    // const [initialClientId, setInitialClientId] = useState(\"\");\n    // Remove the selectionForm as it's now in the parent component\n    const associateOptions = associate === null || associate === void 0 ? void 0 : associate.map((a)=>{\n        var _a_id;\n        return {\n            value: (_a_id = a.id) === null || _a_id === void 0 ? void 0 : _a_id.toString(),\n            label: a.name,\n            name: a.name\n        };\n    });\n    const carrierOptions = carrier === null || carrier === void 0 ? void 0 : carrier.map((c)=>{\n        var _c_id;\n        return {\n            value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n            label: c.name\n        };\n    });\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(trackSheetSchema),\n        defaultValues: {\n            associateId: \"\",\n            clientId: \"\",\n            entries: [\n                {\n                    company: \"\",\n                    division: \"\",\n                    invoice: \"\",\n                    masterInvoice: \"\",\n                    bol: \"\",\n                    invoiceDate: new Date().toISOString().split(\"T\")[0],\n                    receivedDate: new Date().toISOString().split(\"T\")[0],\n                    shipmentDate: new Date().toISOString().split(\"T\")[0],\n                    carrierName: \"\",\n                    invoiceStatus: \"\",\n                    manualMatching: \"\",\n                    invoiceType: \"\",\n                    currency: \"\",\n                    qtyShipped: \"\",\n                    weightUnitName: \"\",\n                    quantityBilledText: \"\",\n                    invoiceTotal: \"\",\n                    savings: \"\",\n                    ftpFileName: \"\",\n                    ftpPage: \"\",\n                    docAvailable: [],\n                    notes: \"\",\n                    mistake: \"\",\n                    legrandAlias: \"\",\n                    legrandCompanyName: \"\",\n                    legrandAddress: \"\",\n                    legrandZipcode: \"\",\n                    shipperAlias: \"\",\n                    shipperAddress: \"\",\n                    shipperZipcode: \"\",\n                    consigneeAlias: \"\",\n                    consigneeAddress: \"\",\n                    consigneeZipcode: \"\",\n                    billtoAlias: \"\",\n                    billtoAddress: \"\",\n                    billtoZipcode: \"\",\n                    customFields: []\n                }\n            ]\n        }\n    });\n    const getFilteredClientOptions = ()=>{\n        if (!initialAssociateId) {\n            return (client === null || client === void 0 ? void 0 : client.map((c)=>{\n                var _c_id;\n                return {\n                    value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n                    label: c.client_name,\n                    name: c.client_name\n                };\n            })) || [];\n        }\n        const filteredClients = (client === null || client === void 0 ? void 0 : client.filter((c)=>{\n            var _c_associateId;\n            return ((_c_associateId = c.associateId) === null || _c_associateId === void 0 ? void 0 : _c_associateId.toString()) === initialAssociateId;\n        })) || [];\n        return filteredClients.map((c)=>{\n            var _c_id;\n            return {\n                value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n                label: c.client_name,\n                name: c.client_name\n            };\n        });\n    };\n    const clientOptions = getFilteredClientOptions();\n    const entries = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useWatch)({\n        control: form.control,\n        name: \"entries\"\n    });\n    const validateClientForAssociate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((associateId, currentClientId)=>{\n        if (associateId && currentClientId) {\n            var _currentClient_associateId;\n            const currentClient = client === null || client === void 0 ? void 0 : client.find((c)=>{\n                var _c_id;\n                return ((_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString()) === currentClientId;\n            });\n            if (currentClient && ((_currentClient_associateId = currentClient.associateId) === null || _currentClient_associateId === void 0 ? void 0 : _currentClient_associateId.toString()) !== associateId) {\n                form.setValue(\"clientId\", \"\");\n                setInitialClientId(\"\");\n                return false;\n            }\n        }\n        return true;\n    }, [\n        client,\n        form\n    ]);\n    const clearEntrySpecificClients = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const currentEntries = form.getValues(\"entries\") || [];\n        if (currentEntries.length > 0) {\n            const hasEntrySpecificClients = currentEntries.some((entry)=>entry.clientId);\n            if (hasEntrySpecificClients) {\n                const updatedEntries = currentEntries.map((entry)=>({\n                        ...entry,\n                        clientId: \"\"\n                    }));\n                form.setValue(\"entries\", updatedEntries);\n            }\n        }\n    }, [\n        form\n    ]);\n    const fetchLegrandData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            const response = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_7__.getAllData)(_lib_routePath__WEBPACK_IMPORTED_MODULE_8__.legrandMapping_routes.GET_LEGRAND_MAPPINGS);\n            if (response && Array.isArray(response)) {\n                setLegrandData(response);\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Error fetching LEGRAND mapping data:\", error);\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        fetchLegrandData();\n    }, [\n        fetchLegrandData\n    ]);\n    const handleLegrandDataChange = (entryIndex, businessUnit, divisionCode)=>{\n        form.setValue(\"entries.\".concat(entryIndex, \".company\"), businessUnit);\n        if (divisionCode) {\n            form.setValue(\"entries.\".concat(entryIndex, \".division\"), divisionCode);\n        } else {\n            form.setValue(\"entries.\".concat(entryIndex, \".division\"), \"\");\n        }\n    };\n    const fetchCustomFieldsForClient = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (clientId)=>{\n        if (!clientId) return [];\n        try {\n            const allCustomFieldsResponse = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_7__.getAllData)(\"\".concat(_lib_routePath__WEBPACK_IMPORTED_MODULE_8__.clientCustomFields_routes.GET_CLIENT_CUSTOM_FIELDS, \"/\").concat(clientId));\n            let customFieldsData = [];\n            if (allCustomFieldsResponse && allCustomFieldsResponse.custom_fields && allCustomFieldsResponse.custom_fields.length > 0) {\n                customFieldsData = allCustomFieldsResponse.custom_fields.map((field)=>{\n                    let autoFilledValue = \"\";\n                    if (field.type === \"AUTO\") {\n                        if (field.autoOption === \"DATE\") {\n                            autoFilledValue = new Date().toISOString().split(\"T\")[0];\n                        } else if (field.autoOption === \"USERNAME\") {\n                            autoFilledValue = (userData === null || userData === void 0 ? void 0 : userData.username) || \"\";\n                        }\n                    }\n                    return {\n                        id: field.id,\n                        name: field.name,\n                        type: field.type,\n                        autoOption: field.autoOption,\n                        value: autoFilledValue\n                    };\n                });\n            }\n            return customFieldsData;\n        } catch (error) {\n            return [];\n        }\n    }, [\n        userData\n    ]);\n    const { fields, append, remove } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useFieldArray)({\n        control: form.control,\n        name: \"entries\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        companyFieldRefs.current = companyFieldRefs.current.slice(0, fields.length);\n    }, [\n        fields.length\n    ]);\n    const generateFilename = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((entryIndex, formValues)=>{\n        try {\n            const entry = formValues.entries[entryIndex];\n            if (!entry) return {\n                filename: \"\",\n                isValid: false,\n                missing: [\n                    \"Entry data\"\n                ]\n            };\n            const missing = [];\n            const selectedAssociate = associate === null || associate === void 0 ? void 0 : associate.find((a)=>{\n                var _a_id;\n                return ((_a_id = a.id) === null || _a_id === void 0 ? void 0 : _a_id.toString()) === formValues.associateId;\n            });\n            const associateName = (selectedAssociate === null || selectedAssociate === void 0 ? void 0 : selectedAssociate.name) || \"\";\n            if (!associateName) {\n                missing.push(\"Associate\");\n            }\n            const entryClientId = entry.clientId || formValues.clientId;\n            const selectedClient = client === null || client === void 0 ? void 0 : client.find((c)=>{\n                var _c_id;\n                return ((_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString()) === entryClientId;\n            });\n            const clientName = (selectedClient === null || selectedClient === void 0 ? void 0 : selectedClient.client_name) || \"\";\n            if (!clientName) {\n                missing.push(\"Client\");\n            }\n            let carrierName = \"\";\n            if (entry.carrierName) {\n                const carrierOption = carrier === null || carrier === void 0 ? void 0 : carrier.find((c)=>{\n                    var _c_id;\n                    return ((_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString()) === entry.carrierName;\n                });\n                carrierName = (carrierOption === null || carrierOption === void 0 ? void 0 : carrierOption.name) || \"\";\n            }\n            if (!carrierName) {\n                missing.push(\"Carrier\");\n            }\n            const receivedDate = entry.receivedDate;\n            const invoiceDate = entry.invoiceDate;\n            const currentDate = new Date();\n            const year = currentDate.getFullYear().toString();\n            const month = currentDate.toLocaleString(\"default\", {\n                month: \"short\"\n            }).toUpperCase();\n            if (!invoiceDate) {\n                missing.push(\"Invoice Date\");\n            }\n            let receivedDateStr = \"\";\n            if (receivedDate) {\n                const date = new Date(receivedDate);\n                receivedDateStr = date.toISOString().split(\"T\")[0];\n            } else {\n                missing.push(\"Received Date\");\n            }\n            const ftpFileName = entry.ftpFileName || \"\";\n            const baseFilename = ftpFileName ? ftpFileName.endsWith(\".pdf\") ? ftpFileName : \"\".concat(ftpFileName, \".pdf\") : \"\";\n            if (!baseFilename) {\n                missing.push(\"FTP File Name\");\n            }\n            const isValid = missing.length === 0;\n            const filename = isValid ? \"/\".concat(associateName, \"/\").concat(clientName, \"/CARRIERINVOICES/\").concat(carrierName, \"/\").concat(year, \"/\").concat(month, \"/\").concat(receivedDateStr, \"/\").concat(baseFilename) : \"\";\n            return {\n                filename,\n                isValid,\n                missing\n            };\n        } catch (error) {\n            return {\n                filename: \"\",\n                isValid: false,\n                missing: [\n                    \"Error generating filename\"\n                ]\n            };\n        }\n    }, [\n        client,\n        carrier,\n        associate\n    ]);\n    const handleCompanyAutoPopulation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((entryIndex, entryClientId)=>{\n        var _clientOptions_find;\n        const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n        const currentEntry = form.getValues(\"entries.\".concat(entryIndex));\n        if (entryClientName && entryClientName !== \"LEGRAND\") {\n            form.setValue(\"entries.\".concat(entryIndex, \".company\"), entryClientName);\n        } else if (entryClientName === \"LEGRAND\") {\n            const shipperAlias = currentEntry.shipperAlias;\n            const consigneeAlias = currentEntry.consigneeAlias;\n            const billtoAlias = currentEntry.billtoAlias;\n            const hasAnyLegrandData = shipperAlias || consigneeAlias || billtoAlias;\n            if (!hasAnyLegrandData && currentEntry.company !== \"\") {\n                form.setValue(\"entries.\".concat(entryIndex, \".company\"), \"\");\n            }\n        } else {\n            if (currentEntry.company !== \"\") {\n                form.setValue(\"entries.\".concat(entryIndex, \".company\"), \"\");\n            }\n        }\n    }, [\n        form,\n        clientOptions\n    ]);\n    const handleCustomFieldsFetch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (entryIndex, entryClientId)=>{\n        var _currentCustomFields_, _currentCustomFields_1;\n        if (!entryClientId) {\n            const currentCustomFields = form.getValues(\"entries.\".concat(entryIndex, \".customFields\"));\n            if (currentCustomFields && currentCustomFields.length > 0) {\n                form.setValue(\"entries.\".concat(entryIndex, \".customFields\"), []);\n            }\n            return;\n        }\n        const currentCustomFields = form.getValues(\"entries.\".concat(entryIndex, \".customFields\")) || [];\n        const hasEmptyAutoUsernameFields = currentCustomFields.some((field)=>field.type === \"AUTO\" && field.autoOption === \"USERNAME\" && !field.value && (userData === null || userData === void 0 ? void 0 : userData.username));\n        const shouldFetchCustomFields = currentCustomFields.length === 0 || currentCustomFields.length > 0 && !((_currentCustomFields_ = currentCustomFields[0]) === null || _currentCustomFields_ === void 0 ? void 0 : _currentCustomFields_.clientId) || ((_currentCustomFields_1 = currentCustomFields[0]) === null || _currentCustomFields_1 === void 0 ? void 0 : _currentCustomFields_1.clientId) !== entryClientId || hasEmptyAutoUsernameFields;\n        if (shouldFetchCustomFields) {\n            const customFieldsData = await fetchCustomFieldsForClient(entryClientId);\n            const fieldsWithClientId = customFieldsData.map((field)=>({\n                    ...field,\n                    clientId: entryClientId\n                }));\n            form.setValue(\"entries.\".concat(entryIndex, \".customFields\"), fieldsWithClientId);\n            setTimeout(()=>{\n                fieldsWithClientId.forEach((field, fieldIndex)=>{\n                    const fieldPath = \"entries.\".concat(entryIndex, \".customFields.\").concat(fieldIndex, \".value\");\n                    if (field.value) {\n                        form.setValue(fieldPath, field.value);\n                    }\n                });\n                setCustomFieldsRefresh((prev)=>prev + 1);\n            }, 100);\n        }\n    }, [\n        form,\n        fetchCustomFieldsForClient,\n        userData === null || userData === void 0 ? void 0 : userData.username\n    ]);\n    const updateFilenames = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const formValues = form.getValues();\n        const newFilenames = [];\n        const newValidation = [];\n        const newMissingFields = [];\n        if (formValues.entries && Array.isArray(formValues.entries)) {\n            formValues.entries.forEach((_, index)=>{\n                const { filename, isValid, missing } = generateFilename(index, formValues);\n                newFilenames[index] = filename;\n                newValidation[index] = isValid;\n                newMissingFields[index] = missing || [];\n            });\n        }\n        setGeneratedFilenames(newFilenames);\n        setFilenameValidation(newValidation);\n        setMissingFields(newMissingFields);\n    }, [\n        form,\n        generateFilename\n    ]);\n    const handleInitialSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((associateId, clientId)=>{\n        form.setValue(\"associateId\", associateId);\n        form.setValue(\"clientId\", clientId);\n        setTimeout(()=>{\n            handleCompanyAutoPopulation(0, clientId);\n            handleCustomFieldsFetch(0, clientId);\n            updateFilenames();\n        }, 50);\n        setShowFullForm(true);\n    }, [\n        form,\n        handleCompanyAutoPopulation,\n        handleCustomFieldsFetch,\n        updateFilenames\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        setTimeout(()=>{\n            updateFilenames();\n            const formValues = form.getValues();\n            if (formValues.entries && Array.isArray(formValues.entries)) {\n                formValues.entries.forEach((entry, index)=>{\n                    const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || (index === 0 ? formValues.clientId : \"\");\n                    if (entryClientId) {\n                        handleCustomFieldsFetch(index, entryClientId);\n                    }\n                });\n            }\n        }, 50);\n    }, [\n        updateFilenames,\n        handleCustomFieldsFetch,\n        form\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const subscription = form.watch((_, param)=>{\n            let { name } = param;\n            if (name && (name.includes(\"associateId\") || name.includes(\"clientId\") || name.includes(\"carrierName\") || name.includes(\"invoiceDate\") || name.includes(\"receivedDate\") || name.includes(\"ftpFileName\") || name.includes(\"company\") || name.includes(\"division\"))) {\n                updateFilenames();\n            }\n        });\n        return ()=>subscription.unsubscribe();\n    }, [\n        form,\n        updateFilenames\n    ]);\n    const onSubmit = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (values)=>{\n        try {\n            const currentFormValues = form.getValues();\n            const currentValidation = [];\n            const currentMissingFields = [];\n            const currentFilenames = [];\n            if (currentFormValues.entries && Array.isArray(currentFormValues.entries)) {\n                currentFormValues.entries.forEach((_, index)=>{\n                    const { filename, isValid, missing } = generateFilename(index, currentFormValues);\n                    currentValidation[index] = isValid;\n                    currentMissingFields[index] = missing || [];\n                    currentFilenames[index] = filename;\n                });\n            }\n            const allFilenamesValid = currentValidation.every((isValid)=>isValid);\n            if (!allFilenamesValid) {\n                const invalidEntries = currentValidation.map((isValid, index)=>({\n                        index,\n                        isValid,\n                        missing: currentMissingFields[index]\n                    })).filter((entry)=>!entry.isValid);\n                const errorDetails = invalidEntries.map((entry)=>\"Entry \".concat(entry.index + 1, \": \").concat(entry.missing.join(\", \"))).join(\" | \");\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Cannot submit: Missing fields - \".concat(errorDetails));\n                return;\n            }\n            const entries = values.entries.map((entry, index)=>{\n                var _entry_customFields;\n                return {\n                    company: entry.company,\n                    division: entry.division,\n                    invoice: entry.invoice,\n                    masterInvoice: entry.masterInvoice,\n                    bol: entry.bol,\n                    invoiceDate: entry.invoiceDate,\n                    receivedDate: entry.receivedDate,\n                    shipmentDate: entry.shipmentDate,\n                    carrierId: entry.carrierName,\n                    invoiceStatus: entry.invoiceStatus,\n                    manualMatching: entry.manualMatching,\n                    invoiceType: entry.invoiceType,\n                    currency: entry.currency,\n                    qtyShipped: entry.qtyShipped,\n                    weightUnitName: entry.weightUnitName,\n                    quantityBilledText: entry.quantityBilledText,\n                    invoiceTotal: entry.invoiceTotal,\n                    savings: entry.savings,\n                    ftpFileName: entry.ftpFileName,\n                    ftpPage: entry.ftpPage,\n                    docAvailable: entry.docAvailable,\n                    notes: entry.notes,\n                    mistake: entry.mistake,\n                    filePath: generatedFilenames[index],\n                    customFields: (_entry_customFields = entry.customFields) === null || _entry_customFields === void 0 ? void 0 : _entry_customFields.map((cf)=>({\n                            id: cf.id,\n                            value: cf.value\n                        }))\n                };\n            });\n            const formData = {\n                clientId: values.clientId,\n                entries: entries\n            };\n            const result = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_7__.formSubmit)(_lib_routePath__WEBPACK_IMPORTED_MODULE_8__.trackSheets_routes.CREATE_TRACK_SHEETS, \"POST\", formData);\n            if (result.success) {\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.success(\"All TrackSheets created successfully\");\n                form.reset();\n                setTimeout(()=>{\n                    handleInitialSelection(initialAssociateId, initialClientId);\n                }, 100);\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(result.message || \"Failed to create TrackSheets\");\n            }\n            router.refresh();\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"An error occurred while creating the TrackSheets\");\n        }\n    }, [\n        form,\n        router,\n        generateFilename,\n        initialAssociateId,\n        initialClientId,\n        handleInitialSelection,\n        generatedFilenames\n    ]);\n    const addNewEntry = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const newIndex = fields.length;\n        append({\n            clientId: initialClientId,\n            company: \"\",\n            division: \"\",\n            invoice: \"\",\n            masterInvoice: \"\",\n            bol: \"\",\n            invoiceDate: new Date().toISOString().split(\"T\")[0],\n            receivedDate: new Date().toISOString().split(\"T\")[0],\n            shipmentDate: new Date().toISOString().split(\"T\")[0],\n            carrierName: \"\",\n            invoiceStatus: \"\",\n            manualMatching: \"\",\n            invoiceType: \"\",\n            currency: \"\",\n            qtyShipped: \"\",\n            weightUnitName: \"\",\n            quantityBilledText: \"\",\n            invoiceTotal: \"\",\n            savings: \"\",\n            ftpFileName: \"\",\n            ftpPage: \"\",\n            docAvailable: [],\n            notes: \"\",\n            mistake: \"\",\n            legrandAlias: \"\",\n            legrandCompanyName: \"\",\n            legrandAddress: \"\",\n            legrandZipcode: \"\",\n            shipperAlias: \"\",\n            shipperAddress: \"\",\n            shipperZipcode: \"\",\n            consigneeAlias: \"\",\n            consigneeAddress: \"\",\n            consigneeZipcode: \"\",\n            billtoAlias: \"\",\n            billtoAddress: \"\",\n            billtoZipcode: \"\",\n            customFields: []\n        });\n        setTimeout(()=>{\n            handleCompanyAutoPopulation(newIndex, initialClientId);\n            handleCustomFieldsFetch(newIndex, initialClientId);\n            if (companyFieldRefs.current[newIndex]) {\n                var _companyFieldRefs_current_newIndex, _companyFieldRefs_current_newIndex1, _companyFieldRefs_current_newIndex2;\n                const inputElement = ((_companyFieldRefs_current_newIndex = companyFieldRefs.current[newIndex]) === null || _companyFieldRefs_current_newIndex === void 0 ? void 0 : _companyFieldRefs_current_newIndex.querySelector(\"input\")) || ((_companyFieldRefs_current_newIndex1 = companyFieldRefs.current[newIndex]) === null || _companyFieldRefs_current_newIndex1 === void 0 ? void 0 : _companyFieldRefs_current_newIndex1.querySelector(\"button\")) || ((_companyFieldRefs_current_newIndex2 = companyFieldRefs.current[newIndex]) === null || _companyFieldRefs_current_newIndex2 === void 0 ? void 0 : _companyFieldRefs_current_newIndex2.querySelector(\"select\"));\n                if (inputElement) {\n                    inputElement.focus();\n                    try {\n                        inputElement.click();\n                    } catch (e) {}\n                }\n            }\n            updateFilenames();\n        }, 200);\n    }, [\n        append,\n        fields.length,\n        updateFilenames,\n        initialClientId,\n        handleCompanyAutoPopulation,\n        handleCustomFieldsFetch\n    ]);\n    const handleFormKeyDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        if (e.ctrlKey && (e.key === \"s\" || e.key === \"S\")) {\n            e.preventDefault();\n            form.handleSubmit(onSubmit)();\n        } else if (e.shiftKey && e.key === \"Enter\") {\n            e.preventDefault();\n            addNewEntry();\n        } else if (e.key === \"Enter\" && !e.ctrlKey && !e.shiftKey && !e.altKey) {\n            const activeElement = document.activeElement;\n            const isSubmitButton = (activeElement === null || activeElement === void 0 ? void 0 : activeElement.getAttribute(\"type\")) === \"submit\";\n            if (isSubmitButton) {\n                e.preventDefault();\n                form.handleSubmit(onSubmit)();\n            }\n        }\n    }, [\n        form,\n        onSubmit,\n        addNewEntry\n    ]);\n    const removeEntry = (index)=>{\n        if (fields.length > 1) {\n            remove(index);\n        } else {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"You must have at least one entry\");\n        }\n    };\n    const getFilteredDivisionOptions = (company, entryIndex)=>{\n        if (!company || !legrandData.length) {\n            return [];\n        }\n        if (entryIndex !== undefined) {\n            var _formValues_entries, _clientOptions_find;\n            const formValues = form.getValues();\n            const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[entryIndex];\n            const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || (entryIndex === 0 ? formValues.clientId : \"\");\n            const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n            if (entryClientName === \"LEGRAND\") {\n                const shipperAlias = form.getValues(\"entries.\".concat(entryIndex, \".shipperAlias\"));\n                const consigneeAlias = form.getValues(\"entries.\".concat(entryIndex, \".consigneeAlias\"));\n                const billtoAlias = form.getValues(\"entries.\".concat(entryIndex, \".billtoAlias\"));\n                const currentAlias = shipperAlias || consigneeAlias || billtoAlias;\n                if (currentAlias) {\n                    const selectedData = legrandData.find((data)=>{\n                        const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n                        return uniqueKey === currentAlias;\n                    });\n                    if (selectedData) {\n                        const baseAliasName = selectedData.aliasShippingNames && selectedData.aliasShippingNames !== \"NONE\" ? selectedData.aliasShippingNames : selectedData.legalName;\n                        const sameAliasEntries = legrandData.filter((data)=>{\n                            const dataAliasName = data.aliasShippingNames && data.aliasShippingNames !== \"NONE\" ? data.aliasShippingNames : data.legalName;\n                            return dataAliasName === baseAliasName;\n                        });\n                        const allDivisions = [];\n                        sameAliasEntries.forEach((entry)=>{\n                            if (entry.customeCode) {\n                                if (entry.customeCode.includes(\"/\")) {\n                                    const splitDivisions = entry.customeCode.split(\"/\").map((d)=>d.trim());\n                                    allDivisions.push(...splitDivisions);\n                                } else {\n                                    allDivisions.push(entry.customeCode);\n                                }\n                            }\n                        });\n                        const uniqueDivisions = Array.from(new Set(allDivisions.filter((code)=>code)));\n                        if (uniqueDivisions.length > 1) {\n                            const contextDivisions = uniqueDivisions.sort().map((code)=>({\n                                    value: code,\n                                    label: code\n                                }));\n                            return contextDivisions;\n                        } else {}\n                    }\n                }\n            }\n        }\n        const allDivisions = [];\n        legrandData.filter((data)=>data.businessUnit === company && data.customeCode).forEach((data)=>{\n            if (data.customeCode.includes(\"/\")) {\n                const splitDivisions = data.customeCode.split(\"/\").map((d)=>d.trim());\n                allDivisions.push(...splitDivisions);\n            } else {\n                allDivisions.push(data.customeCode);\n            }\n        });\n        const divisions = Array.from(new Set(allDivisions.filter((code)=>code))).sort().map((code)=>({\n                value: code,\n                label: code\n            }));\n        return divisions;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full px-2 py-3\",\n                children: showFullForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_3__.Form, {\n                    ...form,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: form.handleSubmit(onSubmit),\n                        onKeyDown: handleFormKeyDown,\n                        className: \"space-y-3\",\n                        children: [\n                            fields.map((field, index)=>{\n                                var _missingFields_index;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2 bg-gray-100 rounded-md px-3 py-2 border border-gray-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-5 h-5 bg-gray-600 rounded-full flex items-center justify-center text-white font-semibold text-xs\",\n                                                        children: index + 1\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 974,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-sm font-semibold text-gray-900\",\n                                                        children: [\n                                                            \"Entry #\",\n                                                            index + 1\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 977,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                lineNumber: 973,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                            lineNumber: 972,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-3 pb-3 border-b border-gray-100\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-50 rounded-md p-2 mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 990,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-sm font-semibold text-gray-900\",\n                                                                        children: \"Client Information\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 991,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 989,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 mb-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                            className: \"mt-2\",\n                                                                            form: form,\n                                                                            label: \"FTP File Name\",\n                                                                            name: \"entries.\".concat(index, \".ftpFileName\"),\n                                                                            type: \"text\",\n                                                                            isRequired: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 998,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 997,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_PageInput__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"mt-2\",\n                                                                        form: form,\n                                                                        label: \"FTP Page\",\n                                                                        name: \"entries.\".concat(index, \".ftpPage\"),\n                                                                        isRequired: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1007,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1014,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    \" \"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 996,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            (()=>{\n                                                                var _formValues_entries, _clientOptions_find;\n                                                                const formValues = form.getValues();\n                                                                const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                return entryClientName === \"LEGRAND\";\n                                                            })() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-3 mb-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            form: form,\n                                                                            entryIndex: index,\n                                                                            onLegrandDataChange: handleLegrandDataChange,\n                                                                            blockTitle: \"Shipper\",\n                                                                            fieldPrefix: \"shipper\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1031,\n                                                                            columnNumber: 33\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            form: form,\n                                                                            entryIndex: index,\n                                                                            onLegrandDataChange: handleLegrandDataChange,\n                                                                            blockTitle: \"Consignee\",\n                                                                            fieldPrefix: \"consignee\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1038,\n                                                                            columnNumber: 33\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            form: form,\n                                                                            entryIndex: index,\n                                                                            onLegrandDataChange: handleLegrandDataChange,\n                                                                            blockTitle: \"Bill-to\",\n                                                                            fieldPrefix: \"billto\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1045,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1030,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1029,\n                                                                columnNumber: 29\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        ref: (el)=>{\n                                                                            companyFieldRefs.current[index] = el;\n                                                                        },\n                                                                        className: \"flex flex-col mb-1 [&_input]:h-10\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                            form: form,\n                                                                            label: \"Company\",\n                                                                            name: \"entries.\".concat(index, \".company\"),\n                                                                            type: \"text\",\n                                                                            disable: (()=>{\n                                                                                var _formValues_entries, _clientOptions_find;\n                                                                                const formValues = form.getValues();\n                                                                                const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                                const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                                const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                                return entryClientName === \"LEGRAND\";\n                                                                            })()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1064,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1058,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex flex-col\",\n                                                                        children: (()=>{\n                                                                            var _formValues_entries, _clientOptions_find, _entries_index;\n                                                                            const formValues = form.getValues();\n                                                                            const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                            const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                            const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                            const isLegrand = entryClientName === \"LEGRAND\";\n                                                                            return isLegrand ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                form: form,\n                                                                                name: \"entries.\".concat(index, \".division\"),\n                                                                                label: \"Division\",\n                                                                                placeholder: \"Search Division\",\n                                                                                disabled: false,\n                                                                                options: getFilteredDivisionOptions((entries === null || entries === void 0 ? void 0 : (_entries_index = entries[index]) === null || _entries_index === void 0 ? void 0 : _entries_index.company) || \"\", index)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1095,\n                                                                                columnNumber: 33\n                                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                form: form,\n                                                                                label: \"Division\",\n                                                                                name: \"entries.\".concat(index, \".division\"),\n                                                                                type: \"text\",\n                                                                                placeholder: \"Enter Division\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1107,\n                                                                                columnNumber: 33\n                                                                            }, undefined);\n                                                                        })()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1083,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex flex-col\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"mt-0\",\n                                                                            form: form,\n                                                                            name: \"entries.\".concat(index, \".carrierName\"),\n                                                                            label: \"Select Carrier\",\n                                                                            placeholder: \"Search Carrier\",\n                                                                            isRequired: true,\n                                                                            options: (carrierOptions === null || carrierOptions === void 0 ? void 0 : carrierOptions.filter((carrier)=>{\n                                                                                const currentEntries = form.getValues(\"entries\") || [];\n                                                                                const isSelectedInOtherEntries = currentEntries.some((entry, entryIndex)=>entryIndex !== index && entry.carrierName === carrier.value);\n                                                                                return !isSelectedInOtherEntries;\n                                                                            })) || [],\n                                                                            onValueChange: ()=>{\n                                                                                setTimeout(()=>updateFilenames(), 100);\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1118,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1117,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1057,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 988,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                    lineNumber: 986,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-3 pb-3 border-b border-gray-100\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-orange-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1149,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-sm font-semibold text-gray-900\",\n                                                                    children: \"Document Information\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1150,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1148,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Master Invoice\",\n                                                                    name: \"entries.\".concat(index, \".masterInvoice\"),\n                                                                    type: \"text\",\n                                                                    onBlur: (e)=>{\n                                                                        const masterInvoiceValue = e.target.value;\n                                                                        if (masterInvoiceValue) {\n                                                                            form.setValue(\"entries.\".concat(index, \".invoice\"), masterInvoiceValue);\n                                                                        }\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1155,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Invoice\",\n                                                                    name: \"entries.\".concat(index, \".invoice\"),\n                                                                    type: \"text\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1170,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"BOL\",\n                                                                    name: \"entries.\".concat(index, \".bol\"),\n                                                                    type: \"text\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1177,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1154,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Received Date\",\n                                                                    name: \"entries.\".concat(index, \".receivedDate\"),\n                                                                    type: \"date\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1186,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Invoice Date\",\n                                                                    name: \"entries.\".concat(index, \".invoiceDate\"),\n                                                                    type: \"date\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1193,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Shipment Date\",\n                                                                    name: \"entries.\".concat(index, \".shipmentDate\"),\n                                                                    type: \"date\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1200,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1185,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                    lineNumber: 1147,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4 pb-4 border-b border-gray-100\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1213,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-sm font-semibold text-gray-900\",\n                                                                    children: \"Financial & Shipment\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1214,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1212,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Invoice Total\",\n                                                                    name: \"entries.\".concat(index, \".invoiceTotal\"),\n                                                                    type: \"number\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1219,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    form: form,\n                                                                    name: \"entries.\".concat(index, \".currency\"),\n                                                                    label: \"Currency\",\n                                                                    placeholder: \"Search currency\",\n                                                                    isRequired: true,\n                                                                    options: [\n                                                                        {\n                                                                            value: \"USD\",\n                                                                            label: \"USD\"\n                                                                        },\n                                                                        {\n                                                                            value: \"CAD\",\n                                                                            label: \"CAD\"\n                                                                        },\n                                                                        {\n                                                                            value: \"EUR\",\n                                                                            label: \"EUR\"\n                                                                        }\n                                                                    ]\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1226,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Quantity Shipped\",\n                                                                    name: \"entries.\".concat(index, \".qtyShipped\"),\n                                                                    type: \"number\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1238,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Weight Unit\",\n                                                                    name: \"entries.\".concat(index, \".weightUnitName\"),\n                                                                    type: \"text\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1245,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1218,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 mt-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Savings\",\n                                                                    name: \"entries.\".concat(index, \".savings\"),\n                                                                    type: \"text\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1254,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Invoice Type\",\n                                                                    name: \"entries.\".concat(index, \".invoiceType\"),\n                                                                    type: \"text\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1260,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Quantity Billed Text\",\n                                                                    name: \"entries.\".concat(index, \".quantityBilledText\"),\n                                                                    type: \"text\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1267,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Invoice Status\",\n                                                                    name: \"entries.\".concat(index, \".invoiceStatus\"),\n                                                                    type: \"text\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1273,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1253,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                    lineNumber: 1211,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-gray-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1286,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-sm font-semibold text-gray-900\",\n                                                                    children: \"Additional Information\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1287,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1285,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Manual Matching\",\n                                                                    name: \"entries.\".concat(index, \".manualMatching\"),\n                                                                    type: \"text\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1292,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Notes\",\n                                                                    name: \"entries.\".concat(index, \".notes\"),\n                                                                    type: \"text\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1299,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Mistake\",\n                                                                    name: \"entries.\".concat(index, \".mistake\"),\n                                                                    type: \"text\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1305,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormCheckboxGroup__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Documents Available\",\n                                                                        name: \"entries.\".concat(index, \".docAvailable\"),\n                                                                        options: [\n                                                                            {\n                                                                                label: \"Invoice\",\n                                                                                value: \"Invoice\"\n                                                                            },\n                                                                            {\n                                                                                label: \"BOL\",\n                                                                                value: \"Bol\"\n                                                                            },\n                                                                            {\n                                                                                label: \"POD\",\n                                                                                value: \"Pod\"\n                                                                            },\n                                                                            {\n                                                                                label: \"Packages List\",\n                                                                                value: \"Packages List\"\n                                                                            }\n                                                                        ],\n                                                                        className: \"flex-row gap-2 text-xs\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1312,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1311,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1291,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                    lineNumber: 1284,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                (()=>{\n                                                    var _formValues_entries;\n                                                    const formValues = form.getValues();\n                                                    const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                    const customFields = (entry === null || entry === void 0 ? void 0 : entry.customFields) || [];\n                                                    return Array.isArray(customFields) && customFields.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"pt-3 border-t border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-purple-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1344,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-sm font-semibold text-gray-900\",\n                                                                        children: [\n                                                                            \"Custom Fields (\",\n                                                                            customFields.length,\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1345,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1343,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2\",\n                                                                children: customFields.map((cf, cfIdx)=>{\n                                                                    const fieldType = cf.type || \"TEXT\";\n                                                                    const isAutoField = fieldType === \"AUTO\";\n                                                                    const autoOption = cf.autoOption;\n                                                                    let inputType = \"text\";\n                                                                    if (fieldType === \"DATE\" || isAutoField && autoOption === \"DATE\") {\n                                                                        inputType = \"date\";\n                                                                    } else if (fieldType === \"NUMBER\") {\n                                                                        inputType = \"number\";\n                                                                    }\n                                                                    const fieldLabel = isAutoField ? \"\".concat(cf.name, \" (Auto - \").concat(autoOption, \")\") : cf.name;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: fieldLabel,\n                                                                        name: \"entries.\".concat(index, \".customFields.\").concat(cfIdx, \".value\"),\n                                                                        type: inputType,\n                                                                        className: \"w-full\",\n                                                                        disable: isAutoField\n                                                                    }, cf.id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1370,\n                                                                        columnNumber: 33\n                                                                    }, undefined);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1349,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, \"custom-fields-\".concat(index, \"-\").concat(customFieldsRefresh), true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1339,\n                                                        columnNumber: 25\n                                                    }, undefined) : null;\n                                                })(),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"pt-3 border-t border-gray-100 mt-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-end space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.Tooltip, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipTrigger, {\n                                                                        asChild: true,\n                                                                        tabIndex: -1,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-5 h-5 rounded-full flex items-center justify-center text-white font-bold text-xs cursor-help transition-colors duration-200 \".concat(filenameValidation[index] ? \"bg-green-500 hover:bg-green-600\" : \"bg-orange-500 hover:bg-orange-600\"),\n                                                                            tabIndex: -1,\n                                                                            role: \"button\",\n                                                                            \"aria-label\": \"Entry \".concat(index + 1, \" filename status\"),\n                                                                            children: \"!\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1392,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1391,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipContent, {\n                                                                        side: \"top\",\n                                                                        align: \"center\",\n                                                                        className: \"z-[9999]\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm max-w-md\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium mb-1\",\n                                                                                    children: [\n                                                                                        \"Entry #\",\n                                                                                        index + 1,\n                                                                                        \" Filename\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1411,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                filenameValidation[index] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"font-medium text-green-600 mb-2\",\n                                                                                            children: \"Filename Generated\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1416,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs font-mono break-all bg-gray-100 p-2 rounded text-black\",\n                                                                                            children: generatedFilenames[index]\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1419,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1415,\n                                                                                    columnNumber: 33\n                                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"font-medium text-orange-600 mb-1\",\n                                                                                            children: \"Please fill the form to generate filename\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1425,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-gray-600 mb-2\",\n                                                                                            children: \"Missing fields:\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1428,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                                            className: \"list-disc list-inside space-y-1\",\n                                                                                            children: (_missingFields_index = missingFields[index]) === null || _missingFields_index === void 0 ? void 0 : _missingFields_index.map((field, fieldIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                                    className: \"text-xs\",\n                                                                                                    children: field\n                                                                                                }, fieldIndex, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                                    lineNumber: 1434,\n                                                                                                    columnNumber: 41\n                                                                                                }, undefined))\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1431,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1424,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1410,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1405,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1390,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                type: \"button\",\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                className: \"h-7 w-7 p-0 hover:bg-red-50 hover:border-red-200\",\n                                                                onClick: ()=>removeEntry(index),\n                                                                disabled: fields.length <= 1,\n                                                                tabIndex: -1,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"h-3 w-3 text-red-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1458,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1449,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            index === fields.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.Tooltip, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipTrigger, {\n                                                                        asChild: true,\n                                                                        tabIndex: -1,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                            type: \"button\",\n                                                                            variant: \"outline\",\n                                                                            size: \"sm\",\n                                                                            className: \"h-7 w-7 p-0 hover:bg-green-50 hover:border-green-200\",\n                                                                            onClick: addNewEntry,\n                                                                            tabIndex: -1,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                className: \"h-3 w-3 text-green-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1471,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1463,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1462,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipContent, {\n                                                                        side: \"top\",\n                                                                        align: \"center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs\",\n                                                                            children: \"Add New Entry (Shift+Enter)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1475,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1474,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1461,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1388,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                    lineNumber: 1387,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                            lineNumber: 984,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, field.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                    lineNumber: 970,\n                                    columnNumber: 19\n                                }, undefined);\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        type: \"submit\",\n                                        className: \"px-6 py-2 rounded-lg font-medium transition-all duration-200 shadow-md hover:shadow-lg text-sm\",\n                                        children: \"Save\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                        lineNumber: 1490,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                    lineNumber: 1489,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                lineNumber: 1488,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                        lineNumber: 964,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                    lineNumber: 963,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                lineNumber: 958,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n            lineNumber: 957,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n        lineNumber: 956,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CreateTrackSheet, \"G9t5Zu03ZeKBM5unrApPUQQEs5g=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useWatch,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useFieldArray\n    ];\n});\n_c = CreateTrackSheet;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CreateTrackSheet);\nvar _c;\n$RefreshReg$(_c, \"CreateTrackSheet\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/trackSheets/v2/createTrackSheet.tsx\n"));

/***/ })

});