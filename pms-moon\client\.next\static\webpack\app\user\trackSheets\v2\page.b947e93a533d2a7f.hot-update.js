"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/trackSheets/v2/page",{

/***/ "(app-pages-browser)/./app/user/trackSheets/v2/createTrackSheet.tsx":
/*!******************************************************!*\
  !*** ./app/user/trackSheets/v2/createTrackSheet.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./components/ui/form.tsx\");\n/* harmony import */ var _app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/_component/FormInput */ \"(app-pages-browser)/./app/_component/FormInput.tsx\");\n/* harmony import */ var _app_component_FormCheckboxGroup__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/_component/FormCheckboxGroup */ \"(app-pages-browser)/./app/_component/FormCheckboxGroup.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-minus.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-plus.js\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/helpers */ \"(app-pages-browser)/./lib/helpers.ts\");\n/* harmony import */ var _lib_routePath__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/routePath */ \"(app-pages-browser)/./lib/routePath.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var _app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/_component/SearchSelect */ \"(app-pages-browser)/./app/_component/SearchSelect.tsx\");\n/* harmony import */ var _app_component_PageInput__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/_component/PageInput */ \"(app-pages-browser)/./app/_component/PageInput.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./components/ui/tooltip.tsx\");\n/* harmony import */ var _LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./LegrandDetailsComponent */ \"(app-pages-browser)/./app/user/trackSheets/v2/LegrandDetailsComponent.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst validateFtpPageFormat = (value)=>{\n    if (!value || value.trim() === \"\") return false;\n    const ftpPageRegex = /^(\\d+)\\s+of\\s+(\\d+)$/i;\n    const match = value.match(ftpPageRegex);\n    if (!match) return false;\n    const currentPage = parseInt(match[1], 10);\n    const totalPages = parseInt(match[2], 10);\n    return currentPage > 0 && totalPages > 0 && currentPage <= totalPages;\n};\nconst trackSheetSchema = zod__WEBPACK_IMPORTED_MODULE_15__.z.object({\n    clientId: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Client is required\"),\n    entries: zod__WEBPACK_IMPORTED_MODULE_15__.z.array(zod__WEBPACK_IMPORTED_MODULE_15__.z.object({\n        company: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Company is required\"),\n        division: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        invoice: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Invoice is required\"),\n        masterInvoice: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        bol: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"BOL is required\"),\n        invoiceDate: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Invoice date is required\"),\n        receivedDate: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Received date is required\"),\n        shipmentDate: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Shipment date is required\"),\n        carrierName: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Carrier name is required\"),\n        invoiceStatus: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Invoice status is required\"),\n        manualMatching: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Manual matching is required\"),\n        invoiceType: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Invoice type is required\"),\n        currency: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Currency is required\"),\n        qtyShipped: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Quantity shipped is required\"),\n        weightUnitName: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Weight unit is required\"),\n        quantityBilledText: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        invoiceTotal: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Invoice total is required\"),\n        savings: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        ftpFileName: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"FTP File Name is required\"),\n        ftpPage: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"FTP Page is required\").refine((value)=>validateFtpPageFormat(value), (value)=>{\n            if (!value || value.trim() === \"\") {\n                return {\n                    message: \"FTP Page is required\"\n                };\n            }\n            const ftpPageRegex = /^(\\d+)\\s+of\\s+(\\d+)$/i;\n            const match = value.match(ftpPageRegex);\n            if (!match) {\n                return {\n                    message: \"\"\n                };\n            }\n            const currentPage = parseInt(match[1], 10);\n            const totalPages = parseInt(match[2], 10);\n            if (currentPage <= 0 || totalPages <= 0) {\n                return {\n                    message: \"Page numbers must be positive (greater than 0)\"\n                };\n            }\n            if (currentPage > totalPages) {\n                return {\n                    message: \"Please enter a page number between \".concat(totalPages, \" and \").concat(currentPage, \" \")\n                };\n            }\n            return {\n                message: \"Invalid page format\"\n            };\n        }),\n        docAvailable: zod__WEBPACK_IMPORTED_MODULE_15__.z.array(zod__WEBPACK_IMPORTED_MODULE_15__.z.string()).optional().default([]),\n        notes: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        mistake: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        legrandAlias: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        legrandCompanyName: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        legrandAddress: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        legrandZipcode: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        shipperAlias: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        shipperAddress: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        shipperZipcode: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        consigneeAlias: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        consigneeAddress: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        consigneeZipcode: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        billtoAlias: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        billtoAddress: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        billtoZipcode: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        customFields: zod__WEBPACK_IMPORTED_MODULE_15__.z.array(zod__WEBPACK_IMPORTED_MODULE_15__.z.object({\n            id: zod__WEBPACK_IMPORTED_MODULE_15__.z.string(),\n            name: zod__WEBPACK_IMPORTED_MODULE_15__.z.string(),\n            type: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n            value: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional()\n        })).default([])\n    }))\n});\nconst CreateTrackSheet = (param)=>{\n    let { client, carrier, associate, userData, initialAssociateId, initialClientId, showFullForm, setShowFullForm// Add this prop\n     } = param;\n    _s();\n    const companyFieldRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const [customFields, setCustomFields] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [generatedFilenames, setGeneratedFilenames] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filenameValidation, setFilenameValidation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [missingFields, setMissingFields] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [legrandData, setLegrandData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [customFieldsRefresh, setCustomFieldsRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Remove these states as they're now passed as props\n    // const [showFullForm, setShowFullForm] = useState(false);\n    // const [initialAssociateId, setInitialAssociateId] = useState(\"\");\n    // const [initialClientId, setInitialClientId] = useState(\"\");\n    // Remove the selectionForm as it's now in the parent component\n    const associateOptions = associate === null || associate === void 0 ? void 0 : associate.map((a)=>{\n        var _a_id;\n        return {\n            value: (_a_id = a.id) === null || _a_id === void 0 ? void 0 : _a_id.toString(),\n            label: a.name,\n            name: a.name\n        };\n    });\n    const carrierOptions = carrier === null || carrier === void 0 ? void 0 : carrier.map((c)=>{\n        var _c_id;\n        return {\n            value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n            label: c.name\n        };\n    });\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(trackSheetSchema),\n        defaultValues: {\n            associateId: \"\",\n            clientId: \"\",\n            entries: [\n                {\n                    company: \"\",\n                    division: \"\",\n                    invoice: \"\",\n                    masterInvoice: \"\",\n                    bol: \"\",\n                    invoiceDate: new Date().toISOString().split(\"T\")[0],\n                    receivedDate: new Date().toISOString().split(\"T\")[0],\n                    shipmentDate: new Date().toISOString().split(\"T\")[0],\n                    carrierName: \"\",\n                    invoiceStatus: \"\",\n                    manualMatching: \"\",\n                    invoiceType: \"\",\n                    currency: \"\",\n                    qtyShipped: \"\",\n                    weightUnitName: \"\",\n                    quantityBilledText: \"\",\n                    invoiceTotal: \"\",\n                    savings: \"\",\n                    ftpFileName: \"\",\n                    ftpPage: \"\",\n                    docAvailable: [],\n                    notes: \"\",\n                    mistake: \"\",\n                    legrandAlias: \"\",\n                    legrandCompanyName: \"\",\n                    legrandAddress: \"\",\n                    legrandZipcode: \"\",\n                    shipperAlias: \"\",\n                    shipperAddress: \"\",\n                    shipperZipcode: \"\",\n                    consigneeAlias: \"\",\n                    consigneeAddress: \"\",\n                    consigneeZipcode: \"\",\n                    billtoAlias: \"\",\n                    billtoAddress: \"\",\n                    billtoZipcode: \"\",\n                    customFields: []\n                }\n            ]\n        }\n    });\n    const getFilteredClientOptions = ()=>{\n        if (!initialAssociateId) {\n            return (client === null || client === void 0 ? void 0 : client.map((c)=>{\n                var _c_id;\n                return {\n                    value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n                    label: c.client_name,\n                    name: c.client_name\n                };\n            })) || [];\n        }\n        const filteredClients = (client === null || client === void 0 ? void 0 : client.filter((c)=>{\n            var _c_associateId;\n            return ((_c_associateId = c.associateId) === null || _c_associateId === void 0 ? void 0 : _c_associateId.toString()) === initialAssociateId;\n        })) || [];\n        return filteredClients.map((c)=>{\n            var _c_id;\n            return {\n                value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n                label: c.client_name,\n                name: c.client_name\n            };\n        });\n    };\n    const clientOptions = getFilteredClientOptions();\n    const entries = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useWatch)({\n        control: form.control,\n        name: \"entries\"\n    });\n    const validateClientForAssociate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((associateId, currentClientId)=>{\n        if (associateId && currentClientId) {\n            var _currentClient_associateId;\n            const currentClient = client === null || client === void 0 ? void 0 : client.find((c)=>{\n                var _c_id;\n                return ((_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString()) === currentClientId;\n            });\n            if (currentClient && ((_currentClient_associateId = currentClient.associateId) === null || _currentClient_associateId === void 0 ? void 0 : _currentClient_associateId.toString()) !== associateId) {\n                form.setValue(\"clientId\", \"\");\n                // setInitialClientId(\"\"); // Removed as we can't modify props directly\n                return false;\n            }\n        }\n        return true;\n    }, [\n        client,\n        form\n    ]);\n    const clearEntrySpecificClients = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const currentEntries = form.getValues(\"entries\") || [];\n        if (currentEntries.length > 0) {\n            const hasEntrySpecificClients = currentEntries.some((entry)=>entry.clientId);\n            if (hasEntrySpecificClients) {\n                const updatedEntries = currentEntries.map((entry)=>({\n                        ...entry,\n                        clientId: \"\"\n                    }));\n                form.setValue(\"entries\", updatedEntries);\n            }\n        }\n    }, [\n        form\n    ]);\n    const fetchLegrandData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            const response = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_7__.getAllData)(_lib_routePath__WEBPACK_IMPORTED_MODULE_8__.legrandMapping_routes.GET_LEGRAND_MAPPINGS);\n            if (response && Array.isArray(response)) {\n                setLegrandData(response);\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Error fetching LEGRAND mapping data:\", error);\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        fetchLegrandData();\n    }, [\n        fetchLegrandData\n    ]);\n    const handleLegrandDataChange = (entryIndex, businessUnit, divisionCode)=>{\n        form.setValue(\"entries.\".concat(entryIndex, \".company\"), businessUnit);\n        if (divisionCode) {\n            form.setValue(\"entries.\".concat(entryIndex, \".division\"), divisionCode);\n        } else {\n            form.setValue(\"entries.\".concat(entryIndex, \".division\"), \"\");\n        }\n    };\n    const fetchCustomFieldsForClient = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (clientId)=>{\n        if (!clientId) return [];\n        try {\n            const allCustomFieldsResponse = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_7__.getAllData)(\"\".concat(_lib_routePath__WEBPACK_IMPORTED_MODULE_8__.clientCustomFields_routes.GET_CLIENT_CUSTOM_FIELDS, \"/\").concat(clientId));\n            let customFieldsData = [];\n            if (allCustomFieldsResponse && allCustomFieldsResponse.custom_fields && allCustomFieldsResponse.custom_fields.length > 0) {\n                customFieldsData = allCustomFieldsResponse.custom_fields.map((field)=>{\n                    let autoFilledValue = \"\";\n                    if (field.type === \"AUTO\") {\n                        if (field.autoOption === \"DATE\") {\n                            autoFilledValue = new Date().toISOString().split(\"T\")[0];\n                        } else if (field.autoOption === \"USERNAME\") {\n                            autoFilledValue = (userData === null || userData === void 0 ? void 0 : userData.username) || \"\";\n                        }\n                    }\n                    return {\n                        id: field.id,\n                        name: field.name,\n                        type: field.type,\n                        autoOption: field.autoOption,\n                        value: autoFilledValue\n                    };\n                });\n            }\n            return customFieldsData;\n        } catch (error) {\n            return [];\n        }\n    }, [\n        userData\n    ]);\n    const { fields, append, remove } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useFieldArray)({\n        control: form.control,\n        name: \"entries\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        companyFieldRefs.current = companyFieldRefs.current.slice(0, fields.length);\n    }, [\n        fields.length\n    ]);\n    const generateFilename = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((entryIndex, formValues)=>{\n        try {\n            const entry = formValues.entries[entryIndex];\n            if (!entry) return {\n                filename: \"\",\n                isValid: false,\n                missing: [\n                    \"Entry data\"\n                ]\n            };\n            const missing = [];\n            const selectedAssociate = associate === null || associate === void 0 ? void 0 : associate.find((a)=>{\n                var _a_id;\n                return ((_a_id = a.id) === null || _a_id === void 0 ? void 0 : _a_id.toString()) === formValues.associateId;\n            });\n            const associateName = (selectedAssociate === null || selectedAssociate === void 0 ? void 0 : selectedAssociate.name) || \"\";\n            if (!associateName) {\n                missing.push(\"Associate\");\n            }\n            const entryClientId = entry.clientId || formValues.clientId;\n            const selectedClient = client === null || client === void 0 ? void 0 : client.find((c)=>{\n                var _c_id;\n                return ((_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString()) === entryClientId;\n            });\n            const clientName = (selectedClient === null || selectedClient === void 0 ? void 0 : selectedClient.client_name) || \"\";\n            if (!clientName) {\n                missing.push(\"Client\");\n            }\n            let carrierName = \"\";\n            if (entry.carrierName) {\n                const carrierOption = carrier === null || carrier === void 0 ? void 0 : carrier.find((c)=>{\n                    var _c_id;\n                    return ((_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString()) === entry.carrierName;\n                });\n                carrierName = (carrierOption === null || carrierOption === void 0 ? void 0 : carrierOption.name) || \"\";\n            }\n            if (!carrierName) {\n                missing.push(\"Carrier\");\n            }\n            const receivedDate = entry.receivedDate;\n            const invoiceDate = entry.invoiceDate;\n            const currentDate = new Date();\n            const year = currentDate.getFullYear().toString();\n            const month = currentDate.toLocaleString(\"default\", {\n                month: \"short\"\n            }).toUpperCase();\n            if (!invoiceDate) {\n                missing.push(\"Invoice Date\");\n            }\n            let receivedDateStr = \"\";\n            if (receivedDate) {\n                const date = new Date(receivedDate);\n                receivedDateStr = date.toISOString().split(\"T\")[0];\n            } else {\n                missing.push(\"Received Date\");\n            }\n            const ftpFileName = entry.ftpFileName || \"\";\n            const baseFilename = ftpFileName ? ftpFileName.endsWith(\".pdf\") ? ftpFileName : \"\".concat(ftpFileName, \".pdf\") : \"\";\n            if (!baseFilename) {\n                missing.push(\"FTP File Name\");\n            }\n            const isValid = missing.length === 0;\n            const filename = isValid ? \"/\".concat(associateName, \"/\").concat(clientName, \"/CARRIERINVOICES/\").concat(carrierName, \"/\").concat(year, \"/\").concat(month, \"/\").concat(receivedDateStr, \"/\").concat(baseFilename) : \"\";\n            return {\n                filename,\n                isValid,\n                missing\n            };\n        } catch (error) {\n            return {\n                filename: \"\",\n                isValid: false,\n                missing: [\n                    \"Error generating filename\"\n                ]\n            };\n        }\n    }, [\n        client,\n        carrier,\n        associate\n    ]);\n    const handleCompanyAutoPopulation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((entryIndex, entryClientId)=>{\n        var _clientOptions_find;\n        const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n        const currentEntry = form.getValues(\"entries.\".concat(entryIndex));\n        if (entryClientName && entryClientName !== \"LEGRAND\") {\n            form.setValue(\"entries.\".concat(entryIndex, \".company\"), entryClientName);\n        } else if (entryClientName === \"LEGRAND\") {\n            const shipperAlias = currentEntry.shipperAlias;\n            const consigneeAlias = currentEntry.consigneeAlias;\n            const billtoAlias = currentEntry.billtoAlias;\n            const hasAnyLegrandData = shipperAlias || consigneeAlias || billtoAlias;\n            if (!hasAnyLegrandData && currentEntry.company !== \"\") {\n                form.setValue(\"entries.\".concat(entryIndex, \".company\"), \"\");\n            }\n        } else {\n            if (currentEntry.company !== \"\") {\n                form.setValue(\"entries.\".concat(entryIndex, \".company\"), \"\");\n            }\n        }\n    }, [\n        form,\n        clientOptions\n    ]);\n    const handleCustomFieldsFetch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (entryIndex, entryClientId)=>{\n        var _currentCustomFields_, _currentCustomFields_1;\n        if (!entryClientId) {\n            const currentCustomFields = form.getValues(\"entries.\".concat(entryIndex, \".customFields\"));\n            if (currentCustomFields && currentCustomFields.length > 0) {\n                form.setValue(\"entries.\".concat(entryIndex, \".customFields\"), []);\n            }\n            return;\n        }\n        const currentCustomFields = form.getValues(\"entries.\".concat(entryIndex, \".customFields\")) || [];\n        const hasEmptyAutoUsernameFields = currentCustomFields.some((field)=>field.type === \"AUTO\" && field.autoOption === \"USERNAME\" && !field.value && (userData === null || userData === void 0 ? void 0 : userData.username));\n        const shouldFetchCustomFields = currentCustomFields.length === 0 || currentCustomFields.length > 0 && !((_currentCustomFields_ = currentCustomFields[0]) === null || _currentCustomFields_ === void 0 ? void 0 : _currentCustomFields_.clientId) || ((_currentCustomFields_1 = currentCustomFields[0]) === null || _currentCustomFields_1 === void 0 ? void 0 : _currentCustomFields_1.clientId) !== entryClientId || hasEmptyAutoUsernameFields;\n        if (shouldFetchCustomFields) {\n            const customFieldsData = await fetchCustomFieldsForClient(entryClientId);\n            const fieldsWithClientId = customFieldsData.map((field)=>({\n                    ...field,\n                    clientId: entryClientId\n                }));\n            form.setValue(\"entries.\".concat(entryIndex, \".customFields\"), fieldsWithClientId);\n            setTimeout(()=>{\n                fieldsWithClientId.forEach((field, fieldIndex)=>{\n                    const fieldPath = \"entries.\".concat(entryIndex, \".customFields.\").concat(fieldIndex, \".value\");\n                    if (field.value) {\n                        form.setValue(fieldPath, field.value);\n                    }\n                });\n                setCustomFieldsRefresh((prev)=>prev + 1);\n            }, 100);\n        }\n    }, [\n        form,\n        fetchCustomFieldsForClient,\n        userData === null || userData === void 0 ? void 0 : userData.username\n    ]);\n    const updateFilenames = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const formValues = form.getValues();\n        const newFilenames = [];\n        const newValidation = [];\n        const newMissingFields = [];\n        if (formValues.entries && Array.isArray(formValues.entries)) {\n            formValues.entries.forEach((_, index)=>{\n                const { filename, isValid, missing } = generateFilename(index, formValues);\n                newFilenames[index] = filename;\n                newValidation[index] = isValid;\n                newMissingFields[index] = missing || [];\n            });\n        }\n        setGeneratedFilenames(newFilenames);\n        setFilenameValidation(newValidation);\n        setMissingFields(newMissingFields);\n    }, [\n        form,\n        generateFilename\n    ]);\n    const handleInitialSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((associateId, clientId)=>{\n        form.setValue(\"associateId\", associateId);\n        form.setValue(\"clientId\", clientId);\n        setTimeout(()=>{\n            handleCompanyAutoPopulation(0, clientId);\n            handleCustomFieldsFetch(0, clientId);\n            updateFilenames();\n        }, 50);\n        setShowFullForm(true);\n    }, [\n        form,\n        handleCompanyAutoPopulation,\n        handleCustomFieldsFetch,\n        updateFilenames,\n        setShowFullForm\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        setTimeout(()=>{\n            updateFilenames();\n            const formValues = form.getValues();\n            if (formValues.entries && Array.isArray(formValues.entries)) {\n                formValues.entries.forEach((entry, index)=>{\n                    const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || (index === 0 ? formValues.clientId : \"\");\n                    if (entryClientId) {\n                        handleCustomFieldsFetch(index, entryClientId);\n                    }\n                });\n            }\n        }, 50);\n    }, [\n        updateFilenames,\n        handleCustomFieldsFetch,\n        form\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const subscription = form.watch((_, param)=>{\n            let { name } = param;\n            if (name && (name.includes(\"associateId\") || name.includes(\"clientId\") || name.includes(\"carrierName\") || name.includes(\"invoiceDate\") || name.includes(\"receivedDate\") || name.includes(\"ftpFileName\") || name.includes(\"company\") || name.includes(\"division\"))) {\n                updateFilenames();\n            }\n        });\n        return ()=>subscription.unsubscribe();\n    }, [\n        form,\n        updateFilenames\n    ]);\n    const onSubmit = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (values)=>{\n        try {\n            const currentFormValues = form.getValues();\n            const currentValidation = [];\n            const currentMissingFields = [];\n            const currentFilenames = [];\n            if (currentFormValues.entries && Array.isArray(currentFormValues.entries)) {\n                currentFormValues.entries.forEach((_, index)=>{\n                    const { filename, isValid, missing } = generateFilename(index, currentFormValues);\n                    currentValidation[index] = isValid;\n                    currentMissingFields[index] = missing || [];\n                    currentFilenames[index] = filename;\n                });\n            }\n            const allFilenamesValid = currentValidation.every((isValid)=>isValid);\n            if (!allFilenamesValid) {\n                const invalidEntries = currentValidation.map((isValid, index)=>({\n                        index,\n                        isValid,\n                        missing: currentMissingFields[index]\n                    })).filter((entry)=>!entry.isValid);\n                const errorDetails = invalidEntries.map((entry)=>\"Entry \".concat(entry.index + 1, \": \").concat(entry.missing.join(\", \"))).join(\" | \");\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Cannot submit: Missing fields - \".concat(errorDetails));\n                return;\n            }\n            const entries = values.entries.map((entry, index)=>{\n                var _entry_customFields;\n                return {\n                    company: entry.company,\n                    division: entry.division,\n                    invoice: entry.invoice,\n                    masterInvoice: entry.masterInvoice,\n                    bol: entry.bol,\n                    invoiceDate: entry.invoiceDate,\n                    receivedDate: entry.receivedDate,\n                    shipmentDate: entry.shipmentDate,\n                    carrierId: entry.carrierName,\n                    invoiceStatus: entry.invoiceStatus,\n                    manualMatching: entry.manualMatching,\n                    invoiceType: entry.invoiceType,\n                    currency: entry.currency,\n                    qtyShipped: entry.qtyShipped,\n                    weightUnitName: entry.weightUnitName,\n                    quantityBilledText: entry.quantityBilledText,\n                    invoiceTotal: entry.invoiceTotal,\n                    savings: entry.savings,\n                    ftpFileName: entry.ftpFileName,\n                    ftpPage: entry.ftpPage,\n                    docAvailable: entry.docAvailable,\n                    notes: entry.notes,\n                    mistake: entry.mistake,\n                    filePath: generatedFilenames[index],\n                    customFields: (_entry_customFields = entry.customFields) === null || _entry_customFields === void 0 ? void 0 : _entry_customFields.map((cf)=>({\n                            id: cf.id,\n                            value: cf.value\n                        }))\n                };\n            });\n            const formData = {\n                clientId: values.clientId,\n                entries: entries\n            };\n            const result = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_7__.formSubmit)(_lib_routePath__WEBPACK_IMPORTED_MODULE_8__.trackSheets_routes.CREATE_TRACK_SHEETS, \"POST\", formData);\n            if (result.success) {\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.success(\"All TrackSheets created successfully\");\n                form.reset();\n                setTimeout(()=>{\n                    handleInitialSelection(initialAssociateId, initialClientId);\n                }, 100);\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(result.message || \"Failed to create TrackSheets\");\n            }\n            router.refresh();\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"An error occurred while creating the TrackSheets\");\n        }\n    }, [\n        form,\n        router,\n        generateFilename,\n        initialAssociateId,\n        initialClientId,\n        handleInitialSelection,\n        generatedFilenames\n    ]);\n    const addNewEntry = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const newIndex = fields.length;\n        append({\n            clientId: initialClientId,\n            company: \"\",\n            division: \"\",\n            invoice: \"\",\n            masterInvoice: \"\",\n            bol: \"\",\n            invoiceDate: new Date().toISOString().split(\"T\")[0],\n            receivedDate: new Date().toISOString().split(\"T\")[0],\n            shipmentDate: new Date().toISOString().split(\"T\")[0],\n            carrierName: \"\",\n            invoiceStatus: \"\",\n            manualMatching: \"\",\n            invoiceType: \"\",\n            currency: \"\",\n            qtyShipped: \"\",\n            weightUnitName: \"\",\n            quantityBilledText: \"\",\n            invoiceTotal: \"\",\n            savings: \"\",\n            ftpFileName: \"\",\n            ftpPage: \"\",\n            docAvailable: [],\n            notes: \"\",\n            mistake: \"\",\n            legrandAlias: \"\",\n            legrandCompanyName: \"\",\n            legrandAddress: \"\",\n            legrandZipcode: \"\",\n            shipperAlias: \"\",\n            shipperAddress: \"\",\n            shipperZipcode: \"\",\n            consigneeAlias: \"\",\n            consigneeAddress: \"\",\n            consigneeZipcode: \"\",\n            billtoAlias: \"\",\n            billtoAddress: \"\",\n            billtoZipcode: \"\",\n            customFields: []\n        });\n        setTimeout(()=>{\n            handleCompanyAutoPopulation(newIndex, initialClientId);\n            handleCustomFieldsFetch(newIndex, initialClientId);\n            if (companyFieldRefs.current[newIndex]) {\n                var _companyFieldRefs_current_newIndex, _companyFieldRefs_current_newIndex1, _companyFieldRefs_current_newIndex2;\n                const inputElement = ((_companyFieldRefs_current_newIndex = companyFieldRefs.current[newIndex]) === null || _companyFieldRefs_current_newIndex === void 0 ? void 0 : _companyFieldRefs_current_newIndex.querySelector(\"input\")) || ((_companyFieldRefs_current_newIndex1 = companyFieldRefs.current[newIndex]) === null || _companyFieldRefs_current_newIndex1 === void 0 ? void 0 : _companyFieldRefs_current_newIndex1.querySelector(\"button\")) || ((_companyFieldRefs_current_newIndex2 = companyFieldRefs.current[newIndex]) === null || _companyFieldRefs_current_newIndex2 === void 0 ? void 0 : _companyFieldRefs_current_newIndex2.querySelector(\"select\"));\n                if (inputElement) {\n                    inputElement.focus();\n                    try {\n                        inputElement.click();\n                    } catch (e) {}\n                }\n            }\n            updateFilenames();\n        }, 200);\n    }, [\n        append,\n        fields.length,\n        updateFilenames,\n        initialClientId,\n        handleCompanyAutoPopulation,\n        handleCustomFieldsFetch\n    ]);\n    const handleFormKeyDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        if (e.ctrlKey && (e.key === \"s\" || e.key === \"S\")) {\n            e.preventDefault();\n            form.handleSubmit(onSubmit)();\n        } else if (e.shiftKey && e.key === \"Enter\") {\n            e.preventDefault();\n            addNewEntry();\n        } else if (e.key === \"Enter\" && !e.ctrlKey && !e.shiftKey && !e.altKey) {\n            const activeElement = document.activeElement;\n            const isSubmitButton = (activeElement === null || activeElement === void 0 ? void 0 : activeElement.getAttribute(\"type\")) === \"submit\";\n            if (isSubmitButton) {\n                e.preventDefault();\n                form.handleSubmit(onSubmit)();\n            }\n        }\n    }, [\n        form,\n        onSubmit,\n        addNewEntry\n    ]);\n    const removeEntry = (index)=>{\n        if (fields.length > 1) {\n            remove(index);\n        } else {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"You must have at least one entry\");\n        }\n    };\n    const getFilteredDivisionOptions = (company, entryIndex)=>{\n        if (!company || !legrandData.length) {\n            return [];\n        }\n        if (entryIndex !== undefined) {\n            var _formValues_entries, _clientOptions_find;\n            const formValues = form.getValues();\n            const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[entryIndex];\n            const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || (entryIndex === 0 ? formValues.clientId : \"\");\n            const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n            if (entryClientName === \"LEGRAND\") {\n                const shipperAlias = form.getValues(\"entries.\".concat(entryIndex, \".shipperAlias\"));\n                const consigneeAlias = form.getValues(\"entries.\".concat(entryIndex, \".consigneeAlias\"));\n                const billtoAlias = form.getValues(\"entries.\".concat(entryIndex, \".billtoAlias\"));\n                const currentAlias = shipperAlias || consigneeAlias || billtoAlias;\n                if (currentAlias) {\n                    const selectedData = legrandData.find((data)=>{\n                        const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n                        return uniqueKey === currentAlias;\n                    });\n                    if (selectedData) {\n                        const baseAliasName = selectedData.aliasShippingNames && selectedData.aliasShippingNames !== \"NONE\" ? selectedData.aliasShippingNames : selectedData.legalName;\n                        const sameAliasEntries = legrandData.filter((data)=>{\n                            const dataAliasName = data.aliasShippingNames && data.aliasShippingNames !== \"NONE\" ? data.aliasShippingNames : data.legalName;\n                            return dataAliasName === baseAliasName;\n                        });\n                        const allDivisions = [];\n                        sameAliasEntries.forEach((entry)=>{\n                            if (entry.customeCode) {\n                                if (entry.customeCode.includes(\"/\")) {\n                                    const splitDivisions = entry.customeCode.split(\"/\").map((d)=>d.trim());\n                                    allDivisions.push(...splitDivisions);\n                                } else {\n                                    allDivisions.push(entry.customeCode);\n                                }\n                            }\n                        });\n                        const uniqueDivisions = Array.from(new Set(allDivisions.filter((code)=>code)));\n                        if (uniqueDivisions.length > 1) {\n                            const contextDivisions = uniqueDivisions.sort().map((code)=>({\n                                    value: code,\n                                    label: code\n                                }));\n                            return contextDivisions;\n                        } else {}\n                    }\n                }\n            }\n        }\n        const allDivisions = [];\n        legrandData.filter((data)=>data.businessUnit === company && data.customeCode).forEach((data)=>{\n            if (data.customeCode.includes(\"/\")) {\n                const splitDivisions = data.customeCode.split(\"/\").map((d)=>d.trim());\n                allDivisions.push(...splitDivisions);\n            } else {\n                allDivisions.push(data.customeCode);\n            }\n        });\n        const divisions = Array.from(new Set(allDivisions.filter((code)=>code))).sort().map((code)=>({\n                value: code,\n                label: code\n            }));\n        return divisions;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full px-2 py-3\",\n                children: showFullForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_3__.Form, {\n                    ...form,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: form.handleSubmit(onSubmit),\n                        onKeyDown: handleFormKeyDown,\n                        className: \"space-y-3\",\n                        children: [\n                            fields.map((field, index)=>{\n                                var _missingFields_index;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2 bg-gray-100 rounded-md px-3 py-2 border border-gray-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-5 h-5 bg-gray-600 rounded-full flex items-center justify-center text-white font-semibold text-xs\",\n                                                        children: index + 1\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 975,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-sm font-semibold text-gray-900\",\n                                                        children: [\n                                                            \"Entry #\",\n                                                            index + 1\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 978,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                lineNumber: 974,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                            lineNumber: 973,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-3 pb-3 border-b border-gray-100\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-50 rounded-md p-2 mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 991,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-sm font-semibold text-gray-900\",\n                                                                        children: \"Client Information\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 992,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 990,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 mb-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                            className: \"mt-2\",\n                                                                            form: form,\n                                                                            label: \"FTP File Name\",\n                                                                            name: \"entries.\".concat(index, \".ftpFileName\"),\n                                                                            type: \"text\",\n                                                                            isRequired: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 999,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 998,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_PageInput__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"mt-2\",\n                                                                        form: form,\n                                                                        label: \"FTP Page\",\n                                                                        name: \"entries.\".concat(index, \".ftpPage\"),\n                                                                        isRequired: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1008,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1015,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    \" \"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 997,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            (()=>{\n                                                                var _formValues_entries, _clientOptions_find;\n                                                                const formValues = form.getValues();\n                                                                const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                return entryClientName === \"LEGRAND\";\n                                                            })() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-3 mb-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            form: form,\n                                                                            entryIndex: index,\n                                                                            onLegrandDataChange: handleLegrandDataChange,\n                                                                            blockTitle: \"Shipper\",\n                                                                            fieldPrefix: \"shipper\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1032,\n                                                                            columnNumber: 33\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            form: form,\n                                                                            entryIndex: index,\n                                                                            onLegrandDataChange: handleLegrandDataChange,\n                                                                            blockTitle: \"Consignee\",\n                                                                            fieldPrefix: \"consignee\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1039,\n                                                                            columnNumber: 33\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            form: form,\n                                                                            entryIndex: index,\n                                                                            onLegrandDataChange: handleLegrandDataChange,\n                                                                            blockTitle: \"Bill-to\",\n                                                                            fieldPrefix: \"billto\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1046,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1031,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1030,\n                                                                columnNumber: 29\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        ref: (el)=>{\n                                                                            companyFieldRefs.current[index] = el;\n                                                                        },\n                                                                        className: \"flex flex-col mb-1 [&_input]:h-10\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                            form: form,\n                                                                            label: \"Company\",\n                                                                            name: \"entries.\".concat(index, \".company\"),\n                                                                            type: \"text\",\n                                                                            disable: (()=>{\n                                                                                var _formValues_entries, _clientOptions_find;\n                                                                                const formValues = form.getValues();\n                                                                                const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                                const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                                const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                                return entryClientName === \"LEGRAND\";\n                                                                            })()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1065,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1059,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex flex-col\",\n                                                                        children: (()=>{\n                                                                            var _formValues_entries, _clientOptions_find, _entries_index;\n                                                                            const formValues = form.getValues();\n                                                                            const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                            const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                            const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                            const isLegrand = entryClientName === \"LEGRAND\";\n                                                                            return isLegrand ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                form: form,\n                                                                                name: \"entries.\".concat(index, \".division\"),\n                                                                                label: \"Division\",\n                                                                                placeholder: \"Search Division\",\n                                                                                disabled: false,\n                                                                                options: getFilteredDivisionOptions((entries === null || entries === void 0 ? void 0 : (_entries_index = entries[index]) === null || _entries_index === void 0 ? void 0 : _entries_index.company) || \"\", index)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1096,\n                                                                                columnNumber: 35\n                                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                form: form,\n                                                                                label: \"Division\",\n                                                                                name: \"entries.\".concat(index, \".division\"),\n                                                                                type: \"text\",\n                                                                                placeholder: \"Enter Division\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1108,\n                                                                                columnNumber: 35\n                                                                            }, undefined);\n                                                                        })()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1084,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex flex-col\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"mt-0\",\n                                                                            form: form,\n                                                                            name: \"entries.\".concat(index, \".carrierName\"),\n                                                                            label: \"Select Carrier\",\n                                                                            placeholder: \"Search Carrier\",\n                                                                            isRequired: true,\n                                                                            options: (carrierOptions === null || carrierOptions === void 0 ? void 0 : carrierOptions.filter((carrier)=>{\n                                                                                const currentEntries = form.getValues(\"entries\") || [];\n                                                                                const isSelectedInOtherEntries = currentEntries.some((entry, entryIndex)=>entryIndex !== index && entry.carrierName === carrier.value);\n                                                                                return !isSelectedInOtherEntries;\n                                                                            })) || [],\n                                                                            onValueChange: ()=>{\n                                                                                setTimeout(()=>updateFilenames(), 100);\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1119,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1118,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1058,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 989,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                    lineNumber: 987,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-3 pb-3 border-b border-gray-100\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-orange-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1150,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-sm font-semibold text-gray-900\",\n                                                                    children: \"Document Information\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1151,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1149,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Master Invoice\",\n                                                                    name: \"entries.\".concat(index, \".masterInvoice\"),\n                                                                    type: \"text\",\n                                                                    onBlur: (e)=>{\n                                                                        const masterInvoiceValue = e.target.value;\n                                                                        if (masterInvoiceValue) {\n                                                                            form.setValue(\"entries.\".concat(index, \".invoice\"), masterInvoiceValue);\n                                                                        }\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1156,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Invoice\",\n                                                                    name: \"entries.\".concat(index, \".invoice\"),\n                                                                    type: \"text\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1171,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"BOL\",\n                                                                    name: \"entries.\".concat(index, \".bol\"),\n                                                                    type: \"text\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1178,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1155,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Received Date\",\n                                                                    name: \"entries.\".concat(index, \".receivedDate\"),\n                                                                    type: \"date\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1187,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Invoice Date\",\n                                                                    name: \"entries.\".concat(index, \".invoiceDate\"),\n                                                                    type: \"date\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1194,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Shipment Date\",\n                                                                    name: \"entries.\".concat(index, \".shipmentDate\"),\n                                                                    type: \"date\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1201,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1186,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                    lineNumber: 1148,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4 pb-4 border-b border-gray-100\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1214,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-sm font-semibold text-gray-900\",\n                                                                    children: \"Financial & Shipment\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1215,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1213,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Invoice Total\",\n                                                                    name: \"entries.\".concat(index, \".invoiceTotal\"),\n                                                                    type: \"number\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1220,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    form: form,\n                                                                    name: \"entries.\".concat(index, \".currency\"),\n                                                                    label: \"Currency\",\n                                                                    placeholder: \"Search currency\",\n                                                                    isRequired: true,\n                                                                    options: [\n                                                                        {\n                                                                            value: \"USD\",\n                                                                            label: \"USD\"\n                                                                        },\n                                                                        {\n                                                                            value: \"CAD\",\n                                                                            label: \"CAD\"\n                                                                        },\n                                                                        {\n                                                                            value: \"EUR\",\n                                                                            label: \"EUR\"\n                                                                        }\n                                                                    ]\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1227,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Quantity Shipped\",\n                                                                    name: \"entries.\".concat(index, \".qtyShipped\"),\n                                                                    type: \"number\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1239,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Weight Unit\",\n                                                                    name: \"entries.\".concat(index, \".weightUnitName\"),\n                                                                    type: \"text\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1246,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1219,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 mt-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Savings\",\n                                                                    name: \"entries.\".concat(index, \".savings\"),\n                                                                    type: \"text\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1255,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Invoice Type\",\n                                                                    name: \"entries.\".concat(index, \".invoiceType\"),\n                                                                    type: \"text\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1261,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Quantity Billed Text\",\n                                                                    name: \"entries.\".concat(index, \".quantityBilledText\"),\n                                                                    type: \"text\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1268,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Invoice Status\",\n                                                                    name: \"entries.\".concat(index, \".invoiceStatus\"),\n                                                                    type: \"text\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1274,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1254,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                    lineNumber: 1212,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-gray-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1287,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-sm font-semibold text-gray-900\",\n                                                                    children: \"Additional Information\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1288,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1286,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Manual Matching\",\n                                                                    name: \"entries.\".concat(index, \".manualMatching\"),\n                                                                    type: \"text\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1293,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Notes\",\n                                                                    name: \"entries.\".concat(index, \".notes\"),\n                                                                    type: \"text\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1300,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Mistake\",\n                                                                    name: \"entries.\".concat(index, \".mistake\"),\n                                                                    type: \"text\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1306,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormCheckboxGroup__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Documents Available\",\n                                                                        name: \"entries.\".concat(index, \".docAvailable\"),\n                                                                        options: [\n                                                                            {\n                                                                                label: \"Invoice\",\n                                                                                value: \"Invoice\"\n                                                                            },\n                                                                            {\n                                                                                label: \"BOL\",\n                                                                                value: \"Bol\"\n                                                                            },\n                                                                            {\n                                                                                label: \"POD\",\n                                                                                value: \"Pod\"\n                                                                            },\n                                                                            {\n                                                                                label: \"Packages List\",\n                                                                                value: \"Packages List\"\n                                                                            }\n                                                                        ],\n                                                                        className: \"flex-row gap-2 text-xs\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1313,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1312,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1292,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                    lineNumber: 1285,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                (()=>{\n                                                    var _formValues_entries;\n                                                    const formValues = form.getValues();\n                                                    const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                    const customFields = (entry === null || entry === void 0 ? void 0 : entry.customFields) || [];\n                                                    return Array.isArray(customFields) && customFields.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"pt-3 border-t border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-purple-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1345,\n                                                                        columnNumber: 31\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-sm font-semibold text-gray-900\",\n                                                                        children: [\n                                                                            \"Custom Fields (\",\n                                                                            customFields.length,\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1346,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1344,\n                                                                columnNumber: 29\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2\",\n                                                                children: customFields.map((cf, cfIdx)=>{\n                                                                    const fieldType = cf.type || \"TEXT\";\n                                                                    const isAutoField = fieldType === \"AUTO\";\n                                                                    const autoOption = cf.autoOption;\n                                                                    let inputType = \"text\";\n                                                                    if (fieldType === \"DATE\" || isAutoField && autoOption === \"DATE\") {\n                                                                        inputType = \"date\";\n                                                                    } else if (fieldType === \"NUMBER\") {\n                                                                        inputType = \"number\";\n                                                                    }\n                                                                    const fieldLabel = isAutoField ? \"\".concat(cf.name, \" (Auto - \").concat(autoOption, \")\") : cf.name;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: fieldLabel,\n                                                                        name: \"entries.\".concat(index, \".customFields.\").concat(cfIdx, \".value\"),\n                                                                        type: inputType,\n                                                                        className: \"w-full\",\n                                                                        disable: isAutoField\n                                                                    }, cf.id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1371,\n                                                                        columnNumber: 35\n                                                                    }, undefined);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1350,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        ]\n                                                    }, \"custom-fields-\".concat(index, \"-\").concat(customFieldsRefresh), true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1340,\n                                                        columnNumber: 27\n                                                    }, undefined) : null;\n                                                })(),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"pt-3 border-t border-gray-100 mt-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-end space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.Tooltip, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipTrigger, {\n                                                                        asChild: true,\n                                                                        tabIndex: -1,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-5 h-5 rounded-full flex items-center justify-center text-white font-bold text-xs cursor-help transition-colors duration-200 \".concat(filenameValidation[index] ? \"bg-green-500 hover:bg-green-600\" : \"bg-orange-500 hover:bg-orange-600\"),\n                                                                            tabIndex: -1,\n                                                                            role: \"button\",\n                                                                            \"aria-label\": \"Entry \".concat(index + 1, \" filename status\"),\n                                                                            children: \"!\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1393,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1392,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipContent, {\n                                                                        side: \"top\",\n                                                                        align: \"center\",\n                                                                        className: \"z-[9999]\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm max-w-md\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium mb-1\",\n                                                                                    children: [\n                                                                                        \"Entry #\",\n                                                                                        index + 1,\n                                                                                        \" Filename\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1412,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                filenameValidation[index] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"font-medium text-green-600 mb-2\",\n                                                                                            children: \"Filename Generated\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1417,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs font-mono break-all bg-gray-100 p-2 rounded text-black\",\n                                                                                            children: generatedFilenames[index]\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1420,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1416,\n                                                                                    columnNumber: 35\n                                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"font-medium text-orange-600 mb-1\",\n                                                                                            children: \"Please fill the form to generate filename\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1426,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-gray-600 mb-2\",\n                                                                                            children: \"Missing fields:\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1429,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                                            className: \"list-disc list-inside space-y-1\",\n                                                                                            children: (_missingFields_index = missingFields[index]) === null || _missingFields_index === void 0 ? void 0 : _missingFields_index.map((field, fieldIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                                    className: \"text-xs\",\n                                                                                                    children: field\n                                                                                                }, fieldIndex, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                                    lineNumber: 1435,\n                                                                                                    columnNumber: 43\n                                                                                                }, undefined))\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1432,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1425,\n                                                                                    columnNumber: 35\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1411,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1406,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1391,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                type: \"button\",\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                className: \"h-7 w-7 p-0 hover:bg-red-50 hover:border-red-200\",\n                                                                onClick: ()=>removeEntry(index),\n                                                                disabled: fields.length <= 1,\n                                                                tabIndex: -1,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"h-3 w-3 text-red-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1459,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1450,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            index === fields.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.Tooltip, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipTrigger, {\n                                                                        asChild: true,\n                                                                        tabIndex: -1,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                            type: \"button\",\n                                                                            variant: \"outline\",\n                                                                            size: \"sm\",\n                                                                            className: \"h-7 w-7 p-0 hover:bg-green-50 hover:border-green-200\",\n                                                                            onClick: addNewEntry,\n                                                                            tabIndex: -1,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                className: \"h-3 w-3 text-green-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1472,\n                                                                                columnNumber: 35\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1464,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1463,\n                                                                        columnNumber: 31\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipContent, {\n                                                                        side: \"top\",\n                                                                        align: \"center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs\",\n                                                                            children: \"Add New Entry (Shift+Enter)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1476,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1475,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1462,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1389,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                    lineNumber: 1388,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                            lineNumber: 985,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, field.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                    lineNumber: 971,\n                                    columnNumber: 19\n                                }, undefined);\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        type: \"submit\",\n                                        className: \"px-6 py-2 rounded-lg font-medium transition-all duration-200 shadow-md hover:shadow-lg text-sm\",\n                                        children: \"Save\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                        lineNumber: 1491,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                    lineNumber: 1490,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                lineNumber: 1489,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                        lineNumber: 965,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                    lineNumber: 964,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                lineNumber: 959,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n            lineNumber: 958,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n        lineNumber: 957,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CreateTrackSheet, \"G9t5Zu03ZeKBM5unrApPUQQEs5g=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useWatch,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useFieldArray\n    ];\n});\n_c = CreateTrackSheet;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CreateTrackSheet);\nvar _c;\n$RefreshReg$(_c, \"CreateTrackSheet\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/trackSheets/v2/createTrackSheet.tsx\n"));

/***/ })

});