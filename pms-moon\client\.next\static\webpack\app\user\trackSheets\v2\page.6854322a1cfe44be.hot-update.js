"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/trackSheets/v2/page",{

/***/ "(app-pages-browser)/./app/user/trackSheets/v2/createTrackSheet.tsx":
/*!******************************************************!*\
  !*** ./app/user/trackSheets/v2/createTrackSheet.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./components/ui/form.tsx\");\n/* harmony import */ var _app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/_component/FormInput */ \"(app-pages-browser)/./app/_component/FormInput.tsx\");\n/* harmony import */ var _app_component_FormCheckboxGroup__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/_component/FormCheckboxGroup */ \"(app-pages-browser)/./app/_component/FormCheckboxGroup.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-minus.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-plus.js\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/helpers */ \"(app-pages-browser)/./lib/helpers.ts\");\n/* harmony import */ var _lib_routePath__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/routePath */ \"(app-pages-browser)/./lib/routePath.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var _app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/_component/SearchSelect */ \"(app-pages-browser)/./app/_component/SearchSelect.tsx\");\n/* harmony import */ var _app_component_PageInput__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/_component/PageInput */ \"(app-pages-browser)/./app/_component/PageInput.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./components/ui/tooltip.tsx\");\n/* harmony import */ var _LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./LegrandDetailsComponent */ \"(app-pages-browser)/./app/user/trackSheets/v2/LegrandDetailsComponent.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst validateFtpPageFormat = (value)=>{\n    if (!value || value.trim() === \"\") return false;\n    const ftpPageRegex = /^(\\d+)\\s+of\\s+(\\d+)$/i;\n    const match = value.match(ftpPageRegex);\n    if (!match) return false;\n    const currentPage = parseInt(match[1], 10);\n    const totalPages = parseInt(match[2], 10);\n    return currentPage > 0 && totalPages > 0 && currentPage <= totalPages;\n};\nconst trackSheetSchema = zod__WEBPACK_IMPORTED_MODULE_15__.z.object({\n    clientId: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Client is required\"),\n    entries: zod__WEBPACK_IMPORTED_MODULE_15__.z.array(zod__WEBPACK_IMPORTED_MODULE_15__.z.object({\n        company: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Company is required\"),\n        division: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        invoice: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Invoice is required\"),\n        masterInvoice: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        bol: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"BOL is required\"),\n        invoiceDate: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Invoice date is required\"),\n        receivedDate: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Received date is required\"),\n        shipmentDate: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Shipment date is required\"),\n        carrierName: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Carrier name is required\"),\n        invoiceStatus: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Invoice status is required\"),\n        manualMatching: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Manual matching is required\"),\n        invoiceType: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Invoice type is required\"),\n        currency: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Currency is required\"),\n        qtyShipped: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Quantity shipped is required\"),\n        weightUnitName: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Weight unit is required\"),\n        quantityBilledText: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        invoiceTotal: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Invoice total is required\"),\n        savings: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        ftpFileName: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"FTP File Name is required\"),\n        ftpPage: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"FTP Page is required\").refine((value)=>validateFtpPageFormat(value), (value)=>{\n            if (!value || value.trim() === \"\") {\n                return {\n                    message: \"FTP Page is required\"\n                };\n            }\n            const ftpPageRegex = /^(\\d+)\\s+of\\s+(\\d+)$/i;\n            const match = value.match(ftpPageRegex);\n            if (!match) {\n                return {\n                    message: \"\"\n                };\n            }\n            const currentPage = parseInt(match[1], 10);\n            const totalPages = parseInt(match[2], 10);\n            if (currentPage <= 0 || totalPages <= 0) {\n                return {\n                    message: \"Page numbers must be positive (greater than 0)\"\n                };\n            }\n            if (currentPage > totalPages) {\n                return {\n                    message: \"Please enter a page number between \".concat(totalPages, \" and \").concat(currentPage, \" \")\n                };\n            }\n            return {\n                message: \"Invalid page format\"\n            };\n        }),\n        docAvailable: zod__WEBPACK_IMPORTED_MODULE_15__.z.array(zod__WEBPACK_IMPORTED_MODULE_15__.z.string()).optional().default([]),\n        notes: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        mistake: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        legrandAlias: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        legrandCompanyName: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        legrandAddress: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        legrandZipcode: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        shipperAlias: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        shipperAddress: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        shipperZipcode: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        consigneeAlias: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        consigneeAddress: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        consigneeZipcode: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        billtoAlias: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        billtoAddress: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        billtoZipcode: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        customFields: zod__WEBPACK_IMPORTED_MODULE_15__.z.array(zod__WEBPACK_IMPORTED_MODULE_15__.z.object({\n            id: zod__WEBPACK_IMPORTED_MODULE_15__.z.string(),\n            name: zod__WEBPACK_IMPORTED_MODULE_15__.z.string(),\n            type: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n            value: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional()\n        })).default([])\n    }))\n});\nconst CreateTrackSheet = (param)=>{\n    let { client, carrier, associate, userData } = param;\n    _s();\n    const companyFieldRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const [customFields, setCustomFields] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [generatedFilenames, setGeneratedFilenames] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filenameValidation, setFilenameValidation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [missingFields, setMissingFields] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [legrandData, setLegrandData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [customFieldsRefresh, setCustomFieldsRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showFullForm, setShowFullForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [initialAssociateId, setInitialAssociateId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [initialClientId, setInitialClientId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const selectionForm = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useForm)({\n        defaultValues: {\n            associateId: \"\",\n            clientId: \"\"\n        }\n    });\n    const associateOptions = associate === null || associate === void 0 ? void 0 : associate.map((a)=>{\n        var _a_id;\n        return {\n            value: (_a_id = a.id) === null || _a_id === void 0 ? void 0 : _a_id.toString(),\n            label: a.name,\n            name: a.name\n        };\n    });\n    const carrierOptions = carrier === null || carrier === void 0 ? void 0 : carrier.map((c)=>{\n        var _c_id;\n        return {\n            value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n            label: c.name\n        };\n    });\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(trackSheetSchema),\n        defaultValues: {\n            associateId: \"\",\n            clientId: \"\",\n            entries: [\n                {\n                    company: \"\",\n                    division: \"\",\n                    invoice: \"\",\n                    masterInvoice: \"\",\n                    bol: \"\",\n                    invoiceDate: new Date().toISOString().split(\"T\")[0],\n                    receivedDate: new Date().toISOString().split(\"T\")[0],\n                    shipmentDate: new Date().toISOString().split(\"T\")[0],\n                    carrierName: \"\",\n                    invoiceStatus: \"\",\n                    manualMatching: \"\",\n                    invoiceType: \"\",\n                    currency: \"\",\n                    qtyShipped: \"\",\n                    weightUnitName: \"\",\n                    quantityBilledText: \"\",\n                    invoiceTotal: \"\",\n                    savings: \"\",\n                    ftpFileName: \"\",\n                    ftpPage: \"\",\n                    docAvailable: [],\n                    notes: \"\",\n                    mistake: \"\",\n                    legrandAlias: \"\",\n                    legrandCompanyName: \"\",\n                    legrandAddress: \"\",\n                    legrandZipcode: \"\",\n                    shipperAlias: \"\",\n                    shipperAddress: \"\",\n                    shipperZipcode: \"\",\n                    consigneeAlias: \"\",\n                    consigneeAddress: \"\",\n                    consigneeZipcode: \"\",\n                    billtoAlias: \"\",\n                    billtoAddress: \"\",\n                    billtoZipcode: \"\",\n                    customFields: []\n                }\n            ]\n        }\n    });\n    const getFilteredClientOptions = ()=>{\n        if (!initialAssociateId) {\n            return (client === null || client === void 0 ? void 0 : client.map((c)=>{\n                var _c_id;\n                return {\n                    value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n                    label: c.client_name,\n                    name: c.client_name\n                };\n            })) || [];\n        }\n        const filteredClients = (client === null || client === void 0 ? void 0 : client.filter((c)=>{\n            var _c_associateId;\n            return ((_c_associateId = c.associateId) === null || _c_associateId === void 0 ? void 0 : _c_associateId.toString()) === initialAssociateId;\n        })) || [];\n        return filteredClients.map((c)=>{\n            var _c_id;\n            return {\n                value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n                label: c.client_name,\n                name: c.client_name\n            };\n        });\n    };\n    const clientOptions = getFilteredClientOptions();\n    const entries = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useWatch)({\n        control: form.control,\n        name: \"entries\"\n    });\n    const validateClientForAssociate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((associateId, currentClientId)=>{\n        if (associateId && currentClientId) {\n            var _currentClient_associateId;\n            const currentClient = client === null || client === void 0 ? void 0 : client.find((c)=>{\n                var _c_id;\n                return ((_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString()) === currentClientId;\n            });\n            if (currentClient && ((_currentClient_associateId = currentClient.associateId) === null || _currentClient_associateId === void 0 ? void 0 : _currentClient_associateId.toString()) !== associateId) {\n                form.setValue(\"clientId\", \"\");\n                setInitialClientId(\"\");\n                return false;\n            }\n        }\n        return true;\n    }, [\n        client,\n        form\n    ]);\n    const clearEntrySpecificClients = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const currentEntries = form.getValues(\"entries\") || [];\n        if (currentEntries.length > 0) {\n            const hasEntrySpecificClients = currentEntries.some((entry)=>entry.clientId);\n            if (hasEntrySpecificClients) {\n                const updatedEntries = currentEntries.map((entry)=>({\n                        ...entry,\n                        clientId: \"\"\n                    }));\n                form.setValue(\"entries\", updatedEntries);\n            }\n        }\n    }, [\n        form\n    ]);\n    const fetchLegrandData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            const response = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_7__.getAllData)(_lib_routePath__WEBPACK_IMPORTED_MODULE_8__.legrandMapping_routes.GET_LEGRAND_MAPPINGS);\n            if (response && Array.isArray(response)) {\n                setLegrandData(response);\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Error fetching LEGRAND mapping data:\", error);\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        fetchLegrandData();\n    }, [\n        fetchLegrandData\n    ]);\n    const handleLegrandDataChange = (entryIndex, businessUnit, divisionCode)=>{\n        form.setValue(\"entries.\".concat(entryIndex, \".company\"), businessUnit);\n        if (divisionCode) {\n            form.setValue(\"entries.\".concat(entryIndex, \".division\"), divisionCode);\n        } else {\n            form.setValue(\"entries.\".concat(entryIndex, \".division\"), \"\");\n        }\n    };\n    const fetchCustomFieldsForClient = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (clientId)=>{\n        if (!clientId) return [];\n        try {\n            const allCustomFieldsResponse = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_7__.getAllData)(\"\".concat(_lib_routePath__WEBPACK_IMPORTED_MODULE_8__.clientCustomFields_routes.GET_CLIENT_CUSTOM_FIELDS, \"/\").concat(clientId));\n            let customFieldsData = [];\n            if (allCustomFieldsResponse && allCustomFieldsResponse.custom_fields && allCustomFieldsResponse.custom_fields.length > 0) {\n                customFieldsData = allCustomFieldsResponse.custom_fields.map((field)=>{\n                    let autoFilledValue = \"\";\n                    if (field.type === \"AUTO\") {\n                        if (field.autoOption === \"DATE\") {\n                            autoFilledValue = new Date().toISOString().split(\"T\")[0];\n                        } else if (field.autoOption === \"USERNAME\") {\n                            autoFilledValue = (userData === null || userData === void 0 ? void 0 : userData.username) || \"\";\n                        }\n                    }\n                    return {\n                        id: field.id,\n                        name: field.name,\n                        type: field.type,\n                        autoOption: field.autoOption,\n                        value: autoFilledValue\n                    };\n                });\n            }\n            return customFieldsData;\n        } catch (error) {\n            return [];\n        }\n    }, [\n        userData\n    ]);\n    const { fields, append, remove } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useFieldArray)({\n        control: form.control,\n        name: \"entries\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        companyFieldRefs.current = companyFieldRefs.current.slice(0, fields.length);\n    }, [\n        fields.length\n    ]);\n    const generateFilename = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((entryIndex, formValues)=>{\n        try {\n            const entry = formValues.entries[entryIndex];\n            if (!entry) return {\n                filename: \"\",\n                isValid: false,\n                missing: [\n                    \"Entry data\"\n                ]\n            };\n            const missing = [];\n            const selectedAssociate = associate === null || associate === void 0 ? void 0 : associate.find((a)=>{\n                var _a_id;\n                return ((_a_id = a.id) === null || _a_id === void 0 ? void 0 : _a_id.toString()) === formValues.associateId;\n            });\n            const associateName = (selectedAssociate === null || selectedAssociate === void 0 ? void 0 : selectedAssociate.name) || \"\";\n            if (!associateName) {\n                missing.push(\"Associate\");\n            }\n            const entryClientId = entry.clientId || formValues.clientId;\n            const selectedClient = client === null || client === void 0 ? void 0 : client.find((c)=>{\n                var _c_id;\n                return ((_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString()) === entryClientId;\n            });\n            const clientName = (selectedClient === null || selectedClient === void 0 ? void 0 : selectedClient.client_name) || \"\";\n            if (!clientName) {\n                missing.push(\"Client\");\n            }\n            let carrierName = \"\";\n            if (entry.carrierName) {\n                const carrierOption = carrier === null || carrier === void 0 ? void 0 : carrier.find((c)=>{\n                    var _c_id;\n                    return ((_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString()) === entry.carrierName;\n                });\n                carrierName = (carrierOption === null || carrierOption === void 0 ? void 0 : carrierOption.name) || \"\";\n            }\n            if (!carrierName) {\n                missing.push(\"Carrier\");\n            }\n            const receivedDate = entry.receivedDate;\n            const invoiceDate = entry.invoiceDate;\n            const currentDate = new Date();\n            const year = currentDate.getFullYear().toString();\n            const month = currentDate.toLocaleString(\"default\", {\n                month: \"short\"\n            }).toUpperCase();\n            if (!invoiceDate) {\n                missing.push(\"Invoice Date\");\n            }\n            let receivedDateStr = \"\";\n            if (receivedDate) {\n                const date = new Date(receivedDate);\n                receivedDateStr = date.toISOString().split(\"T\")[0];\n            } else {\n                missing.push(\"Received Date\");\n            }\n            const ftpFileName = entry.ftpFileName || \"\";\n            const baseFilename = ftpFileName ? ftpFileName.endsWith(\".pdf\") ? ftpFileName : \"\".concat(ftpFileName, \".pdf\") : \"\";\n            if (!baseFilename) {\n                missing.push(\"FTP File Name\");\n            }\n            const isValid = missing.length === 0;\n            const filename = isValid ? \"/\".concat(associateName, \"/\").concat(clientName, \"/CARRIERINVOICES/\").concat(carrierName, \"/\").concat(year, \"/\").concat(month, \"/\").concat(receivedDateStr, \"/\").concat(baseFilename) : \"\";\n            return {\n                filename,\n                isValid,\n                missing\n            };\n        } catch (error) {\n            return {\n                filename: \"\",\n                isValid: false,\n                missing: [\n                    \"Error generating filename\"\n                ]\n            };\n        }\n    }, [\n        client,\n        carrier,\n        associate\n    ]);\n    const handleCompanyAutoPopulation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((entryIndex, entryClientId)=>{\n        var _clientOptions_find;\n        const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n        const currentEntry = form.getValues(\"entries.\".concat(entryIndex));\n        if (entryClientName && entryClientName !== \"LEGRAND\") {\n            form.setValue(\"entries.\".concat(entryIndex, \".company\"), entryClientName);\n        } else if (entryClientName === \"LEGRAND\") {\n            const shipperAlias = currentEntry.shipperAlias;\n            const consigneeAlias = currentEntry.consigneeAlias;\n            const billtoAlias = currentEntry.billtoAlias;\n            const hasAnyLegrandData = shipperAlias || consigneeAlias || billtoAlias;\n            if (!hasAnyLegrandData && currentEntry.company !== \"\") {\n                form.setValue(\"entries.\".concat(entryIndex, \".company\"), \"\");\n            }\n        } else {\n            if (currentEntry.company !== \"\") {\n                form.setValue(\"entries.\".concat(entryIndex, \".company\"), \"\");\n            }\n        }\n    }, [\n        form,\n        clientOptions\n    ]);\n    const handleCustomFieldsFetch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (entryIndex, entryClientId)=>{\n        var _currentCustomFields_, _currentCustomFields_1;\n        if (!entryClientId) {\n            const currentCustomFields = form.getValues(\"entries.\".concat(entryIndex, \".customFields\"));\n            if (currentCustomFields && currentCustomFields.length > 0) {\n                form.setValue(\"entries.\".concat(entryIndex, \".customFields\"), []);\n            }\n            return;\n        }\n        const currentCustomFields = form.getValues(\"entries.\".concat(entryIndex, \".customFields\")) || [];\n        const hasEmptyAutoUsernameFields = currentCustomFields.some((field)=>field.type === \"AUTO\" && field.autoOption === \"USERNAME\" && !field.value && (userData === null || userData === void 0 ? void 0 : userData.username));\n        const shouldFetchCustomFields = currentCustomFields.length === 0 || currentCustomFields.length > 0 && !((_currentCustomFields_ = currentCustomFields[0]) === null || _currentCustomFields_ === void 0 ? void 0 : _currentCustomFields_.clientId) || ((_currentCustomFields_1 = currentCustomFields[0]) === null || _currentCustomFields_1 === void 0 ? void 0 : _currentCustomFields_1.clientId) !== entryClientId || hasEmptyAutoUsernameFields;\n        if (shouldFetchCustomFields) {\n            const customFieldsData = await fetchCustomFieldsForClient(entryClientId);\n            const fieldsWithClientId = customFieldsData.map((field)=>({\n                    ...field,\n                    clientId: entryClientId\n                }));\n            form.setValue(\"entries.\".concat(entryIndex, \".customFields\"), fieldsWithClientId);\n            setTimeout(()=>{\n                fieldsWithClientId.forEach((field, fieldIndex)=>{\n                    const fieldPath = \"entries.\".concat(entryIndex, \".customFields.\").concat(fieldIndex, \".value\");\n                    if (field.value) {\n                        form.setValue(fieldPath, field.value);\n                    }\n                });\n                setCustomFieldsRefresh((prev)=>prev + 1);\n            }, 100);\n        }\n    }, [\n        form,\n        fetchCustomFieldsForClient,\n        userData === null || userData === void 0 ? void 0 : userData.username\n    ]);\n    const updateFilenames = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const formValues = form.getValues();\n        const newFilenames = [];\n        const newValidation = [];\n        const newMissingFields = [];\n        if (formValues.entries && Array.isArray(formValues.entries)) {\n            formValues.entries.forEach((_, index)=>{\n                const { filename, isValid, missing } = generateFilename(index, formValues);\n                newFilenames[index] = filename;\n                newValidation[index] = isValid;\n                newMissingFields[index] = missing || [];\n            });\n        }\n        setGeneratedFilenames(newFilenames);\n        setFilenameValidation(newValidation);\n        setMissingFields(newMissingFields);\n    }, [\n        form,\n        generateFilename\n    ]);\n    const handleInitialSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((associateId, clientId)=>{\n        form.setValue(\"associateId\", associateId);\n        form.setValue(\"clientId\", clientId);\n        setTimeout(()=>{\n            handleCompanyAutoPopulation(0, clientId);\n            handleCustomFieldsFetch(0, clientId);\n            updateFilenames();\n        }, 50);\n        setShowFullForm(true);\n    }, [\n        form,\n        handleCompanyAutoPopulation,\n        handleCustomFieldsFetch,\n        updateFilenames\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        setTimeout(()=>{\n            updateFilenames();\n            const formValues = form.getValues();\n            if (formValues.entries && Array.isArray(formValues.entries)) {\n                formValues.entries.forEach((entry, index)=>{\n                    const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || (index === 0 ? formValues.clientId : \"\");\n                    if (entryClientId) {\n                        handleCustomFieldsFetch(index, entryClientId);\n                    }\n                });\n            }\n        }, 50);\n    }, [\n        updateFilenames,\n        handleCustomFieldsFetch,\n        form\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const subscription = form.watch((_, param)=>{\n            let { name } = param;\n            if (name && (name.includes(\"associateId\") || name.includes(\"clientId\") || name.includes(\"carrierName\") || name.includes(\"invoiceDate\") || name.includes(\"receivedDate\") || name.includes(\"ftpFileName\") || name.includes(\"company\") || name.includes(\"division\"))) {\n                updateFilenames();\n            }\n        });\n        return ()=>subscription.unsubscribe();\n    }, [\n        form,\n        updateFilenames\n    ]);\n    const onSubmit = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (values)=>{\n        try {\n            const currentFormValues = form.getValues();\n            const currentValidation = [];\n            const currentMissingFields = [];\n            const currentFilenames = [];\n            if (currentFormValues.entries && Array.isArray(currentFormValues.entries)) {\n                currentFormValues.entries.forEach((_, index)=>{\n                    const { filename, isValid, missing } = generateFilename(index, currentFormValues);\n                    currentValidation[index] = isValid;\n                    currentMissingFields[index] = missing || [];\n                    currentFilenames[index] = filename;\n                });\n            }\n            const allFilenamesValid = currentValidation.every((isValid)=>isValid);\n            if (!allFilenamesValid) {\n                const invalidEntries = currentValidation.map((isValid, index)=>({\n                        index,\n                        isValid,\n                        missing: currentMissingFields[index]\n                    })).filter((entry)=>!entry.isValid);\n                const errorDetails = invalidEntries.map((entry)=>\"Entry \".concat(entry.index + 1, \": \").concat(entry.missing.join(\", \"))).join(\" | \");\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Cannot submit: Missing fields - \".concat(errorDetails));\n                return;\n            }\n            const entries = values.entries.map((entry, index)=>{\n                var _entry_customFields;\n                return {\n                    company: entry.company,\n                    division: entry.division,\n                    invoice: entry.invoice,\n                    masterInvoice: entry.masterInvoice,\n                    bol: entry.bol,\n                    invoiceDate: entry.invoiceDate,\n                    receivedDate: entry.receivedDate,\n                    shipmentDate: entry.shipmentDate,\n                    carrierId: entry.carrierName,\n                    invoiceStatus: entry.invoiceStatus,\n                    manualMatching: entry.manualMatching,\n                    invoiceType: entry.invoiceType,\n                    currency: entry.currency,\n                    qtyShipped: entry.qtyShipped,\n                    weightUnitName: entry.weightUnitName,\n                    quantityBilledText: entry.quantityBilledText,\n                    invoiceTotal: entry.invoiceTotal,\n                    savings: entry.savings,\n                    ftpFileName: entry.ftpFileName,\n                    ftpPage: entry.ftpPage,\n                    docAvailable: entry.docAvailable,\n                    notes: entry.notes,\n                    mistake: entry.mistake,\n                    filePath: generatedFilenames[index],\n                    customFields: (_entry_customFields = entry.customFields) === null || _entry_customFields === void 0 ? void 0 : _entry_customFields.map((cf)=>({\n                            id: cf.id,\n                            value: cf.value\n                        }))\n                };\n            });\n            const formData = {\n                clientId: values.clientId,\n                entries: entries\n            };\n            const result = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_7__.formSubmit)(_lib_routePath__WEBPACK_IMPORTED_MODULE_8__.trackSheets_routes.CREATE_TRACK_SHEETS, \"POST\", formData);\n            if (result.success) {\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.success(\"All TrackSheets created successfully\");\n                form.reset();\n                setTimeout(()=>{\n                    handleInitialSelection(initialAssociateId, initialClientId);\n                }, 100);\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(result.message || \"Failed to create TrackSheets\");\n            }\n            router.refresh();\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"An error occurred while creating the TrackSheets\");\n        }\n    }, [\n        form,\n        router,\n        generateFilename,\n        initialAssociateId,\n        initialClientId,\n        handleInitialSelection,\n        generatedFilenames\n    ]);\n    const addNewEntry = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const newIndex = fields.length;\n        append({\n            clientId: initialClientId,\n            company: \"\",\n            division: \"\",\n            invoice: \"\",\n            masterInvoice: \"\",\n            bol: \"\",\n            invoiceDate: new Date().toISOString().split(\"T\")[0],\n            receivedDate: new Date().toISOString().split(\"T\")[0],\n            shipmentDate: new Date().toISOString().split(\"T\")[0],\n            carrierName: \"\",\n            invoiceStatus: \"\",\n            manualMatching: \"\",\n            invoiceType: \"\",\n            currency: \"\",\n            qtyShipped: \"\",\n            weightUnitName: \"\",\n            quantityBilledText: \"\",\n            invoiceTotal: \"\",\n            savings: \"\",\n            ftpFileName: \"\",\n            ftpPage: \"\",\n            docAvailable: [],\n            notes: \"\",\n            mistake: \"\",\n            legrandAlias: \"\",\n            legrandCompanyName: \"\",\n            legrandAddress: \"\",\n            legrandZipcode: \"\",\n            shipperAlias: \"\",\n            shipperAddress: \"\",\n            shipperZipcode: \"\",\n            consigneeAlias: \"\",\n            consigneeAddress: \"\",\n            consigneeZipcode: \"\",\n            billtoAlias: \"\",\n            billtoAddress: \"\",\n            billtoZipcode: \"\",\n            customFields: []\n        });\n        setTimeout(()=>{\n            handleCompanyAutoPopulation(newIndex, initialClientId);\n            handleCustomFieldsFetch(newIndex, initialClientId);\n            if (companyFieldRefs.current[newIndex]) {\n                var _companyFieldRefs_current_newIndex, _companyFieldRefs_current_newIndex1, _companyFieldRefs_current_newIndex2;\n                const inputElement = ((_companyFieldRefs_current_newIndex = companyFieldRefs.current[newIndex]) === null || _companyFieldRefs_current_newIndex === void 0 ? void 0 : _companyFieldRefs_current_newIndex.querySelector(\"input\")) || ((_companyFieldRefs_current_newIndex1 = companyFieldRefs.current[newIndex]) === null || _companyFieldRefs_current_newIndex1 === void 0 ? void 0 : _companyFieldRefs_current_newIndex1.querySelector(\"button\")) || ((_companyFieldRefs_current_newIndex2 = companyFieldRefs.current[newIndex]) === null || _companyFieldRefs_current_newIndex2 === void 0 ? void 0 : _companyFieldRefs_current_newIndex2.querySelector(\"select\"));\n                if (inputElement) {\n                    inputElement.focus();\n                    try {\n                        inputElement.click();\n                    } catch (e) {}\n                }\n            }\n            updateFilenames();\n        }, 200);\n    }, [\n        append,\n        fields.length,\n        updateFilenames,\n        initialClientId,\n        handleCompanyAutoPopulation,\n        handleCustomFieldsFetch\n    ]);\n    const handleFormKeyDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        if (e.ctrlKey && (e.key === \"s\" || e.key === \"S\")) {\n            e.preventDefault();\n            form.handleSubmit(onSubmit)();\n        } else if (e.shiftKey && e.key === \"Enter\") {\n            e.preventDefault();\n            addNewEntry();\n        } else if (e.key === \"Enter\" && !e.ctrlKey && !e.shiftKey && !e.altKey) {\n            const activeElement = document.activeElement;\n            const isSubmitButton = (activeElement === null || activeElement === void 0 ? void 0 : activeElement.getAttribute(\"type\")) === \"submit\";\n            if (isSubmitButton) {\n                e.preventDefault();\n                form.handleSubmit(onSubmit)();\n            }\n        }\n    }, [\n        form,\n        onSubmit,\n        addNewEntry\n    ]);\n    const removeEntry = (index)=>{\n        if (fields.length > 1) {\n            remove(index);\n        } else {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"You must have at least one entry\");\n        }\n    };\n    const getFilteredDivisionOptions = (company, entryIndex)=>{\n        if (!company || !legrandData.length) {\n            return [];\n        }\n        if (entryIndex !== undefined) {\n            var _formValues_entries, _clientOptions_find;\n            const formValues = form.getValues();\n            const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[entryIndex];\n            const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || (entryIndex === 0 ? formValues.clientId : \"\");\n            const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n            if (entryClientName === \"LEGRAND\") {\n                const shipperAlias = form.getValues(\"entries.\".concat(entryIndex, \".shipperAlias\"));\n                const consigneeAlias = form.getValues(\"entries.\".concat(entryIndex, \".consigneeAlias\"));\n                const billtoAlias = form.getValues(\"entries.\".concat(entryIndex, \".billtoAlias\"));\n                const currentAlias = shipperAlias || consigneeAlias || billtoAlias;\n                if (currentAlias) {\n                    const selectedData = legrandData.find((data)=>{\n                        const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n                        return uniqueKey === currentAlias;\n                    });\n                    if (selectedData) {\n                        const baseAliasName = selectedData.aliasShippingNames && selectedData.aliasShippingNames !== \"NONE\" ? selectedData.aliasShippingNames : selectedData.legalName;\n                        const sameAliasEntries = legrandData.filter((data)=>{\n                            const dataAliasName = data.aliasShippingNames && data.aliasShippingNames !== \"NONE\" ? data.aliasShippingNames : data.legalName;\n                            return dataAliasName === baseAliasName;\n                        });\n                        const allDivisions = [];\n                        sameAliasEntries.forEach((entry)=>{\n                            if (entry.customeCode) {\n                                if (entry.customeCode.includes(\"/\")) {\n                                    const splitDivisions = entry.customeCode.split(\"/\").map((d)=>d.trim());\n                                    allDivisions.push(...splitDivisions);\n                                } else {\n                                    allDivisions.push(entry.customeCode);\n                                }\n                            }\n                        });\n                        const uniqueDivisions = Array.from(new Set(allDivisions.filter((code)=>code)));\n                        if (uniqueDivisions.length > 1) {\n                            const contextDivisions = uniqueDivisions.sort().map((code)=>({\n                                    value: code,\n                                    label: code\n                                }));\n                            return contextDivisions;\n                        } else {}\n                    }\n                }\n            }\n        }\n        const allDivisions = [];\n        legrandData.filter((data)=>data.businessUnit === company && data.customeCode).forEach((data)=>{\n            if (data.customeCode.includes(\"/\")) {\n                const splitDivisions = data.customeCode.split(\"/\").map((d)=>d.trim());\n                allDivisions.push(...splitDivisions);\n            } else {\n                allDivisions.push(data.customeCode);\n            }\n        });\n        const divisions = Array.from(new Set(allDivisions.filter((code)=>code))).sort().map((code)=>({\n                value: code,\n                label: code\n            }));\n        return divisions;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full px-2 py-3\",\n                children: showFullForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_3__.Form, {\n                    ...form,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: form.handleSubmit(onSubmit),\n                        onKeyDown: handleFormKeyDown,\n                        className: \"space-y-3\",\n                        children: [\n                            fields.map((field, index)=>{\n                                var _missingFields_index;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2 bg-gray-100 rounded-md px-3 py-2 border border-gray-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-5 h-5 bg-gray-600 rounded-full flex items-center justify-center text-white font-semibold text-xs\",\n                                                        children: index + 1\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1028,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-sm font-semibold text-gray-900\",\n                                                        children: [\n                                                            \"Entry #\",\n                                                            index + 1\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1031,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                lineNumber: 1027,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                            lineNumber: 1026,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-3 pb-3 border-b border-gray-100\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-50 rounded-md p-2 mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1044,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-sm font-semibold text-gray-900\",\n                                                                        children: \"Client Information\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1045,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1043,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 mb-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                            className: \"mt-2\",\n                                                                            form: form,\n                                                                            label: \"FTP File Name\",\n                                                                            name: \"entries.\".concat(index, \".ftpFileName\"),\n                                                                            type: \"text\",\n                                                                            isRequired: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1052,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1051,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_PageInput__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"mt-2\",\n                                                                        form: form,\n                                                                        label: \"FTP Page\",\n                                                                        name: \"entries.\".concat(index, \".ftpPage\"),\n                                                                        isRequired: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1061,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1068,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    \" \"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1050,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            (()=>{\n                                                                var _formValues_entries, _clientOptions_find;\n                                                                const formValues = form.getValues();\n                                                                const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                return entryClientName === \"LEGRAND\";\n                                                            })() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-3 mb-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            form: form,\n                                                                            entryIndex: index,\n                                                                            onLegrandDataChange: handleLegrandDataChange,\n                                                                            blockTitle: \"Shipper\",\n                                                                            fieldPrefix: \"shipper\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1085,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            form: form,\n                                                                            entryIndex: index,\n                                                                            onLegrandDataChange: handleLegrandDataChange,\n                                                                            blockTitle: \"Consignee\",\n                                                                            fieldPrefix: \"consignee\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1092,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            form: form,\n                                                                            entryIndex: index,\n                                                                            onLegrandDataChange: handleLegrandDataChange,\n                                                                            blockTitle: \"Bill-to\",\n                                                                            fieldPrefix: \"billto\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1099,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1084,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1083,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        ref: (el)=>{\n                                                                            companyFieldRefs.current[index] = el;\n                                                                        },\n                                                                        className: \"flex flex-col mb-1 [&_input]:h-10\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                            form: form,\n                                                                            label: \"Company\",\n                                                                            name: \"entries.\".concat(index, \".company\"),\n                                                                            type: \"text\",\n                                                                            disable: (()=>{\n                                                                                var _formValues_entries, _clientOptions_find;\n                                                                                const formValues = form.getValues();\n                                                                                const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                                const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                                const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                                return entryClientName === \"LEGRAND\";\n                                                                            })()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1118,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1112,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex flex-col\",\n                                                                        children: (()=>{\n                                                                            var _formValues_entries, _clientOptions_find, _entries_index;\n                                                                            const formValues = form.getValues();\n                                                                            const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                            const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                            const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                            const isLegrand = entryClientName === \"LEGRAND\";\n                                                                            return isLegrand ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                form: form,\n                                                                                name: \"entries.\".concat(index, \".division\"),\n                                                                                label: \"Division\",\n                                                                                placeholder: \"Search Division\",\n                                                                                disabled: false,\n                                                                                options: getFilteredDivisionOptions((entries === null || entries === void 0 ? void 0 : (_entries_index = entries[index]) === null || _entries_index === void 0 ? void 0 : _entries_index.company) || \"\", index)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1149,\n                                                                                columnNumber: 33\n                                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                form: form,\n                                                                                label: \"Division\",\n                                                                                name: \"entries.\".concat(index, \".division\"),\n                                                                                type: \"text\",\n                                                                                placeholder: \"Enter Division\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1161,\n                                                                                columnNumber: 33\n                                                                            }, undefined);\n                                                                        })()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1137,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex flex-col\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"mt-0\",\n                                                                            form: form,\n                                                                            name: \"entries.\".concat(index, \".carrierName\"),\n                                                                            label: \"Select Carrier\",\n                                                                            placeholder: \"Search Carrier\",\n                                                                            isRequired: true,\n                                                                            options: (carrierOptions === null || carrierOptions === void 0 ? void 0 : carrierOptions.filter((carrier)=>{\n                                                                                const currentEntries = form.getValues(\"entries\") || [];\n                                                                                const isSelectedInOtherEntries = currentEntries.some((entry, entryIndex)=>entryIndex !== index && entry.carrierName === carrier.value);\n                                                                                return !isSelectedInOtherEntries;\n                                                                            })) || [],\n                                                                            onValueChange: ()=>{\n                                                                                setTimeout(()=>updateFilenames(), 100);\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1172,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1171,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1111,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1042,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                    lineNumber: 1040,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-3 pb-3 border-b border-gray-100\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-orange-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1203,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-sm font-semibold text-gray-900\",\n                                                                    children: \"Document Information\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1204,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1202,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Master Invoice\",\n                                                                    name: \"entries.\".concat(index, \".masterInvoice\"),\n                                                                    type: \"text\",\n                                                                    onBlur: (e)=>{\n                                                                        const masterInvoiceValue = e.target.value;\n                                                                        if (masterInvoiceValue) {\n                                                                            form.setValue(\"entries.\".concat(index, \".invoice\"), masterInvoiceValue);\n                                                                        }\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1209,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Invoice\",\n                                                                    name: \"entries.\".concat(index, \".invoice\"),\n                                                                    type: \"text\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1224,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"BOL\",\n                                                                    name: \"entries.\".concat(index, \".bol\"),\n                                                                    type: \"text\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1231,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1208,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Received Date\",\n                                                                    name: \"entries.\".concat(index, \".receivedDate\"),\n                                                                    type: \"date\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1240,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Invoice Date\",\n                                                                    name: \"entries.\".concat(index, \".invoiceDate\"),\n                                                                    type: \"date\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1247,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Shipment Date\",\n                                                                    name: \"entries.\".concat(index, \".shipmentDate\"),\n                                                                    type: \"date\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1254,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1239,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                    lineNumber: 1201,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4 pb-4 border-b border-gray-100\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1267,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-sm font-semibold text-gray-900\",\n                                                                    children: \"Financial & Shipment\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1268,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1266,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Invoice Total\",\n                                                                    name: \"entries.\".concat(index, \".invoiceTotal\"),\n                                                                    type: \"number\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1273,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    form: form,\n                                                                    name: \"entries.\".concat(index, \".currency\"),\n                                                                    label: \"Currency\",\n                                                                    placeholder: \"Search currency\",\n                                                                    isRequired: true,\n                                                                    options: [\n                                                                        {\n                                                                            value: \"USD\",\n                                                                            label: \"USD\"\n                                                                        },\n                                                                        {\n                                                                            value: \"CAD\",\n                                                                            label: \"CAD\"\n                                                                        },\n                                                                        {\n                                                                            value: \"EUR\",\n                                                                            label: \"EUR\"\n                                                                        }\n                                                                    ]\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1280,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Quantity Shipped\",\n                                                                    name: \"entries.\".concat(index, \".qtyShipped\"),\n                                                                    type: \"number\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1292,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Weight Unit\",\n                                                                    name: \"entries.\".concat(index, \".weightUnitName\"),\n                                                                    type: \"text\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1299,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1272,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 mt-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Savings\",\n                                                                    name: \"entries.\".concat(index, \".savings\"),\n                                                                    type: \"text\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1308,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Invoice Type\",\n                                                                    name: \"entries.\".concat(index, \".invoiceType\"),\n                                                                    type: \"text\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1314,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Quantity Billed Text\",\n                                                                    name: \"entries.\".concat(index, \".quantityBilledText\"),\n                                                                    type: \"text\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1321,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Invoice Status\",\n                                                                    name: \"entries.\".concat(index, \".invoiceStatus\"),\n                                                                    type: \"text\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1327,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1307,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                    lineNumber: 1265,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-gray-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1340,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-sm font-semibold text-gray-900\",\n                                                                    children: \"Additional Information\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1341,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1339,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Manual Matching\",\n                                                                    name: \"entries.\".concat(index, \".manualMatching\"),\n                                                                    type: \"text\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1346,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Notes\",\n                                                                    name: \"entries.\".concat(index, \".notes\"),\n                                                                    type: \"text\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1353,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Mistake\",\n                                                                    name: \"entries.\".concat(index, \".mistake\"),\n                                                                    type: \"text\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1359,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormCheckboxGroup__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Documents Available\",\n                                                                        name: \"entries.\".concat(index, \".docAvailable\"),\n                                                                        options: [\n                                                                            {\n                                                                                label: \"Invoice\",\n                                                                                value: \"Invoice\"\n                                                                            },\n                                                                            {\n                                                                                label: \"BOL\",\n                                                                                value: \"Bol\"\n                                                                            },\n                                                                            {\n                                                                                label: \"POD\",\n                                                                                value: \"Pod\"\n                                                                            },\n                                                                            {\n                                                                                label: \"Packages List\",\n                                                                                value: \"Packages List\"\n                                                                            }\n                                                                        ],\n                                                                        className: \"flex-row gap-2 text-xs\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1366,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1365,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1345,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                    lineNumber: 1338,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                (()=>{\n                                                    var _formValues_entries;\n                                                    const formValues = form.getValues();\n                                                    const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                    const customFields = (entry === null || entry === void 0 ? void 0 : entry.customFields) || [];\n                                                    return Array.isArray(customFields) && customFields.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"pt-3 border-t border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-purple-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1398,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-sm font-semibold text-gray-900\",\n                                                                        children: [\n                                                                            \"Custom Fields (\",\n                                                                            customFields.length,\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1399,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1397,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2\",\n                                                                children: customFields.map((cf, cfIdx)=>{\n                                                                    const fieldType = cf.type || \"TEXT\";\n                                                                    const isAutoField = fieldType === \"AUTO\";\n                                                                    const autoOption = cf.autoOption;\n                                                                    let inputType = \"text\";\n                                                                    if (fieldType === \"DATE\" || isAutoField && autoOption === \"DATE\") {\n                                                                        inputType = \"date\";\n                                                                    } else if (fieldType === \"NUMBER\") {\n                                                                        inputType = \"number\";\n                                                                    }\n                                                                    const fieldLabel = isAutoField ? \"\".concat(cf.name, \" (Auto - \").concat(autoOption, \")\") : cf.name;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: fieldLabel,\n                                                                        name: \"entries.\".concat(index, \".customFields.\").concat(cfIdx, \".value\"),\n                                                                        type: inputType,\n                                                                        className: \"w-full\",\n                                                                        disable: isAutoField\n                                                                    }, cf.id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1424,\n                                                                        columnNumber: 33\n                                                                    }, undefined);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1403,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, \"custom-fields-\".concat(index, \"-\").concat(customFieldsRefresh), true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1393,\n                                                        columnNumber: 25\n                                                    }, undefined) : null;\n                                                })(),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"pt-3 border-t border-gray-100 mt-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-end space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.Tooltip, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipTrigger, {\n                                                                        asChild: true,\n                                                                        tabIndex: -1,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-5 h-5 rounded-full flex items-center justify-center text-white font-bold text-xs cursor-help transition-colors duration-200 \".concat(filenameValidation[index] ? \"bg-green-500 hover:bg-green-600\" : \"bg-orange-500 hover:bg-orange-600\"),\n                                                                            tabIndex: -1,\n                                                                            role: \"button\",\n                                                                            \"aria-label\": \"Entry \".concat(index + 1, \" filename status\"),\n                                                                            children: \"!\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1446,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1445,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipContent, {\n                                                                        side: \"top\",\n                                                                        align: \"center\",\n                                                                        className: \"z-[9999]\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm max-w-md\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium mb-1\",\n                                                                                    children: [\n                                                                                        \"Entry #\",\n                                                                                        index + 1,\n                                                                                        \" Filename\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1465,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                filenameValidation[index] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"font-medium text-green-600 mb-2\",\n                                                                                            children: \"Filename Generated\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1470,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs font-mono break-all bg-gray-100 p-2 rounded text-black\",\n                                                                                            children: generatedFilenames[index]\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1473,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1469,\n                                                                                    columnNumber: 33\n                                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"font-medium text-orange-600 mb-1\",\n                                                                                            children: \"Please fill the form to generate filename\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1479,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-gray-600 mb-2\",\n                                                                                            children: \"Missing fields:\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1482,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                                            className: \"list-disc list-inside space-y-1\",\n                                                                                            children: (_missingFields_index = missingFields[index]) === null || _missingFields_index === void 0 ? void 0 : _missingFields_index.map((field, fieldIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                                    className: \"text-xs\",\n                                                                                                    children: field\n                                                                                                }, fieldIndex, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                                    lineNumber: 1488,\n                                                                                                    columnNumber: 41\n                                                                                                }, undefined))\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1485,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1478,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1464,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1459,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1444,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                type: \"button\",\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                className: \"h-7 w-7 p-0 hover:bg-red-50 hover:border-red-200\",\n                                                                onClick: ()=>removeEntry(index),\n                                                                disabled: fields.length <= 1,\n                                                                tabIndex: -1,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"h-3 w-3 text-red-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1512,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1503,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            index === fields.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.Tooltip, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipTrigger, {\n                                                                        asChild: true,\n                                                                        tabIndex: -1,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                            type: \"button\",\n                                                                            variant: \"outline\",\n                                                                            size: \"sm\",\n                                                                            className: \"h-7 w-7 p-0 hover:bg-green-50 hover:border-green-200\",\n                                                                            onClick: addNewEntry,\n                                                                            tabIndex: -1,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                className: \"h-3 w-3 text-green-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1525,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1517,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1516,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipContent, {\n                                                                        side: \"top\",\n                                                                        align: \"center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs\",\n                                                                            children: \"Add New Entry (Shift+Enter)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1529,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1528,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1515,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1442,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                    lineNumber: 1441,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                            lineNumber: 1038,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, field.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                    lineNumber: 1024,\n                                    columnNumber: 17\n                                }, undefined);\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        type: \"submit\",\n                                        className: \"px-6 py-2 rounded-lg font-medium transition-all duration-200 shadow-md hover:shadow-lg text-sm\",\n                                        children: \"Save\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                        lineNumber: 1544,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                    lineNumber: 1543,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                lineNumber: 1542,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                        lineNumber: 1018,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                    lineNumber: 1017,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                lineNumber: 955,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n            lineNumber: 953,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n        lineNumber: 952,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CreateTrackSheet, \"tnkI5A80t5p2rfXOW68gzibbyK4=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useForm,\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useWatch,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useFieldArray\n    ];\n});\n_c = CreateTrackSheet;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CreateTrackSheet);\nvar _c;\n$RefreshReg$(_c, \"CreateTrackSheet\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/trackSheets/v2/createTrackSheet.tsx\n"));

/***/ })

});