import express, { Request, Response } from "express";
import userRoutes from "./corporation/routes/userroutes";
import clientRoutes from "./corporation/routes/client/clientroutes";
import corporationRoutes from "./corporation/routes/corporationroutes";
import workReportRoutes from "./corporation/routes/workreport/workreportroutes";
import carrierRoutes from "./corporation/routes/carrier/carrierroutes";
import superAdminRoutes from "./superadmin/routes/superadminroutes";
import workTypeRoutes from "./corporation/routes/worktype/worktyperoutes";
import dailyPlanningRoutes from "./corporation/routes/dailyPlanning/dailyPlanningRoutes";
import dailyPlanningDetailsRoutes from "./corporation/routes/dailyPlanningDetails/dailyPlanningDetailsRoutes";
import uploadfilerouter from "./corporation/routes/uploadFile/uploadfilerouter";
import categoryroutes from "./corporation/routes/category/categoryroutes";
import branchroutes from "./corporation/routes/branch/branchroutes";
import searchRoutes from "./search/searchRoutes";

import cors from "cors";
import cookieParser from "cookie-parser";
import clientCarrierRoutes from "./corporation/routes/clientCarrier/clientCarrierRoutes";
import rolesPermissionRoutes from "./corporation/routes/rolesPermission/rolesPermissionRoutes";
import "./corporation/controllers/uploadfile/incrementAge"
import userTitleRoutes from "./corporation/routes/usertitle/usertitleroutes";

const crypto = require("crypto");
import multer from "multer";
import associateroutes from "./corporation/routes/associate/associateroutes";
import customizeReportRoutes from "./corporation/routes/customizeReport/customizeReportRoutes";
import customFieldRoutes from "./corporation/routes/customfields/custom-fields-routes";

import trackSheetsRoutes from "./corporation/routes/trackSheets/trackSheetsRoutes"
import clientCustomFieldsRoutes from "./corporation/routes/clientCustomFields/ClientCustomFieldsRoute";
import rulesRoutes from "./corporation/routes/legrandMappings/legrandMappingsRoutes";
import legrandMappingsRoutes from "./corporation/routes/legrandMappings/legrandMappingsRoutes";
import manualMatchingMappingsRoutes from "./corporation/routes/manualMatchingMapping/manualMatchingMappingRoutes";

const app = express();
app.use(express.json());
app.use(
  cors({
    origin: "http://localhost:3000",
    credentials: true,
  })
);
const jwtSecret = crypto.randomBytes(32).toString("hex");
app.use(express.json({ limit: "150mb" }));
app.use(express.urlencoded({ limit: "150mb", extended: true }));

app.use(cookieParser());
app.use("/api/users", userRoutes);
app.use("/api/clients", clientRoutes);
app.use("/api/corporation", corporationRoutes);
app.use("/api/workreport", workReportRoutes);
app.use("/api/carrier", carrierRoutes);
app.use("/api/superAdmin", superAdminRoutes);
app.use("/api/worktype", workTypeRoutes);
app.use("/api/client-carrier", clientCarrierRoutes);
app.use("/api/category", categoryroutes);
app.use("/api/branch", branchroutes);
app.use("/api/client-carreir", clientCarrierRoutes);
app.use("/api/rolespermission", rolesPermissionRoutes);
app.use("/api/dailyplanning", dailyPlanningRoutes);
app.use("/api/dailyplanningdetails", dailyPlanningDetailsRoutes);
app.use("/api/upload", uploadfilerouter);
app.use("/api/usertitle",userTitleRoutes)
app.use("/api/search", searchRoutes);
app.use("/api/associate", associateroutes);
app.use("/api/customizeReport", customizeReportRoutes);
app.use("/api", customFieldRoutes);

app.use("/api/track-sheets", trackSheetsRoutes);
app.use("/api/client-custom-fields", clientCustomFieldsRoutes);
app.use("/api/legrand-mappings", legrandMappingsRoutes);
app.use("/api/manual-matching-mappings", manualMatchingMappingsRoutes);

app.get("/", (req: Request, res: Response) => {
  res.send("Welcome to the API!");
});

export default app;
