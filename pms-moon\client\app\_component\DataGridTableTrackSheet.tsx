"use client";
import React, { useState, useEffect, useRef, useMemo } from "react";
import { Fa<PERSON><PERSON><PERSON>, FaSearch } from "react-icons/fa";
import { useRouter, useSearchParams, usePathname } from "next/navigation";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import Pagination from "./Pagination";
import { AgGridReact } from "ag-grid-react";
import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-alpine.css";
import { AllCommunityModule, ModuleRegistry } from "ag-grid-community";
import { cn } from "@/lib/utils";
import { CustomInput } from "@/components/ui/customInput";
import { BiHide, BiShow } from "react-icons/bi";
import { LuSearchX } from "react-icons/lu";

ModuleRegistry.registerModules([AllCommunityModule]);

interface DataGridTableProps {
  columns: any[];
  data: any[];
  isLoading?: boolean;
  showColDropDowns?: boolean;
  filter?: boolean;
  filter1PlaceHolder?: string;
  filter2?: boolean;
  filter3?: boolean;
  filter3view?: JSX.Element;
  filter4?: boolean;
  filter4view?: JSX.Element;
  filter5?: boolean;
  filter5view?: JSX.Element;
  total?: boolean;
  totalview?: JSX.Element;
  filter_column?: string;
  filter_column2?: string;
  filter_column3?: string;
  filter_column4?: string;
  overflow?: boolean;
  totalPages?: number;
  showPageEntries?: boolean;
  className?: string;
  showSearchColumn?: boolean;
  pageSize?: number;
  isTimerRunning?: any;
  setIsTimerRunning?: any;
  onFilteredDataChange?: (filteredData: any[]) => void;
  customFieldsMap?: { [id: string]: { name: string; type: string } };
}

const DataGridTableTrackSheet = ({
  columns,
  data,
  isLoading,
  showColDropDowns,
  filter,
  filter2,
  filter3 = false,
  filter3view,
  filter4,
  filter4view,
  filter5,
  filter5view,
  total,
  totalview,
  filter_column,
  filter_column2,
  filter_column3,
  filter_column4,
  totalPages,
  showPageEntries,
  className,
  filter1PlaceHolder,
  showSearchColumn = true,
  pageSize,
  customFieldsMap,
}: DataGridTableProps) => {
  const [page, setPage] = useState<number>(pageSize || 50);
  const [selectedColumns, setSelectedColumns] = useState<string[]>([]);
  const searchParams = useSearchParams();
  const params = new URLSearchParams(searchParams);
  const pathname = usePathname();
  const { replace } = useRouter();

  const currentPage = Number(searchParams.get("page")) || 1;
  const currentPageSize = Number(searchParams.get("pageSize")) || pageSize || 50;

  const gridRef = useRef<AgGridReact>(null);

  const [columnData, setColumnData] = useState("");
  const [columnVisibility, setColumnVisibility] = useState<any>({});

  const [inputValues, setInputValues] = useState<{ [key: string]: string }>({});

  const [searchTerms, setSearchTerms] = useState<{ [key: string]: string[] }>(
    {}
  );

  const [filtersCollapsed, setFiltersCollapsed] = useState(true);
  const COLLAPSE_COUNT = 6;

  const processedData = useMemo(() => {
    return data?.map((item, index) => ({
      ...item,
      stableId: (currentPage - 1) * currentPageSize + index + 1,
    }));
  }, [data, currentPage, currentPageSize]);

  const columnsWithSerialNumber = useMemo(() => {
    return [
      {
        headerName: "Sr. No.",
        field: "stableId",
        sortable: true,
        width: 80,
        minWidth: 80,
        maxWidth: 80,
        cellStyle: {
          textAlign: "center",
        },
        pinned: "left",

        comparator: (valueA: number, valueB: number) => valueA - valueB,
      },
      ...columns.filter((col) => col.field !== "sr_no"),
    ];
  }, [columns]);

  useEffect(() => {
    const initialVisibility: any = {};
    columnsWithSerialNumber.forEach((col) => {
      initialVisibility[col.field] = true;
    });
    setColumnVisibility(initialVisibility);
  }, [columnsWithSerialNumber]);

  useMemo(() => {
    if (gridRef.current && gridRef.current.api) {
      if (isLoading) {
        gridRef.current.api.showLoadingOverlay();
      } else if (!processedData || processedData.length === 0) {
        gridRef.current.api.showNoRowsOverlay();
      } else {
        gridRef.current.api.hideOverlay();
      }
    }
  }, [isLoading, processedData]);

  const handlePageChange = (e: any) => {
    const newPageSize = parseInt(e.target.value);
    setPage(newPageSize);
    if (totalPages) {
      params.set("pageSize", newPageSize?.toString());
      replace(`${pathname}?${params.toString()}`);
    }
  };

  const handleColumnSelection = (columnKey: string, columnHeader: string) => {
    setSelectedColumns((prevSelectedColumns) => {
      let updatedColumns;

      if (prevSelectedColumns.includes(columnKey)) {
        updatedColumns = prevSelectedColumns.filter((col) => col !== columnKey);

        const updatedParams = new URLSearchParams(searchParams);

        if (columnHeader === "Received Date") {
          updatedParams.delete("recievedFDate");
          updatedParams.delete("recievedTDate");
        } else {
          updatedParams.delete(columnHeader);
        }

        replace(`${pathname}?${updatedParams.toString()}`);
      } else {
        updatedColumns = [...prevSelectedColumns, columnKey];

        const updatedParams = new URLSearchParams(searchParams);
        if (columnHeader === "Received Date") {
          updatedParams.set("recievedFDate", "");
          updatedParams.set("recievedTDate", "");
        } else {
          updatedParams.set(columnHeader, "");
        }
        replace(`${pathname}?${updatedParams.toString()}`);
      }

      return updatedColumns;
    });
  };

  const handleKeyDown = (
    e: React.KeyboardEvent<HTMLInputElement>,
    columnName: string
  ) => {
    let paramKey = columnName;
    if (!paramKey.startsWith("customField_")) {
      const col = columnsWithSerialNumber.find(
        (c) => c.headerName === columnName || c.field === columnName
      );
      if (col && col.field && col.field.startsWith("customField_")) {
        paramKey = col.field;
      }
    }
    const currentInput = inputValues[columnName]?.trim();

    if (e.key === "Enter") {
      e.preventDefault();
      if (currentInput && !searchTerms[columnName]?.includes(currentInput)) {
        const updated = [...(searchTerms[columnName] || []), currentInput];
        updateSearchParams(columnName, updated);
        setSearchTerms({ ...searchTerms, [columnName]: updated });
        setInputValues({ ...inputValues, [columnName]: "" });
      }
    }

    if (
      e.key === "Backspace" &&
      !currentInput &&
      searchTerms[columnName]?.length > 0
    ) {
      const updated = [...searchTerms[columnName]];
      updated.pop();
      updateSearchParams(columnName, updated);
      setSearchTerms({ ...searchTerms, [columnName]: updated });
    }
  };

  const handleRemoveTerm = (columnName: string, term: string) => {
    const col = columnsWithSerialNumber.find(
      (c) => c.headerName === columnName || c.field === columnName
    );
    const key = col ? col.headerName : columnName;

    let paramKey = columnName;
    if (!paramKey.startsWith("customField_")) {
      if (col && col.field && col.field.startsWith("customField_")) {
        paramKey = col.field;
      }
    }

    if (paramKey.startsWith("customField_")) {
      const newSearchTerms = { ...searchTerms };
      Object.keys(newSearchTerms).forEach((k) => {
        if (k === key || k === paramKey || k.startsWith(paramKey)) {
          newSearchTerms[k] = [];
        }
      });
      setSearchTerms(newSearchTerms);
      setInputValues((prev) => {
        const newValues = { ...prev };
        Object.keys(newValues).forEach((k) => {
          if (k === key || k === paramKey || k.startsWith(paramKey)) {
            newValues[k] = "";
          }
        });
        return newValues;
      });
      const updatedParams = new URLSearchParams(searchParams);
      updatedParams.delete(paramKey);
      replace(`${pathname}?${updatedParams.toString()}`);
      return;
    }

    const updated = (searchTerms[paramKey] || []).filter((t) => t !== term);
    updateSearchParams(paramKey, updated);
    setSearchTerms({ ...searchTerms, [paramKey]: updated });
  };

  const updateSearchParams = (columnName: string, terms: string[]) => {
    let paramKey = columnName;
    if (!paramKey.startsWith("customField_")) {
      const col = columnsWithSerialNumber.find(
        (c) => c.headerName === columnName || c.field === columnName
      );
      if (col && col.field && col.field.startsWith("customField_")) {
        paramKey = col.field;
      }
    }
    const updatedParams = new URLSearchParams(searchParams);
    if (terms.length > 0) {
      updatedParams.set(paramKey, terms.join(","));
      updatedParams.set("page", "1");
    } else {
      updatedParams.delete(paramKey);
    }
    replace(`${pathname}?${updatedParams.toString()}`);
  };

  const filterColumns = selectedColumns.length
    ? selectedColumns
        .map((columnKey) =>
          columnsWithSerialNumber.find((col) => col.field === columnKey)
        )
        .filter((col) => col !== undefined)
    : [];

  const toggleColumnVisibility = (field: string, isVisible: boolean) => {
    setColumnVisibility((prev) => ({
      ...prev,
      [field]: isVisible,
    }));

    if (gridRef.current) {
      gridRef.current.api.setColumnsVisible([field], isVisible);
    }
  };

  return (
    <div className={`animate-in fade-in duration-1000`}>
      <div className="flex justify-between w-full lg:w-full lg:flex lg:justify-end mb-3 animate-in fade-in duration-1000 gap-5">
        {filtersCollapsed && showPageEntries && (
          <select
            value={page}
            onChange={handlePageChange}
            className=" border-2 rounded-md  items-center justify-center cursor-pointer h-10"
          >
            <option value={10}>10</option>
            <option value={15}>15</option>
            <option value={25}>25</option>
            <option value={50}>50</option>
            <option value={100}>100</option>
            <option value={250}>250</option>
          </select>
        )}

        {filtersCollapsed && showColDropDowns && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <p className="border-2 rounded-md flex items-center justify-center  cursor-pointer h-10 w-10">
                <FaFilter />
              </p>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              className="bg-white border-none dark:bg-gray-900 dark:ring-1 dark:ring-gray-800 overflow-y-auto max-h-80"
              onSelect={(e) => e.preventDefault()}
            >
              {columnsWithSerialNumber
                .filter((column) => column.hideable !== false)
                .map((column) => (
                  <DropdownMenuCheckboxItem
                    key={column.field}
                    className="capitalize cursor-pointer"
                    checked={columnVisibility[column.field]}
                    onCheckedChange={(value) =>
                      toggleColumnVisibility(column.field, value)
                    }
                    onSelect={(e) => e.preventDefault()}
                  >
                    {column.headerName || column.field}
                  </DropdownMenuCheckboxItem>
                ))}
            </DropdownMenuContent>
          </DropdownMenu>
        )}

        {filtersCollapsed && showSearchColumn && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <p className="border-2 rounded-md flex items-center justify-center  cursor-pointer h-10 w-10">
                <FaSearch className="text-base" />
              </p>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              className="mt-2 bg-white border-none dark:bg-gray-900 dark:ring-1 dark:ring-gray-800 max-h-96 min-w-[260px]"
              onSelect={(e) => e.preventDefault()}
            >
              <div className="px-3 py-2 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 sticky top-0 z-10">
                <div className="flex items-center justify-between mb-2">
                  <p className="font-semibold text-sm">Select Columns</p>
                  <span className="text-xs text-gray-500">
                    {selectedColumns.length} selected
                  </span>
                </div>
                <Input
                  placeholder="Search columns..."
                  value={columnData}
                  onChange={(e) => setColumnData(e.target.value)}
                  className="mt-1 mb-2"
                />
                <div className="flex gap-2 mb-2 ">
                  <button
                    className="text-xs text-blue-600 underline"
                    type="button"
                    onClick={() =>
                      setSelectedColumns(
                        columnsWithSerialNumber
                          .filter(
                            (col) =>
                              col.field !== "action" &&
                              col.field !== "sr_no" &&
                              col.field !== "stableId"
                          )
                          .map((col) => col.field)
                      )
                    }
                  >
                    Select All
                  </button>
                </div>
              </div>
              <div className="overflow-y-auto max-h-60">
                {columnsWithSerialNumber
                  .filter(
                    (item: any) =>
                      item.field !== "action" &&
                      item.field !== "sr_no" &&
                      item.field !== "stableId" &&
                      (!columnData ||
                        (item.headerName || item.field)
                          .toLowerCase()
                          .includes(columnData.toLowerCase()))
                  )
                  .map((item: any, id: any) => (
                    <DropdownMenuCheckboxItem
                      key={id}
                      checked={selectedColumns.includes(item.field)}
                      onCheckedChange={() =>
                        handleColumnSelection(
                          item.field,
                          item.headerName || item.field
                        )
                      }
                      className="capitalize cursor-pointer"
                      onSelect={(e) => e.preventDefault()}
                    >
                      {item.headerName}
                    </DropdownMenuCheckboxItem>
                  ))}
              </div>
            </DropdownMenuContent>
          </DropdownMenu>
        )}

        {filterColumns.length > 0 && (
          <div className="relative w-full mb-3">
            <div
              className="flex flex-nowrap gap-2 items-center overflow-x-auto bg-gray-50 border border-gray-200 rounded-md py-2 px-2"
              style={{ scrollbarWidth: "thin", minHeight: 50 }}
            >
              {filterColumns.length > COLLAPSE_COUNT && (
                <div className="flex-shrink-0 sticky left-0 z-10 bg-gray-50">
                  <button
                    className="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300 text-xs border border-gray-300 mr-2"
                    type="button"
                    onClick={() => setFiltersCollapsed((prev) => !prev)}
                  >
                    {filtersCollapsed ? (
                      <BiShow className="w-5 h-5" title="Show Filters" />
                    ) : (
                      <BiHide className="w-5 h-5" title="Hide Filters" />
                    )}
                  </button>
                </div>
              )}
              {filterColumns
                .slice(
                  0,
                  filtersCollapsed && filterColumns.length > COLLAPSE_COUNT
                    ? COLLAPSE_COUNT
                    : filterColumns.length
                )
                .map((col, index) => {
                  if (col.headerName === "Received Date") {
                    return (
                      <div
                        key={index}
                        className="flex flex-col min-w-[180px] relative"
                      >
                        <label className="text-xs text-gray-600 mb-1">
                          Recieved Date (From)
                        </label>
                        <Input
                          type="date"
                          value={searchParams.get("recievedFDate") || ""}
                          onChange={(e) => {
                            const updatedParams = new URLSearchParams(
                              searchParams
                            );
                            updatedParams.set("recievedFDate", e.target.value);
                            updatedParams.set("page", "1");
                            replace(`${pathname}?${updatedParams.toString()}`);
                          }}
                          className="mb-1 pr-6"
                        />
                        <label className="text-xs text-gray-600 mb-1">
                          Recieved Date (To)
                        </label>
                        <Input
                          type="date"
                          value={searchParams.get("recievedTDate") || ""}
                          onChange={(e) => {
                            const updatedParams = new URLSearchParams(
                              searchParams
                            );
                            updatedParams.set("recievedTDate", e.target.value);
                            updatedParams.set("page", "1");
                            replace(`${pathname}?${updatedParams.toString()}`);
                          }}
                          className="mb-1 pr-6"
                        />
                        {(searchParams.get("recievedFDate") ||
                          searchParams.get("recievedTDate")) && (
                          <button
                            className="absolute right-1 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-700 text-lg mt-2"
                            type="button"
                            onClick={() => {
                              const updatedParams = new URLSearchParams(
                                searchParams
                              );
                              updatedParams.delete("recievedFDate");
                              updatedParams.delete("recievedTDate");
                              replace(
                                `${pathname}?${updatedParams.toString()}`
                              );
                            }}
                            title="Clear"
                          >
                            ×
                          </button>
                        )}
                      </div>
                    );
                  }
                  if (col.headerName === "Invoice Date") {
                    return (
                      <div
                        key={index}
                        className="flex flex-col min-w-[180px] relative"
                      >
                        <label className="text-xs text-gray-600 mb-1">
                          Invoice Date (From)
                        </label>
                        <Input
                          type="date"
                          value={searchParams.get("invoiceFDate") || ""}
                          onChange={(e) => {
                            const updatedParams = new URLSearchParams(
                              searchParams
                            );
                            updatedParams.set("invoiceFDate", e.target.value);
                            updatedParams.set("page", "1");
                            replace(`${pathname}?${updatedParams.toString()}`);
                          }}
                          className="mb-1 pr-6"
                        />
                        <label className="text-xs text-gray-600 mb-1">
                          Invoice Date (To)
                        </label>
                        <Input
                          type="date"
                          value={searchParams.get("invoiceTDate") || ""}
                          onChange={(e) => {
                            const updatedParams = new URLSearchParams(
                              searchParams
                            );
                            updatedParams.set("invoiceTDate", e.target.value);
                            updatedParams.set("page", "1");
                            replace(`${pathname}?${updatedParams.toString()}`);
                          }}
                          className="mb-1 pr-6"
                        />
                        {(searchParams.get("invoiceFDate") ||
                          searchParams.get("invoiceTDate")) && (
                          <button
                            className="absolute right-1 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-700 text-lg mt-2"
                            type="button"
                            onClick={() => {
                              const updatedParams = new URLSearchParams(
                                searchParams
                              );
                              updatedParams.delete("invoiceFDate");
                              updatedParams.delete("invoiceTDate");
                              replace(
                                `${pathname}?${updatedParams.toString()}`
                              );
                            }}
                            title="Clear"
                          >
                            ×
                          </button>
                        )}
                      </div>
                    );
                  }
                  if (col.headerName === "Shipment Date") {
                    return (
                      <div
                        key={index}
                        className="flex flex-col min-w-[180px] relative"
                      >
                        <label className="text-xs text-gray-600 mb-1">
                          Shipment Date (From)
                        </label>
                        <Input
                          type="date"
                          value={searchParams.get("shipmentFDate") || ""}
                          onChange={(e) => {
                            const updatedParams = new URLSearchParams(
                              searchParams
                            );
                            updatedParams.set("shipmentFDate", e.target.value);
                            updatedParams.set("page", "1");
                            replace(`${pathname}?${updatedParams.toString()}`);
                          }}
                          className="mb-1 pr-6"
                        />
                        <label className="text-xs text-gray-600 mb-1">
                          Shipment Date (To)
                        </label>
                        <Input
                          type="date"
                          value={searchParams.get("shipmentTDate") || ""}
                          onChange={(e) => {
                            const updatedParams = new URLSearchParams(
                              searchParams
                            );
                            updatedParams.set("shipmentTDate", e.target.value);
                            updatedParams.set("page", "1");
                            replace(`${pathname}?${updatedParams.toString()}`);
                          }}
                          className="mb-1 pr-6"
                        />
                        {(searchParams.get("shipmentFDate") ||
                          searchParams.get("shipmentTDate")) && (
                          <button
                            className="absolute right-1 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-700 text-lg mt-2 "
                            type="button"
                            onClick={() => {
                              const updatedParams = new URLSearchParams(
                                searchParams
                              );
                              updatedParams.delete("shipmentFDate");
                              updatedParams.delete("shipmentTDate");
                              replace(
                                `${pathname}?${updatedParams.toString()}`
                              );
                            }}
                            title="Clear"
                          >
                            ×
                          </button>
                        )}
                      </div>
                    );
                  }
                  const isCustomField = col.field.startsWith("customField_");
                  const customFieldId = isCustomField
                    ? col.field.replace("customField_", "")
                    : null;
                  const isDateType =
                    isCustomField &&
                    customFieldsMap &&
                    customFieldsMap[customFieldId]?.type?.toLowerCase() ===
                      "date";
                  if (isDateType) {
                    const fromKey = `customField_${customFieldId}_from`;
                    const toKey = `customField_${customFieldId}_to`;
                    return (
                      <div
                        key={index}
                        className="flex flex-col min-w-[180px] relative"
                      >
                        <label className="text-xs text-gray-600 mb-1">
                          {customFieldsMap[customFieldId].name} (From)
                        </label>
                        <Input
                          type="date"
                          value={searchParams.get(fromKey) || ""}
                          onChange={(e) => {
                            const updatedParams = new URLSearchParams(
                              searchParams
                            );
                            if (e.target.value)
                              updatedParams.set(fromKey, e.target.value);
                            else updatedParams.delete(fromKey);
                            updatedParams.set("page", "1");
                            replace(`${pathname}?${updatedParams.toString()}`);
                          }}
                          className="mb-1 pr-6"
                        />
                        <label className="text-xs text-gray-600 mb-1">
                          {customFieldsMap[customFieldId].name} (To)
                        </label>
                        <Input
                          type="date"
                          value={searchParams.get(toKey) || ""}
                          onChange={(e) => {
                            const updatedParams = new URLSearchParams(
                              searchParams
                            );
                            if (e.target.value)
                              updatedParams.set(toKey, e.target.value);
                            else updatedParams.delete(toKey);
                            updatedParams.set("page", "1");
                            replace(`${pathname}?${updatedParams.toString()}`);
                          }}
                          className="pr-6"
                        />
                        {(searchParams.get(fromKey) ||
                          searchParams.get(toKey)) && (
                          <button
                            className="mt-2 absolute right-1 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-700 text-lg"
                            type="button"
                            onClick={() => {
                              const updatedParams = new URLSearchParams(
                                searchParams
                              );
                              updatedParams.delete(fromKey);
                              updatedParams.delete(toKey);
                              replace(
                                `${pathname}?${updatedParams.toString()}`
                              );
                            }}
                            title="Clear"
                          >
                            ×
                          </button>
                        )}
                      </div>
                    );
                  }
                  return (
                    <div
                      key={index}
                      className={`flex flex- w-full relative  ${
                        filtersCollapsed
                          ? "min-w-[100px] md:w-[calc(20%-1rem)]"
                          : "min-w-[220px] md:w-[calc(25%-1rem)]"
                      }`}
                    >
                      <div className="relative w-full">
                        <div className="flex flex-wrap items-center gap-1 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md  px-1 focus-within:ring-0 focus-within:ring-blue-600 focus-within:border-blue-600 shadow-sm">
                          {searchTerms[col?.headerName]?.length > 0 && (
                            <>
                              {searchTerms[col.headerName].map((term, i) => (
                                <div
                                  key={i}
                                  className=" flex items-center bg-gray-100 dark:bg-gray-600 text-gray-800 dark:text-gray-200 px-2 rounded-full text-xs"
                                >
                                  <span className="mr-1 truncate max-w-xs">
                                    {term}
                                  </span>
                                  <button
                                    onClick={(e) => {
                                      e.preventDefault();
                                      handleRemoveTerm(col.headerName, term);
                                    }}
                                    className="  text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-gray-100"
                                    title="Remove"
                                  >
                                    ×
                                  </button>
                                </div>
                              ))}
                            </>
                          )}
                          <CustomInput
                            value={inputValues[col?.headerName] || ""}
                            onChange={(e) =>
                              setInputValues({
                                ...inputValues,
                                [col.headerName]: e.target.value,
                              })
                            }
                            onKeyDown={(e) => handleKeyDown(e, col.headerName)}
                            placeholder={
                              searchTerms[col?.headerName]?.length > 0
                                ? ""
                                : `Search ${col.headerName}...`
                            }
                            className="flex-1 min-w-[30px] bg-transparent border-none focus:outline-none"
                          />
                          {inputValues[col.headerName] && (
                            <button
                              className="ml-1 text-gray-400 hover:text-gray-700 text-lg"
                              type="button"
                              onClick={() => {
                                setInputValues({
                                  ...inputValues,
                                  [col.headerName]: "",
                                });
                                handleRemoveTerm(
                                  col.headerName,
                                  inputValues[col.headerName]
                                );
                              }}
                              title="Clear"
                            >
                              ×
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
              <div className="flex-shrink-0 ml-2 sticky right-0 z-10 bg-gray-50 flex gap-2">
                <button
                  className="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300 text-xs border border-gray-300"
                  type="button"
                  onClick={() => {
                    setInputValues({});
                    setSearchTerms({});
                    setSelectedColumns([]);
                    setColumnData("");
                    setFiltersCollapsed(true);
                    setColumnVisibility({});
                    const updatedParams = new URLSearchParams(searchParams);
                    filterColumns.forEach((col) => {
                      updatedParams.delete(col.headerName);
                      if (col.field.startsWith("customField_")) {
                        updatedParams.delete(col.field);
                        updatedParams.delete(`${col.field}_from`);
                        updatedParams.delete(`${col.field}_to`);
                      }
                      if (col.headerName === "Received Date") {
                        updatedParams.delete("recievedFDate");
                        updatedParams.delete("recievedTDate");
                      }
                      if (col.headerName === "Invoice Date") {
                        updatedParams.delete("invoiceFDate");
                        updatedParams.delete("invoiceTDate");
                      }
                      if (col.headerName === "Shipment Date") {
                        updatedParams.delete("shipmentFDate");
                        updatedParams.delete("shipmentTDate");
                      }
                    });
                    replace(`${pathname}?${updatedParams.toString()}`);
                  }}
                  title="Reset All Filters"
                >
                  <LuSearchX className="text-red-500 h-5 w-5" />
                </button>
              </div>
            </div>
          </div>
        )}
        {filter && filterColumns.length > 0 && (
          <>
            {filterColumns.map((column: any, index: number) => (
              <Input
                key={index}
                placeholder={
                  filter1PlaceHolder ? filter1PlaceHolder : filter_column
                }
                value={inputValues[column.headerName] || ""}
                onChange={(e) =>
                  setInputValues({
                    ...inputValues,
                    [column.headerName]: e.target.value,
                  })
                }
                onKeyDown={(e) => handleKeyDown(e, column.headerName)}
                className="w-[20%] dark:bg-gray-700 !outline-main-color"
              />
            ))}
          </>
        )}
        {filter2 && (
          <>
            {filterColumns.map((column: any, index: number) => (
              <Input
                key={index}
                placeholder={`${filter_column2}`}
                value={inputValues[column.headerName] || ""}
                onChange={(e) =>
                  setInputValues({
                    ...inputValues,
                    [column.headerName]: e.target.value,
                  })
                }
                onKeyDown={(e) => handleKeyDown(e, column.headerName)}
                className="w-[20%] dark:bg-gray-700 !outline-main-color"
              />
            ))}
          </>
        )}
      </div>

      {total && <div>{totalview}</div>}

      <div className={cn("", className)}>
        <div
          className="ag-theme-alpine custom-ag-grid"
          style={{ height: "100vh", width: "100%" }}
        >
          <AgGridReact
            ref={gridRef}
            rowData={processedData}
            columnDefs={columnsWithSerialNumber}
            headerHeight={35}
            domLayout="autoHeight"
            enableCellTextSelection={true}
            // overlayNoRowsTemplate={noRowsOverlayTemplate}
            // onGridReady={(params) => {
            //   params.api.sizeColumnsToFit();
            //   // Show overlays on grid ready
            //   if (isLoading) {
            //     params.api.showLoadingOverlay();
            //   } else if (!processedData || processedData.length === 0) {
            //     params.api.showNoRowsOverlay();
            //   } else {
            //     params.api.hideOverlay();
            //   }
            // }}
            // // onFirstDataRendered={(params) => {
            // //   params.api.sizeColumnsToFit();
            // // }}
            // onColumnVisible={(event) => {
            //   event.api.sizeColumnsToFit();
            // }}
            // onGridSizeChanged={(params) => {
            //   params.api.sizeColumnsToFit();
            // }}
            defaultColDef={{
              sortable: true,
              resizable: true,
              cellStyle: { borderRight: "1px solid #ddd" },
            }}
          />
        </div>
      </div>

      {data && (
        <Pagination
          currentPage={
            totalPages
              ? Number(params.get("page")) || 1
              : (gridRef.current?.api?.paginationGetCurrentPage() || 0) + 1
          }
          totalPages={
            totalPages
              ? totalPages
              : gridRef.current?.api?.paginationGetTotalPages() || 1
          }
          onPageChange={(page: number) => {
            if (gridRef.current) {
              gridRef?.current?.api?.paginationGoToPage(page - 1);
            }

            if (totalPages) {
              params.set("page", page.toString());
              replace(`${pathname}?${params.toString()}`);
            }
          }}
        />
      )}
    </div>
  );
};

export default DataGridTableTrackSheet;
