import { useContext, useState, useMemo } from "react";
import { TrackSheetContext } from "./TrackSheetContext";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import Column from "./column";
import DataGridTableTrackSheet from "@/app/_component/DataGridTableTrackSheet";
import { Input } from "@/components/ui/input";

const ViewTrackSheet = ({
  permissions,
  totalPages,
  client,
  customFieldsMap,
  selectedClients,
  trackSheetData,
  pageSize,
}: any) => {
  const {
    recievedFDate,
    recievedTDate,
    setRecievedFDate,
    setRecievedTDate,
    invoiceFDate,
    setInvoiceFDate,
    invoiceTDate,
    setInvoiceTDate,
    shipmentFDate,
    setShipmentFDate,
    shipmentTDate,
    setShipmentTDate,
    deleteData,
    setDeletedData,
  } = useContext(TrackSheetContext);
  const searchParams = useSearchParams();
  const params = new URLSearchParams(searchParams);
  const pathname = usePathname();
  const { replace } = useRouter();



  const filteredTrackSheetData = useMemo(() => {
    if (!trackSheetData?.data) {
      return [];
    }

    // Data is already processed by mapCustomFields in ClientSelectPage, so just return it sorted
    return trackSheetData.data.sort((a: any, b: any) => b.id - a.id);
  }, [trackSheetData?.data]);

  const handleDateChange = async (
    field:
      | "recievedFDate"
      | "recievedTDate"
      | "invoiceFDate"
      | "invoiceTDate"
      | "shipmentFDate"
      | "shipmentTDate",
    value: string
  ) => {
    if (field === "recievedFDate") {
      setRecievedFDate(value);
      params.set("recievedFDate", value?.toString());
      params.set("page", "1");
      replace(`${pathname}?${params.toString()}`);
    } else if (field === "recievedTDate") {
      setRecievedTDate(value);
      params.set("recievedTDate", value?.toString());
      params.set("page", "1");
      replace(`${pathname}?${params.toString()}`);
    } else if (field === "invoiceFDate") {
      setInvoiceFDate(value);
      params.set("invoiceFDate", value?.toString());
      params.set("page", "1");
      replace(`${pathname}?${params.toString()}`);
    } else if (field === "invoiceTDate") {
      setInvoiceTDate(value);
      params.set("invoiceTDate", value?.toString());
      params.set("page", "1");
      replace(`${pathname}?${params.toString()}`);
    } else if (field === "shipmentFDate") {
      setShipmentFDate(value);
      params.set("shipmentFDate", value?.toString());
      params.set("page", "1");
      replace(`${pathname}?${params.toString()}`);
    } else if (field === "shipmentTDate") {
      setShipmentTDate(value);
      params.set("shipmentTDate", value?.toString());
      params.set("page", "1");
      replace(`${pathname}?${params.toString()}`);
    }
  };

  const filterview = (
    <div className="flex gap-2 mb-4">
      <Input
        type="date"
        value={recievedFDate}
        onChange={(e) => handleDateChange("recievedFDate", e.target.value)}
        placeholder="From Date"
        className="w-full dark:bg-gray-700 !outline-main-color"
      />
      <Input
        type="date"
        value={recievedTDate}
        onChange={(e) => handleDateChange("recievedTDate", e.target.value)}
        placeholder="To Date"
        className="w-full dark:bg-gray-700 !outline-main-color"
      />
    </div>
  );

  const invoiceDateFilterView = (
    <div className="flex gap-2 mb-4">
      <Input
        type="date"
        value={invoiceFDate}
        onChange={(e) => handleDateChange("invoiceFDate", e.target.value)}
        placeholder="From Date"
        className="w-full dark:bg-gray-700 !outline-main-color"
      />
      <Input
        type="date"
        value={invoiceTDate}
        onChange={(e) => handleDateChange("invoiceTDate", e.target.value)}
        placeholder="To Date"
        className="w-full dark:bg-gray-700 !outline-main-color"
      />
    </div>
  );

  const shipmentDateFilterView = (
    <div className="flex gap-2 mb-4">
      <Input
        type="date"
        value={shipmentFDate}
        onChange={(e) => handleDateChange("shipmentFDate", e.target.value)}
        placeholder="From Date"
        className="w-full dark:bg-gray-700 !outline-main-color"
      />
      <Input
        type="date"
        value={shipmentTDate}
        onChange={(e) => handleDateChange("shipmentTDate", e.target.value)}
        placeholder="To Date"
        className="w-full dark:bg-gray-700 !outline-main-color"
      />
    </div>
  );

  return (
    <div className="w-full">
      {selectedClients.length === 0 ? (
        <div className="flex items-center justify-center h-64">
          <p className="text-gray-500 text-lg">Please Select a Client</p>
        </div>
      ) : (
        <DataGridTableTrackSheet
          data={filteredTrackSheetData}
          columns={Column(permissions, setDeletedData, deleteData, {
            data: filteredTrackSheetData,
            customFieldsMap: customFieldsMap,
          })}
          showColDropDowns
          showPageEntries
          filter3={true}
          filter3view={filterview}
          filter4={true}
          filter4view={invoiceDateFilterView}
          filter5={true}
          filter5view={shipmentDateFilterView}
          className="w-full"
          total={true}
          pageSize={pageSize}
          totalPages={totalPages}
        />
      )}
    </div>
  );
};

export default ViewTrackSheet;
