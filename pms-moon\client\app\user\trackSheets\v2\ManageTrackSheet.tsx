"use client";
import React, { useState, useCallback } from "react";
import CreateTrackSheet from "./createTrackSheet";
import { Button } from "@/components/ui/button";
import { useSearchParams } from "next/navigation";
import { TrackSheetContext } from "./TrackSheetContext";
import ClientSelectPage from "./ClientSelectPage";
import Sidebar from "@/components/sidebar/Sidebar";
import { SidebarProvider } from "@/components/ui/sidebar";
import BreadCrumbs from "@/app/_component/BreadCrumbs";
import { Building2 } from "lucide-react";
import { Form } from "@/components/ui/form";
import { useForm } from "react-hook-form";
import SearchSelect from "@/app/_component/SearchSelect";

const ManageWorkSheet = ({
  permissions,
  client,
  carrier,
  associate,
  userData,
  actions,
}: any) => {
  const [filterdata, setFilterData] = useState([]);
  const [deleteData, setDeletedData] = useState(false);
  const [recievedFDate, setRecievedFDate] = useState("");
  const [recievedTDate, setRecievedTDate] = useState("");
  const [invoiceFDate, setInvoiceFDate] = useState("");
  const [invoiceTDate, setInvoiceTDate] = useState("");
  const [shipmentFDate, setShipmentFDate] = useState("");
  const [shipmentTDate, setShipmentTDate] = useState("");
  const [activeView, setActiveView] = useState("view"); // Default to view mode
  
  // Add these states for the selection form
  const [showFullForm, setShowFullForm] = useState(false);
  const [initialAssociateId, setInitialAssociateId] = useState("");
  const [initialClientId, setInitialClientId] = useState("");

  const selectionForm = useForm({
    defaultValues: {
      associateId: "",
      clientId: "",
    },
  });

  const associateOptions = associate?.map((a: any) => ({
    value: a.id?.toString(),
    label: a.name,
    name: a.name,
  }));

  const getFilteredClientOptions = () => {
    if (!initialAssociateId) {
      return client?.map((c: any) => ({
        value: c.id?.toString(),
        label: c.client_name,
        name: c.client_name,
      })) || [];
    }

    const filteredClients = client?.filter((c: any) =>
      c.associateId?.toString() === initialAssociateId
    ) || [];

    return filteredClients.map((c: any) => ({
      value: c.id?.toString(),
      label: c.client_name,
      name: c.client_name,
    }));
  };

  const clientOptions = getFilteredClientOptions();

  const validateClientForAssociate = useCallback((associateId: string, currentClientId: string) => {
    if (associateId && currentClientId) {
      const currentClient = client?.find((c: any) => c.id?.toString() === currentClientId);
      if (currentClient && currentClient.associateId?.toString() !== associateId) {
        selectionForm.setValue("clientId", "");
        setInitialClientId("");
        return false;
      }
    }
    return true;
  }, [client, selectionForm]);

  const clearEntrySpecificClients = useCallback(() => {
    // This is a placeholder - the actual implementation would be in CreateTrackSheet
    // We'll pass this as a prop to CreateTrackSheet
  }, []);

  const handleInitialSelection = useCallback((associateId: string, clientId: string) => {
    // This is a placeholder - the actual implementation would be in CreateTrackSheet
    // We'll pass these values as props to CreateTrackSheet
    setShowFullForm(true);
  }, []);

  return (
    <TrackSheetContext.Provider
      value={{
        filterdata,
        setFilterData,
        deleteData,
        setDeletedData,
        recievedFDate,
        recievedTDate,
        setRecievedFDate,
        setRecievedTDate,
        invoiceFDate,
        setInvoiceFDate,
        invoiceTDate,
        setInvoiceTDate,
        shipmentFDate,
        setShipmentFDate,
        shipmentTDate,
        setShipmentTDate,
      }}
    >
      <SidebarProvider>
        <div className="flex w-full min-h-screen">
          <Sidebar permissions={permissions} profile={userData} />
          <main className="flex-1 w-full pl-3 overflow-auto">
            <div className="mb-6">
              <BreadCrumbs
                breadcrumblist={[
                  { link: "/user/trackSheets", name: "TrackSheet" },
                ]}
              />
            </div>

            {/* Toggle Buttons and Selection Form in one row */}
            <div className="flex items-center gap-4 pl-3 mb-6">
              <Button
                variant="default"
                onClick={() => setActiveView("view")}
                className={`w-40 shadow-md rounded-xl text-base transition-all duration-200 ${
                  activeView === "view"
                    ? "bg-neutral-800 hover:bg-neutral-900 text-white"
                    : "bg-white text-neutral-800 border border-neutral-300 hover:bg-neutral-100"
                }`}
              >
                View TrackSheet
              </Button>
              <Button
                variant="default"
                onClick={() => setActiveView("create")}
                className={`w-40 shadow-md rounded-xl text-base transition-all duration-200 ${
                  activeView === "create"
                    ? "bg-neutral-800 hover:bg-neutral-900 text-white"
                    : "bg-white text-neutral-800 border border-neutral-300 hover:bg-neutral-100"
                }`}
              >
                Create TrackSheet
              </Button>
              
              {/* Inline Selection Form */}
              {activeView === "create" && (
                <div className="flex-1 bg-white rounded-lg shadow-sm border border-gray-200 p-3">
                  <Form {...selectionForm}>
                    <div className="flex items-center gap-4">
                      <div className="flex-1">
                        <SearchSelect
                          form={selectionForm}
                          name="associateId"
                          label="Select Associate"
                          placeholder="Search Associate..."
                          isRequired
                          options={associateOptions || []}
                          onValueChange={(value) => {
                            setInitialAssociateId(value);

                            if (value && initialClientId) {
                              validateClientForAssociate(value, initialClientId);
                            } else {
                              setInitialClientId(""); 
                              selectionForm.setValue("clientId", ""); 
                            }
                            setShowFullForm(false); 
                          }}
                        />
                      </div>

                      <div className="flex-1">
                        <SearchSelect
                          form={selectionForm}
                          name="clientId"
                          label="Select Client"
                          placeholder="Search Client..."
                          isRequired
                          disabled={!initialAssociateId}
                          options={clientOptions || []}
                          onValueChange={(value) => {
                            setInitialClientId(value);

                            if (showFullForm) {
                              clearEntrySpecificClients();
                            }

                            if (value && initialAssociateId) {
                              setTimeout(() => {
                                handleInitialSelection(initialAssociateId, value);
                              }, 100);
                            } else {
                              setShowFullForm(false);
                            }
                          }}
                        />
                      </div>
                    </div>
                  </Form>
                </div>
              )}
            </div>

            <div className="w-full animate-in fade-in duration-500 rounded-2xl shadow-sm dark:bg-gray-800 p-1">
              {activeView === "create" ? (
                <CreateTrackSheet 
                  client={client} 
                  carrier={carrier} 
                  associate={associate} 
                  userData={userData}
                  initialAssociateId={initialAssociateId}
                  initialClientId={initialClientId}
                  showFullForm={showFullForm}
                />
              ) : (
                <ClientSelectPage
                  permissions={actions}
                  client={client}
                />
              )}
            </div>
          </main>
        </div>
      </SidebarProvider>
    </TrackSheetContext.Provider>
  );
};

export default ManageWorkSheet;
