"use client";
import React, { useState } from "react";
import CreateTrackSheet from "./createTrackSheet";
import { But<PERSON> } from "@/components/ui/button";
import { useSearchParams } from "next/navigation";
import { TrackSheetContext } from "./TrackSheetContext";
import ClientSelectPage from "./ClientSelectPage";
import Sidebar from "@/components/sidebar/Sidebar";
import { SidebarProvider } from "@/components/ui/sidebar";
import BreadCrumbs from "@/app/_component/BreadCrumbs";
import { Form } from "@/components/ui/form";
import SearchSelect from "@/app/_component/SearchSelect";
import { useForm } from "react-hook-form";

const ManageWorkSheet = ({
  permissions,
  client,
  carrier,
  associate,
  userData,
  actions,
}: any) => {
  const [filterdata, setFilterData] = useState([]);
  const [deleteData, setDeletedData] = useState(false);
  const [recievedFDate, setRecievedFDate] = useState("");
  const [recievedTDate, setRecievedTDate] = useState("");
  const [invoiceFDate, setInvoiceFDate] = useState("");
  const [invoiceTDate, setInvoiceTDate] = useState("");
  const [shipmentFDate, setShipmentFDate] = useState("");
  const [shipmentTDate, setShipmentTDate] = useState("");
  const [activeView, setActiveView] = useState("view"); // Default to view mode
  const [selectedAssociateId, setSelectedAssociateId] = useState("");
  const [selectedClientId, setSelectedClientId] = useState("");

  const selectionForm = useForm({
    defaultValues: {
      associateId: "",
      clientId: "",
    },
  });

  const associateOptions = associate?.map((a: any) => ({
    value: a.id?.toString(),
    label: a.name,
    name: a.name,
  }));

  const getFilteredClientOptions = () => {
    if (!selectedAssociateId) {
      return client?.map((c: any) => ({
        value: c.id?.toString(),
        label: c.client_name,
        name: c.client_name,
      })) || [];
    }

    const filteredClients = client?.filter((c: any) =>
      c.associateId?.toString() === selectedAssociateId
    ) || [];

    return filteredClients.map((c: any) => ({
      value: c.id?.toString(),
      label: c.client_name,
      name: c.client_name,
    }));
  };

  const clientOptions = getFilteredClientOptions();

  return (
    <TrackSheetContext.Provider
      value={{
        filterdata,
        setFilterData,
        deleteData,
        setDeletedData,
        recievedFDate,
        recievedTDate,
        setRecievedFDate,
        setRecievedTDate,
        invoiceFDate,
        setInvoiceFDate,
        invoiceTDate,
        setInvoiceTDate,
        shipmentFDate,
        setShipmentFDate,
        shipmentTDate,
        setShipmentTDate,
      }}
    >
      <SidebarProvider>
        <div className="flex w-full min-h-screen">
          <Sidebar permissions={permissions} profile={userData} />
          <main className="flex-1 w-full pl-3 overflow-auto">
            <div className="mb-6">
              <BreadCrumbs
                breadcrumblist={[
                  { link: "/user/trackSheets", name: "TrackSheet" },
                ]}
              />
            </div>

            {/* Toggle Buttons with inline Associate and Client Selection */}
            <div className="flex items-center gap-4 pl-3 mb-6">
              <Button
                variant="default"
                onClick={() => setActiveView("view")}
                className={`w-40 shadow-md rounded-xl text-base transition-all duration-200 ${
                  activeView === "view"
                    ? "bg-neutral-800 hover:bg-neutral-900 text-white"
                    : "bg-white text-neutral-800 border border-neutral-300 hover:bg-neutral-100"
                }`}
              >
                View TrackSheet
              </Button>
              <Button
                variant="default"
                onClick={() => setActiveView("create")}
                className={`w-40 shadow-md rounded-xl text-base transition-all duration-200 ${
                  activeView === "create"
                    ? "bg-neutral-800 hover:bg-neutral-900 text-white"
                    : "bg-white text-neutral-800 border border-neutral-300 hover:bg-neutral-100"
                }`}
              >
                Create TrackSheet
              </Button>

              {/* Associate and Client Selection - Inline after Create TrackSheet button */}
              {activeView === "create" && (
                <Form {...selectionForm}>
                  <div className="flex items-center gap-4">
                    <div className="w-48">
                      <SearchSelect
                        form={selectionForm}
                        name="associateId"
                        label="Select Associate"
                        placeholder="Search Associate..."
                        isRequired
                        options={associateOptions || []}
                        onValueChange={(value) => {
                          setSelectedAssociateId(value);
                          setSelectedClientId("");
                          selectionForm.setValue("clientId", "");
                        }}
                      />
                    </div>

                    <div className="w-48">
                      <SearchSelect
                        form={selectionForm}
                        name="clientId"
                        label="Select Client"
                        placeholder="Search Client..."
                        isRequired
                        disabled={!selectedAssociateId}
                        options={clientOptions || []}
                        onValueChange={(value) => {
                          setSelectedClientId(value);
                        }}
                      />
                    </div>
                  </div>
                </Form>
              )}
            </div>

            <div className="w-full animate-in fade-in duration-500 rounded-2xl shadow-sm  dark:bg-gray-800 p-1">
              {activeView === "create" ? (
                <CreateTrackSheet client={client} carrier={carrier} associate={associate} userData={userData} />
              ) : (
                <ClientSelectPage
                  permissions={actions}
                  client={client}
                />
              )}
            </div>
          </main>
        </div>
      </SidebarProvider>
    </TrackSheetContext.Provider>
  );
};

export default ManageWorkSheet;