"use client";
import React, { useState } from "react";
import CreateTrackSheet from "./createTrackSheet";
import { But<PERSON> } from "@/components/ui/button";
import { useSearchParams } from "next/navigation";
import { TrackSheetContext } from "./TrackSheetContext";
import ClientSelectPage from "./ClientSelectPage";
import Sidebar from "@/components/sidebar/Sidebar";
import { SidebarProvider } from "@/components/ui/sidebar";
import BreadCrumbs from "@/app/_component/BreadCrumbs";

const ManageWorkSheet = ({
  permissions,
  client,
  carrier,
  associate,
  userData,
  actions,
}: any) => {
  const [filterdata, setFilterData] = useState([]);
  const [deleteData, setDeletedData] = useState(false);
  const [recievedFDate, setRecievedFDate] = useState("");
  const [recievedTDate, setRecievedTDate] = useState("");
  const [invoiceFDate, setInvoiceFDate] = useState("");
  const [invoiceTDate, setInvoiceTDate] = useState("");
  const [shipmentFDate, setShipmentFDate] = useState("");
  const [shipmentTDate, setShipmentTDate] = useState("");
  const [activeView, setActiveView] = useState("view"); // Default to view mode

  return (
    <TrackSheetContext.Provider
      value={{
        filterdata,
        setFilterData,
        deleteData,
        setDeletedData,
        recievedFDate,
        recievedTDate,
        setRecievedFDate,
        setRecievedTDate,
        invoiceFDate,
        setInvoiceFDate,
        invoiceTDate,
        setInvoiceTDate,
        shipmentFDate,
        setShipmentFDate,
        shipmentTDate,
        setShipmentTDate,
      }}
    >
      <SidebarProvider>
        <div className="flex w-full min-h-screen">
          <Sidebar permissions={permissions} profile={userData} />
          <main className="flex-1 w-full pl-3 overflow-auto">
            <div className="mb-6">
              <BreadCrumbs
                breadcrumblist={[
                  { link: "/user/trackSheets", name: "TrackSheet" },
                ]}
              />
            </div>

            {/* Toggle Buttons */}
            <div className="flex gap-4 pl-3 mb-6">
              <Button
                variant="default"
                onClick={() => setActiveView("view")}
                className={`w-40 shadow-md rounded-xl text-base transition-all duration-200 ${
                  activeView === "view"
                    ? "bg-neutral-800 hover:bg-neutral-900 text-white"
                    : "bg-white text-neutral-800 border border-neutral-300 hover:bg-neutral-100"
                }`}
              >
                View TrackSheet
              </Button>
              <Button
                variant="default"
                onClick={() => setActiveView("create")}
                className={`w-40 shadow-md rounded-xl text-base transition-all duration-200 ${
                  activeView === "create"
                    ? "bg-neutral-800 hover:bg-neutral-900 text-white"
                    : "bg-white text-neutral-800 border border-neutral-300 hover:bg-neutral-100"
                }`}
              >
                Create TrackSheet
              </Button>
            </div>

            <div className="w-full animate-in fade-in duration-500 rounded-2xl shadow-sm  dark:bg-gray-800 p-1">
              {activeView === "create" ? (
                <CreateTrackSheet client={client} carrier={carrier} associate={associate} userData={userData} />
              ) : (
                <ClientSelectPage
                  permissions={actions}
                  client={client}
                />
              )}
            </div>
          </main>
        </div>
      </SidebarProvider>
    </TrackSheetContext.Provider>
  );
};

export default ManageWorkSheet;