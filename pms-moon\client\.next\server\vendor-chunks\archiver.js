/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/archiver";
exports.ids = ["vendor-chunks/archiver"];
exports.modules = {

/***/ "(ssr)/./node_modules/archiver/index.js":
/*!****************************************!*\
  !*** ./node_modules/archiver/index.js ***!
  \****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * Archiver Vending\n *\n * @ignore\n * @license [MIT]{@link https://github.com/archiverjs/node-archiver/blob/master/LICENSE}\n * @copyright (c) 2012-2014 Chris Talkington, contributors.\n */\nvar Archiver = __webpack_require__(/*! ./lib/core */ \"(ssr)/./node_modules/archiver/lib/core.js\");\n\nvar formats = {};\n\n/**\n * Dispenses a new Archiver instance.\n *\n * @constructor\n * @param  {String} format The archive format to use.\n * @param  {Object} options See [Archiver]{@link Archiver}\n * @return {Archiver}\n */\nvar vending = function(format, options) {\n  return vending.create(format, options);\n};\n\n/**\n * Creates a new Archiver instance.\n *\n * @param  {String} format The archive format to use.\n * @param  {Object} options See [Archiver]{@link Archiver}\n * @return {Archiver}\n */\nvending.create = function(format, options) {\n  if (formats[format]) {\n    var instance = new Archiver(format, options);\n    instance.setFormat(format);\n    instance.setModule(new formats[format](options));\n\n    return instance;\n  } else {\n    throw new Error('create(' + format + '): format not registered');\n  }\n};\n\n/**\n * Registers a format for use with archiver.\n *\n * @param  {String} format The name of the format.\n * @param  {Function} module The function for archiver to interact with.\n * @return void\n */\nvending.registerFormat = function(format, module) {\n  if (formats[format]) {\n    throw new Error('register(' + format + '): format already registered');\n  }\n\n  if (typeof module !== 'function') {\n    throw new Error('register(' + format + '): format module invalid');\n  }\n\n  if (typeof module.prototype.append !== 'function' || typeof module.prototype.finalize !== 'function') {\n    throw new Error('register(' + format + '): format module missing methods');\n  }\n\n  formats[format] = module;\n};\n\n/**\n * Check if the format is already registered.\n * \n * @param {String} format the name of the format.\n * @return boolean\n */\nvending.isRegisteredFormat = function (format) {\n  if (formats[format]) {\n    return true;\n  }\n  \n  return false;\n};\n\nvending.registerFormat('zip', __webpack_require__(/*! ./lib/plugins/zip */ \"(ssr)/./node_modules/archiver/lib/plugins/zip.js\"));\nvending.registerFormat('tar', __webpack_require__(/*! ./lib/plugins/tar */ \"(ssr)/./node_modules/archiver/lib/plugins/tar.js\"));\nvending.registerFormat('json', __webpack_require__(/*! ./lib/plugins/json */ \"(ssr)/./node_modules/archiver/lib/plugins/json.js\"));\n\nmodule.exports = vending;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/archiver/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/archiver/lib/core.js":
/*!*******************************************!*\
  !*** ./node_modules/archiver/lib/core.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * Archiver Core\n *\n * @ignore\n * @license [MIT]{@link https://github.com/archiverjs/node-archiver/blob/master/LICENSE}\n * @copyright (c) 2012-2014 Chris Talkington, contributors.\n */\nvar fs = __webpack_require__(/*! fs */ \"fs\");\nvar glob = __webpack_require__(/*! readdir-glob */ \"(ssr)/./node_modules/readdir-glob/index.js\");\nvar async = __webpack_require__(/*! async */ \"(ssr)/./node_modules/async/dist/async.mjs\");\nvar path = __webpack_require__(/*! path */ \"path\");\nvar util = __webpack_require__(/*! archiver-utils */ \"(ssr)/./node_modules/archiver-utils/index.js\");\n\nvar inherits = (__webpack_require__(/*! util */ \"util\").inherits);\nvar ArchiverError = __webpack_require__(/*! ./error */ \"(ssr)/./node_modules/archiver/lib/error.js\");\nvar Transform = (__webpack_require__(/*! readable-stream */ \"(ssr)/./node_modules/readable-stream/readable.js\").Transform);\n\nvar win32 = process.platform === 'win32';\n\n/**\n * @constructor\n * @param {String} format The archive format to use.\n * @param {(CoreOptions|TransformOptions)} options See also {@link ZipOptions} and {@link TarOptions}.\n */\nvar Archiver = function(format, options) {\n  if (!(this instanceof Archiver)) {\n    return new Archiver(format, options);\n  }\n\n  if (typeof format !== 'string') {\n    options = format;\n    format = 'zip';\n  }\n\n  options = this.options = util.defaults(options, {\n    highWaterMark: 1024 * 1024,\n    statConcurrency: 4\n  });\n\n  Transform.call(this, options);\n\n  this._format = false;\n  this._module = false;\n  this._pending = 0;\n  this._pointer = 0;\n\n  this._entriesCount = 0;\n  this._entriesProcessedCount = 0;\n  this._fsEntriesTotalBytes = 0;\n  this._fsEntriesProcessedBytes = 0;\n\n  this._queue = async.queue(this._onQueueTask.bind(this), 1);\n  this._queue.drain(this._onQueueDrain.bind(this));\n\n  this._statQueue = async.queue(this._onStatQueueTask.bind(this), options.statConcurrency);\n  this._statQueue.drain(this._onQueueDrain.bind(this));\n\n  this._state = {\n    aborted: false,\n    finalize: false,\n    finalizing: false,\n    finalized: false,\n    modulePiped: false\n  };\n\n  this._streams = [];\n};\n\ninherits(Archiver, Transform);\n\n/**\n * Internal logic for `abort`.\n *\n * @private\n * @return void\n */\nArchiver.prototype._abort = function() {\n  this._state.aborted = true;\n  this._queue.kill();\n  this._statQueue.kill();\n\n  if (this._queue.idle()) {\n    this._shutdown();\n  }\n};\n\n/**\n * Internal helper for appending files.\n *\n * @private\n * @param  {String} filepath The source filepath.\n * @param  {EntryData} data The entry data.\n * @return void\n */\nArchiver.prototype._append = function(filepath, data) {\n  data = data || {};\n\n  var task = {\n    source: null,\n    filepath: filepath\n  };\n\n  if (!data.name) {\n    data.name = filepath;\n  }\n\n  data.sourcePath = filepath;\n  task.data = data;\n  this._entriesCount++;\n\n  if (data.stats && data.stats instanceof fs.Stats) {\n    task = this._updateQueueTaskWithStats(task, data.stats);\n    if (task) {\n      if (data.stats.size) {\n        this._fsEntriesTotalBytes += data.stats.size;\n      }\n\n      this._queue.push(task);\n    }\n  } else {\n    this._statQueue.push(task);\n  }\n};\n\n/**\n * Internal logic for `finalize`.\n *\n * @private\n * @return void\n */\nArchiver.prototype._finalize = function() {\n  if (this._state.finalizing || this._state.finalized || this._state.aborted) {\n    return;\n  }\n\n  this._state.finalizing = true;\n\n  this._moduleFinalize();\n\n  this._state.finalizing = false;\n  this._state.finalized = true;\n};\n\n/**\n * Checks the various state variables to determine if we can `finalize`.\n *\n * @private\n * @return {Boolean}\n */\nArchiver.prototype._maybeFinalize = function() {\n  if (this._state.finalizing || this._state.finalized || this._state.aborted) {\n    return false;\n  }\n\n  if (this._state.finalize && this._pending === 0 && this._queue.idle() && this._statQueue.idle()) {\n    this._finalize();\n    return true;\n  }\n\n  return false;\n};\n\n/**\n * Appends an entry to the module.\n *\n * @private\n * @fires  Archiver#entry\n * @param  {(Buffer|Stream)} source\n * @param  {EntryData} data\n * @param  {Function} callback\n * @return void\n */\nArchiver.prototype._moduleAppend = function(source, data, callback) {\n  if (this._state.aborted) {\n    callback();\n    return;\n  }\n\n  this._module.append(source, data, function(err) {\n    this._task = null;\n\n    if (this._state.aborted) {\n      this._shutdown();\n      return;\n    }\n\n    if (err) {\n      this.emit('error', err);\n      setImmediate(callback);\n      return;\n    }\n\n    /**\n     * Fires when the entry's input has been processed and appended to the archive.\n     *\n     * @event Archiver#entry\n     * @type {EntryData}\n     */\n    this.emit('entry', data);\n    this._entriesProcessedCount++;\n\n    if (data.stats && data.stats.size) {\n      this._fsEntriesProcessedBytes += data.stats.size;\n    }\n\n    /**\n     * @event Archiver#progress\n     * @type {ProgressData}\n     */\n    this.emit('progress', {\n      entries: {\n        total: this._entriesCount,\n        processed: this._entriesProcessedCount\n      },\n      fs: {\n        totalBytes: this._fsEntriesTotalBytes,\n        processedBytes: this._fsEntriesProcessedBytes\n      }\n    });\n\n    setImmediate(callback);\n  }.bind(this));\n};\n\n/**\n * Finalizes the module.\n *\n * @private\n * @return void\n */\nArchiver.prototype._moduleFinalize = function() {\n  if (typeof this._module.finalize === 'function') {\n    this._module.finalize();\n  } else if (typeof this._module.end === 'function') {\n    this._module.end();\n  } else {\n    this.emit('error', new ArchiverError('NOENDMETHOD'));\n  }\n};\n\n/**\n * Pipes the module to our internal stream with error bubbling.\n *\n * @private\n * @return void\n */\nArchiver.prototype._modulePipe = function() {\n  this._module.on('error', this._onModuleError.bind(this));\n  this._module.pipe(this);\n  this._state.modulePiped = true;\n};\n\n/**\n * Determines if the current module supports a defined feature.\n *\n * @private\n * @param  {String} key\n * @return {Boolean}\n */\nArchiver.prototype._moduleSupports = function(key) {\n  if (!this._module.supports || !this._module.supports[key]) {\n    return false;\n  }\n\n  return this._module.supports[key];\n};\n\n/**\n * Unpipes the module from our internal stream.\n *\n * @private\n * @return void\n */\nArchiver.prototype._moduleUnpipe = function() {\n  this._module.unpipe(this);\n  this._state.modulePiped = false;\n};\n\n/**\n * Normalizes entry data with fallbacks for key properties.\n *\n * @private\n * @param  {Object} data\n * @param  {fs.Stats} stats\n * @return {Object}\n */\nArchiver.prototype._normalizeEntryData = function(data, stats) {\n  data = util.defaults(data, {\n    type: 'file',\n    name: null,\n    date: null,\n    mode: null,\n    prefix: null,\n    sourcePath: null,\n    stats: false\n  });\n\n  if (stats && data.stats === false) {\n    data.stats = stats;\n  }\n\n  var isDir = data.type === 'directory';\n\n  if (data.name) {\n    if (typeof data.prefix === 'string' && '' !== data.prefix) {\n      data.name = data.prefix + '/' + data.name;\n      data.prefix = null;\n    }\n\n    data.name = util.sanitizePath(data.name);\n\n    if (data.type !== 'symlink' && data.name.slice(-1) === '/') {\n      isDir = true;\n      data.type = 'directory';\n    } else if (isDir) {\n      data.name += '/';\n    }\n  }\n\n  // 511 === 0777; 493 === 0755; 438 === 0666; 420 === 0644\n  if (typeof data.mode === 'number') {\n    if (win32) {\n      data.mode &= 511;\n    } else {\n      data.mode &= 4095\n    }\n  } else if (data.stats && data.mode === null) {\n    if (win32) {\n      data.mode = data.stats.mode & 511;\n    } else {\n      data.mode = data.stats.mode & 4095;\n    }\n\n    // stat isn't reliable on windows; force 0755 for dir\n    if (win32 && isDir) {\n      data.mode = 493;\n    }\n  } else if (data.mode === null) {\n    data.mode = isDir ? 493 : 420;\n  }\n\n  if (data.stats && data.date === null) {\n    data.date = data.stats.mtime;\n  } else {\n    data.date = util.dateify(data.date);\n  }\n\n  return data;\n};\n\n/**\n * Error listener that re-emits error on to our internal stream.\n *\n * @private\n * @param  {Error} err\n * @return void\n */\nArchiver.prototype._onModuleError = function(err) {\n  /**\n   * @event Archiver#error\n   * @type {ErrorData}\n   */\n  this.emit('error', err);\n};\n\n/**\n * Checks the various state variables after queue has drained to determine if\n * we need to `finalize`.\n *\n * @private\n * @return void\n */\nArchiver.prototype._onQueueDrain = function() {\n  if (this._state.finalizing || this._state.finalized || this._state.aborted) {\n    return;\n  }\n\n  if (this._state.finalize && this._pending === 0 && this._queue.idle() && this._statQueue.idle()) {\n    this._finalize();\n  }\n};\n\n/**\n * Appends each queue task to the module.\n *\n * @private\n * @param  {Object} task\n * @param  {Function} callback\n * @return void\n */\nArchiver.prototype._onQueueTask = function(task, callback) {\n  var fullCallback = () => {\n    if(task.data.callback) {\n      task.data.callback();\n    }\n    callback();\n  }\n\n  if (this._state.finalizing || this._state.finalized || this._state.aborted) {\n    fullCallback();\n    return;\n  }\n\n  this._task = task;\n  this._moduleAppend(task.source, task.data, fullCallback);\n};\n\n/**\n * Performs a file stat and reinjects the task back into the queue.\n *\n * @private\n * @param  {Object} task\n * @param  {Function} callback\n * @return void\n */\nArchiver.prototype._onStatQueueTask = function(task, callback) {\n  if (this._state.finalizing || this._state.finalized || this._state.aborted) {\n    callback();\n    return;\n  }\n\n  fs.lstat(task.filepath, function(err, stats) {\n    if (this._state.aborted) {\n      setImmediate(callback);\n      return;\n    }\n\n    if (err) {\n      this._entriesCount--;\n\n      /**\n       * @event Archiver#warning\n       * @type {ErrorData}\n       */\n      this.emit('warning', err);\n      setImmediate(callback);\n      return;\n    }\n\n    task = this._updateQueueTaskWithStats(task, stats);\n\n    if (task) {\n      if (stats.size) {\n        this._fsEntriesTotalBytes += stats.size;\n      }\n\n      this._queue.push(task);\n    }\n\n    setImmediate(callback);\n  }.bind(this));\n};\n\n/**\n * Unpipes the module and ends our internal stream.\n *\n * @private\n * @return void\n */\nArchiver.prototype._shutdown = function() {\n  this._moduleUnpipe();\n  this.end();\n};\n\n/**\n * Tracks the bytes emitted by our internal stream.\n *\n * @private\n * @param  {Buffer} chunk\n * @param  {String} encoding\n * @param  {Function} callback\n * @return void\n */\nArchiver.prototype._transform = function(chunk, encoding, callback) {\n  if (chunk) {\n    this._pointer += chunk.length;\n  }\n\n  callback(null, chunk);\n};\n\n/**\n * Updates and normalizes a queue task using stats data.\n *\n * @private\n * @param  {Object} task\n * @param  {fs.Stats} stats\n * @return {Object}\n */\nArchiver.prototype._updateQueueTaskWithStats = function(task, stats) {\n  if (stats.isFile()) {\n    task.data.type = 'file';\n    task.data.sourceType = 'stream';\n    task.source = util.lazyReadStream(task.filepath);\n  } else if (stats.isDirectory() && this._moduleSupports('directory')) {\n    task.data.name = util.trailingSlashIt(task.data.name);\n    task.data.type = 'directory';\n    task.data.sourcePath = util.trailingSlashIt(task.filepath);\n    task.data.sourceType = 'buffer';\n    task.source = Buffer.concat([]);\n  } else if (stats.isSymbolicLink() && this._moduleSupports('symlink')) {\n    var linkPath = fs.readlinkSync(task.filepath);\n    var dirName = path.dirname(task.filepath);\n    task.data.type = 'symlink';\n    task.data.linkname = path.relative(dirName, path.resolve(dirName, linkPath));\n    task.data.sourceType = 'buffer';\n    task.source = Buffer.concat([]);\n  } else {\n    if (stats.isDirectory()) {\n      this.emit('warning', new ArchiverError('DIRECTORYNOTSUPPORTED', task.data));\n    } else if (stats.isSymbolicLink()) {\n      this.emit('warning', new ArchiverError('SYMLINKNOTSUPPORTED', task.data));\n    } else {\n      this.emit('warning', new ArchiverError('ENTRYNOTSUPPORTED', task.data));\n    }\n\n    return null;\n  }\n\n  task.data = this._normalizeEntryData(task.data, stats);\n\n  return task;\n};\n\n/**\n * Aborts the archiving process, taking a best-effort approach, by:\n *\n * - removing any pending queue tasks\n * - allowing any active queue workers to finish\n * - detaching internal module pipes\n * - ending both sides of the Transform stream\n *\n * It will NOT drain any remaining sources.\n *\n * @return {this}\n */\nArchiver.prototype.abort = function() {\n  if (this._state.aborted || this._state.finalized) {\n    return this;\n  }\n\n  this._abort();\n\n  return this;\n};\n\n/**\n * Appends an input source (text string, buffer, or stream) to the instance.\n *\n * When the instance has received, processed, and emitted the input, the `entry`\n * event is fired.\n *\n * @fires  Archiver#entry\n * @param  {(Buffer|Stream|String)} source The input source.\n * @param  {EntryData} data See also {@link ZipEntryData} and {@link TarEntryData}.\n * @return {this}\n */\nArchiver.prototype.append = function(source, data) {\n  if (this._state.finalize || this._state.aborted) {\n    this.emit('error', new ArchiverError('QUEUECLOSED'));\n    return this;\n  }\n\n  data = this._normalizeEntryData(data);\n\n  if (typeof data.name !== 'string' || data.name.length === 0) {\n    this.emit('error', new ArchiverError('ENTRYNAMEREQUIRED'));\n    return this;\n  }\n\n  if (data.type === 'directory' && !this._moduleSupports('directory')) {\n    this.emit('error', new ArchiverError('DIRECTORYNOTSUPPORTED', { name: data.name }));\n    return this;\n  }\n\n  source = util.normalizeInputSource(source);\n\n  if (Buffer.isBuffer(source)) {\n    data.sourceType = 'buffer';\n  } else if (util.isStream(source)) {\n    data.sourceType = 'stream';\n  } else {\n    this.emit('error', new ArchiverError('INPUTSTEAMBUFFERREQUIRED', { name: data.name }));\n    return this;\n  }\n\n  this._entriesCount++;\n  this._queue.push({\n    data: data,\n    source: source\n  });\n\n  return this;\n};\n\n/**\n * Appends a directory and its files, recursively, given its dirpath.\n *\n * @param  {String} dirpath The source directory path.\n * @param  {String} destpath The destination path within the archive.\n * @param  {(EntryData|Function)} data See also [ZipEntryData]{@link ZipEntryData} and\n * [TarEntryData]{@link TarEntryData}.\n * @return {this}\n */\nArchiver.prototype.directory = function(dirpath, destpath, data) {\n  if (this._state.finalize || this._state.aborted) {\n    this.emit('error', new ArchiverError('QUEUECLOSED'));\n    return this;\n  }\n\n  if (typeof dirpath !== 'string' || dirpath.length === 0) {\n    this.emit('error', new ArchiverError('DIRECTORYDIRPATHREQUIRED'));\n    return this;\n  }\n\n  this._pending++;\n\n  if (destpath === false) {\n    destpath = '';\n  } else if (typeof destpath !== 'string'){\n    destpath = dirpath;\n  }\n\n  var dataFunction = false;\n  if (typeof data === 'function') {\n    dataFunction = data;\n    data = {};\n  } else if (typeof data !== 'object') {\n    data = {};\n  }\n\n  var globOptions = {\n    stat: true,\n    dot: true\n  };\n\n  function onGlobEnd() {\n    this._pending--;\n    this._maybeFinalize();\n  }\n\n  function onGlobError(err) {\n    this.emit('error', err);\n  }\n\n  function onGlobMatch(match){\n    globber.pause();\n\n    var ignoreMatch = false;\n    var entryData = Object.assign({}, data);\n    entryData.name = match.relative;\n    entryData.prefix = destpath;\n    entryData.stats = match.stat;\n    entryData.callback = globber.resume.bind(globber);\n\n    try {\n      if (dataFunction) {\n        entryData = dataFunction(entryData);\n\n        if (entryData === false) {\n          ignoreMatch = true;\n        } else if (typeof entryData !== 'object') {\n          throw new ArchiverError('DIRECTORYFUNCTIONINVALIDDATA', { dirpath: dirpath });\n        }\n      }\n    } catch(e) {\n      this.emit('error', e);\n      return;\n    }\n\n    if (ignoreMatch) {\n      globber.resume();\n      return;\n    }\n\n    this._append(match.absolute, entryData);\n  }\n\n  var globber = glob(dirpath, globOptions);\n  globber.on('error', onGlobError.bind(this));\n  globber.on('match', onGlobMatch.bind(this));\n  globber.on('end', onGlobEnd.bind(this));\n\n  return this;\n};\n\n/**\n * Appends a file given its filepath using a\n * [lazystream]{@link https://github.com/jpommerening/node-lazystream} wrapper to\n * prevent issues with open file limits.\n *\n * When the instance has received, processed, and emitted the file, the `entry`\n * event is fired.\n *\n * @param  {String} filepath The source filepath.\n * @param  {EntryData} data See also [ZipEntryData]{@link ZipEntryData} and\n * [TarEntryData]{@link TarEntryData}.\n * @return {this}\n */\nArchiver.prototype.file = function(filepath, data) {\n  if (this._state.finalize || this._state.aborted) {\n    this.emit('error', new ArchiverError('QUEUECLOSED'));\n    return this;\n  }\n\n  if (typeof filepath !== 'string' || filepath.length === 0) {\n    this.emit('error', new ArchiverError('FILEFILEPATHREQUIRED'));\n    return this;\n  }\n\n  this._append(filepath, data);\n\n  return this;\n};\n\n/**\n * Appends multiple files that match a glob pattern.\n *\n * @param  {String} pattern The [glob pattern]{@link https://github.com/isaacs/minimatch} to match.\n * @param  {Object} options See [node-readdir-glob]{@link https://github.com/yqnn/node-readdir-glob#options}.\n * @param  {EntryData} data See also [ZipEntryData]{@link ZipEntryData} and\n * [TarEntryData]{@link TarEntryData}.\n * @return {this}\n */\nArchiver.prototype.glob = function(pattern, options, data) {\n  this._pending++;\n\n  options = util.defaults(options, {\n    stat: true,\n    pattern: pattern\n  });\n\n  function onGlobEnd() {\n    this._pending--;\n    this._maybeFinalize();\n  }\n\n  function onGlobError(err) {\n    this.emit('error', err);\n  }\n\n  function onGlobMatch(match){\n    globber.pause();\n    var entryData = Object.assign({}, data);\n    entryData.callback = globber.resume.bind(globber);\n    entryData.stats = match.stat;\n    entryData.name = match.relative;\n\n    this._append(match.absolute, entryData);\n  }\n\n  var globber = glob(options.cwd || '.', options);\n  globber.on('error', onGlobError.bind(this));\n  globber.on('match', onGlobMatch.bind(this));\n  globber.on('end', onGlobEnd.bind(this));\n\n  return this;\n};\n\n/**\n * Finalizes the instance and prevents further appending to the archive\n * structure (queue will continue til drained).\n *\n * The `end`, `close` or `finish` events on the destination stream may fire\n * right after calling this method so you should set listeners beforehand to\n * properly detect stream completion.\n *\n * @return {Promise}\n */\nArchiver.prototype.finalize = function() {\n  if (this._state.aborted) {\n    var abortedError = new ArchiverError('ABORTED');\n    this.emit('error', abortedError);\n    return Promise.reject(abortedError);\n  }\n\n  if (this._state.finalize) {\n    var finalizingError = new ArchiverError('FINALIZING');\n    this.emit('error', finalizingError);\n    return Promise.reject(finalizingError);\n  }\n\n  this._state.finalize = true;\n\n  if (this._pending === 0 && this._queue.idle() && this._statQueue.idle()) {\n    this._finalize();\n  }\n\n  var self = this;\n\n  return new Promise(function(resolve, reject) {\n    var errored;\n\n    self._module.on('end', function() {\n      if (!errored) {\n        resolve();\n      }\n    })\n\n    self._module.on('error', function(err) {\n      errored = true;\n      reject(err);\n    })\n  })\n};\n\n/**\n * Sets the module format name used for archiving.\n *\n * @param {String} format The name of the format.\n * @return {this}\n */\nArchiver.prototype.setFormat = function(format) {\n  if (this._format) {\n    this.emit('error', new ArchiverError('FORMATSET'));\n    return this;\n  }\n\n  this._format = format;\n\n  return this;\n};\n\n/**\n * Sets the module used for archiving.\n *\n * @param {Function} module The function for archiver to interact with.\n * @return {this}\n */\nArchiver.prototype.setModule = function(module) {\n  if (this._state.aborted) {\n    this.emit('error', new ArchiverError('ABORTED'));\n    return this;\n  }\n\n  if (this._state.module) {\n    this.emit('error', new ArchiverError('MODULESET'));\n    return this;\n  }\n\n  this._module = module;\n  this._modulePipe();\n\n  return this;\n};\n\n/**\n * Appends a symlink to the instance.\n *\n * This does NOT interact with filesystem and is used for programmatically creating symlinks.\n *\n * @param  {String} filepath The symlink path (within archive).\n * @param  {String} target The target path (within archive).\n * @param  {Number} mode Sets the entry permissions.\n * @return {this}\n */\nArchiver.prototype.symlink = function(filepath, target, mode) {\n  if (this._state.finalize || this._state.aborted) {\n    this.emit('error', new ArchiverError('QUEUECLOSED'));\n    return this;\n  }\n\n  if (typeof filepath !== 'string' || filepath.length === 0) {\n    this.emit('error', new ArchiverError('SYMLINKFILEPATHREQUIRED'));\n    return this;\n  }\n\n  if (typeof target !== 'string' || target.length === 0) {\n    this.emit('error', new ArchiverError('SYMLINKTARGETREQUIRED', { filepath: filepath }));\n    return this;\n  }\n\n  if (!this._moduleSupports('symlink')) {\n    this.emit('error', new ArchiverError('SYMLINKNOTSUPPORTED', { filepath: filepath }));\n    return this;\n  }\n\n  var data = {};\n  data.type = 'symlink';\n  data.name = filepath.replace(/\\\\/g, '/');\n  data.linkname = target.replace(/\\\\/g, '/');\n  data.sourceType = 'buffer';\n\n  if (typeof mode === \"number\") {\n    data.mode = mode;\n  }\n\n  this._entriesCount++;\n  this._queue.push({\n    data: data,\n    source: Buffer.concat([])\n  });\n\n  return this;\n};\n\n/**\n * Returns the current length (in bytes) that has been emitted.\n *\n * @return {Number}\n */\nArchiver.prototype.pointer = function() {\n  return this._pointer;\n};\n\n/**\n * Middleware-like helper that has yet to be fully implemented.\n *\n * @private\n * @param  {Function} plugin\n * @return {this}\n */\nArchiver.prototype.use = function(plugin) {\n  this._streams.push(plugin);\n  return this;\n};\n\nmodule.exports = Archiver;\n\n/**\n * @typedef {Object} CoreOptions\n * @global\n * @property {Number} [statConcurrency=4] Sets the number of workers used to\n * process the internal fs stat queue.\n */\n\n/**\n * @typedef {Object} TransformOptions\n * @property {Boolean} [allowHalfOpen=true] If set to false, then the stream\n * will automatically end the readable side when the writable side ends and vice\n * versa.\n * @property {Boolean} [readableObjectMode=false] Sets objectMode for readable\n * side of the stream. Has no effect if objectMode is true.\n * @property {Boolean} [writableObjectMode=false] Sets objectMode for writable\n * side of the stream. Has no effect if objectMode is true.\n * @property {Boolean} [decodeStrings=true] Whether or not to decode strings\n * into Buffers before passing them to _write(). `Writable`\n * @property {String} [encoding=NULL] If specified, then buffers will be decoded\n * to strings using the specified encoding. `Readable`\n * @property {Number} [highWaterMark=16kb] The maximum number of bytes to store\n * in the internal buffer before ceasing to read from the underlying resource.\n * `Readable` `Writable`\n * @property {Boolean} [objectMode=false] Whether this stream should behave as a\n * stream of objects. Meaning that stream.read(n) returns a single value instead\n * of a Buffer of size n. `Readable` `Writable`\n */\n\n/**\n * @typedef {Object} EntryData\n * @property {String} name Sets the entry name including internal path.\n * @property {(String|Date)} [date=NOW()] Sets the entry date.\n * @property {Number} [mode=D:0755/F:0644] Sets the entry permissions.\n * @property {String} [prefix] Sets a path prefix for the entry name. Useful\n * when working with methods like `directory` or `glob`.\n * @property {fs.Stats} [stats] Sets the fs stat data for this entry allowing\n * for reduction of fs stat calls when stat data is already known.\n */\n\n/**\n * @typedef {Object} ErrorData\n * @property {String} message The message of the error.\n * @property {String} code The error code assigned to this error.\n * @property {String} data Additional data provided for reporting or debugging (where available).\n */\n\n/**\n * @typedef {Object} ProgressData\n * @property {Object} entries\n * @property {Number} entries.total Number of entries that have been appended.\n * @property {Number} entries.processed Number of entries that have been processed.\n * @property {Object} fs\n * @property {Number} fs.totalBytes Number of bytes that have been appended. Calculated asynchronously and might not be accurate: it growth while entries are added. (based on fs.Stats)\n * @property {Number} fs.processedBytes Number of bytes that have been processed. (based on fs.Stats)\n */\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/archiver/lib/core.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/archiver/lib/error.js":
/*!********************************************!*\
  !*** ./node_modules/archiver/lib/error.js ***!
  \********************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("/**\n * Archiver Core\n *\n * @ignore\n * @license [MIT]{@link https://github.com/archiverjs/node-archiver/blob/master/LICENSE}\n * @copyright (c) 2012-2014 Chris Talkington, contributors.\n */\n\nvar util = __webpack_require__(/*! util */ \"util\");\n\nconst ERROR_CODES = {\n  'ABORTED': 'archive was aborted',\n  'DIRECTORYDIRPATHREQUIRED': 'diretory dirpath argument must be a non-empty string value',\n  'DIRECTORYFUNCTIONINVALIDDATA': 'invalid data returned by directory custom data function',\n  'ENTRYNAMEREQUIRED': 'entry name must be a non-empty string value',\n  'FILEFILEPATHREQUIRED': 'file filepath argument must be a non-empty string value',\n  'FINALIZING': 'archive already finalizing',\n  'QUEUECLOSED': 'queue closed',\n  'NOENDMETHOD': 'no suitable finalize/end method defined by module',\n  'DIRECTORYNOTSUPPORTED': 'support for directory entries not defined by module',\n  'FORMATSET': 'archive format already set',\n  'INPUTSTEAMBUFFERREQUIRED': 'input source must be valid Stream or Buffer instance',\n  'MODULESET': 'module already set',\n  'SYMLINKNOTSUPPORTED': 'support for symlink entries not defined by module',\n  'SYMLINKFILEPATHREQUIRED': 'symlink filepath argument must be a non-empty string value',\n  'SYMLINKTARGETREQUIRED': 'symlink target argument must be a non-empty string value',\n  'ENTRYNOTSUPPORTED': 'entry not supported'\n};\n\nfunction ArchiverError(code, data) {\n  Error.captureStackTrace(this, this.constructor);\n  //this.name = this.constructor.name;\n  this.message = ERROR_CODES[code] || code;\n  this.code = code;\n  this.data = data;\n}\n\nutil.inherits(ArchiverError, Error);\n\nexports = module.exports = ArchiverError;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/archiver/lib/error.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/archiver/lib/plugins/json.js":
/*!***************************************************!*\
  !*** ./node_modules/archiver/lib/plugins/json.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * JSON Format Plugin\n *\n * @module plugins/json\n * @license [MIT]{@link https://github.com/archiverjs/node-archiver/blob/master/LICENSE}\n * @copyright (c) 2012-2014 Chris Talkington, contributors.\n */\nvar inherits = (__webpack_require__(/*! util */ \"util\").inherits);\nvar Transform = (__webpack_require__(/*! readable-stream */ \"(ssr)/./node_modules/readable-stream/readable.js\").Transform);\n\nvar crc32 = __webpack_require__(/*! buffer-crc32 */ \"(ssr)/./node_modules/buffer-crc32/index.js\");\nvar util = __webpack_require__(/*! archiver-utils */ \"(ssr)/./node_modules/archiver-utils/index.js\");\n\n/**\n * @constructor\n * @param {(JsonOptions|TransformOptions)} options\n */\nvar Json = function(options) {\n  if (!(this instanceof Json)) {\n    return new Json(options);\n  }\n\n  options = this.options = util.defaults(options, {});\n\n  Transform.call(this, options);\n\n  this.supports = {\n    directory: true,\n    symlink: true\n  };\n\n  this.files = [];\n};\n\ninherits(Json, Transform);\n\n/**\n * [_transform description]\n *\n * @private\n * @param  {Buffer}   chunk\n * @param  {String}   encoding\n * @param  {Function} callback\n * @return void\n */\nJson.prototype._transform = function(chunk, encoding, callback) {\n  callback(null, chunk);\n};\n\n/**\n * [_writeStringified description]\n *\n * @private\n * @return void\n */\nJson.prototype._writeStringified = function() {\n  var fileString = JSON.stringify(this.files);\n  this.write(fileString);\n};\n\n/**\n * [append description]\n *\n * @param  {(Buffer|Stream)}   source\n * @param  {EntryData}   data\n * @param  {Function} callback\n * @return void\n */\nJson.prototype.append = function(source, data, callback) {\n  var self = this;\n\n  data.crc32 = 0;\n\n  function onend(err, sourceBuffer) {\n    if (err) {\n      callback(err);\n      return;\n    }\n\n    data.size = sourceBuffer.length || 0;\n    data.crc32 = crc32.unsigned(sourceBuffer);\n\n    self.files.push(data);\n\n    callback(null, data);\n  }\n\n  if (data.sourceType === 'buffer') {\n    onend(null, source);\n  } else if (data.sourceType === 'stream') {\n    util.collectStream(source, onend);\n  }\n};\n\n/**\n * [finalize description]\n *\n * @return void\n */\nJson.prototype.finalize = function() {\n  this._writeStringified();\n  this.end();\n};\n\nmodule.exports = Json;\n\n/**\n * @typedef {Object} JsonOptions\n * @global\n */\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/archiver/lib/plugins/json.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/archiver/lib/plugins/tar.js":
/*!**************************************************!*\
  !*** ./node_modules/archiver/lib/plugins/tar.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * TAR Format Plugin\n *\n * @module plugins/tar\n * @license [MIT]{@link https://github.com/archiverjs/node-archiver/blob/master/LICENSE}\n * @copyright (c) 2012-2014 Chris Talkington, contributors.\n */\nvar zlib = __webpack_require__(/*! zlib */ \"zlib\");\n\nvar engine = __webpack_require__(/*! tar-stream */ \"(ssr)/./node_modules/tar-stream/index.js\");\nvar util = __webpack_require__(/*! archiver-utils */ \"(ssr)/./node_modules/archiver-utils/index.js\");\n\n/**\n * @constructor\n * @param {TarOptions} options\n */\nvar Tar = function(options) {\n  if (!(this instanceof Tar)) {\n    return new Tar(options);\n  }\n\n  options = this.options = util.defaults(options, {\n    gzip: false\n  });\n\n  if (typeof options.gzipOptions !== 'object') {\n    options.gzipOptions = {};\n  }\n\n  this.supports = {\n    directory: true,\n    symlink: true\n  };\n\n  this.engine = engine.pack(options);\n  this.compressor = false;\n\n  if (options.gzip) {\n    this.compressor = zlib.createGzip(options.gzipOptions);\n    this.compressor.on('error', this._onCompressorError.bind(this));\n  }\n};\n\n/**\n * [_onCompressorError description]\n *\n * @private\n * @param  {Error} err\n * @return void\n */\nTar.prototype._onCompressorError = function(err) {\n  this.engine.emit('error', err);\n};\n\n/**\n * [append description]\n *\n * @param  {(Buffer|Stream)} source\n * @param  {TarEntryData} data\n * @param  {Function} callback\n * @return void\n */\nTar.prototype.append = function(source, data, callback) {\n  var self = this;\n\n  data.mtime = data.date;\n\n  function append(err, sourceBuffer) {\n    if (err) {\n      callback(err);\n      return;\n    }\n\n    self.engine.entry(data, sourceBuffer, function(err) {\n      callback(err, data);\n    });\n  }\n\n  if (data.sourceType === 'buffer') {\n    append(null, source);\n  } else if (data.sourceType === 'stream' && data.stats) {\n    data.size = data.stats.size;\n\n    var entry = self.engine.entry(data, function(err) {\n      callback(err, data);\n    });\n\n    source.pipe(entry);\n  } else if (data.sourceType === 'stream') {\n    util.collectStream(source, append);\n  }\n};\n\n/**\n * [finalize description]\n *\n * @return void\n */\nTar.prototype.finalize = function() {\n  this.engine.finalize();\n};\n\n/**\n * [on description]\n *\n * @return this.engine\n */\nTar.prototype.on = function() {\n  return this.engine.on.apply(this.engine, arguments);\n};\n\n/**\n * [pipe description]\n *\n * @param  {String} destination\n * @param  {Object} options\n * @return this.engine\n */\nTar.prototype.pipe = function(destination, options) {\n  if (this.compressor) {\n    return this.engine.pipe.apply(this.engine, [this.compressor]).pipe(destination, options);\n  } else {\n    return this.engine.pipe.apply(this.engine, arguments);\n  }\n};\n\n/**\n * [unpipe description]\n *\n * @return this.engine\n */\nTar.prototype.unpipe = function() {\n  if (this.compressor) {\n    return this.compressor.unpipe.apply(this.compressor, arguments);\n  } else {\n    return this.engine.unpipe.apply(this.engine, arguments);\n  }\n};\n\nmodule.exports = Tar;\n\n/**\n * @typedef {Object} TarOptions\n * @global\n * @property {Boolean} [gzip=false] Compress the tar archive using gzip.\n * @property {Object} [gzipOptions] Passed to [zlib]{@link https://nodejs.org/api/zlib.html#zlib_class_options}\n * to control compression.\n * @property {*} [*] See [tar-stream]{@link https://github.com/mafintosh/tar-stream} documentation for additional properties.\n */\n\n/**\n * @typedef {Object} TarEntryData\n * @global\n * @property {String} name Sets the entry name including internal path.\n * @property {(String|Date)} [date=NOW()] Sets the entry date.\n * @property {Number} [mode=D:0755/F:0644] Sets the entry permissions.\n * @property {String} [prefix] Sets a path prefix for the entry name. Useful\n * when working with methods like `directory` or `glob`.\n * @property {fs.Stats} [stats] Sets the fs stat data for this entry allowing\n * for reduction of fs stat calls when stat data is already known.\n */\n\n/**\n * TarStream Module\n * @external TarStream\n * @see {@link https://github.com/mafintosh/tar-stream}\n */\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/archiver/lib/plugins/tar.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/archiver/lib/plugins/zip.js":
/*!**************************************************!*\
  !*** ./node_modules/archiver/lib/plugins/zip.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * ZIP Format Plugin\n *\n * @module plugins/zip\n * @license [MIT]{@link https://github.com/archiverjs/node-archiver/blob/master/LICENSE}\n * @copyright (c) 2012-2014 Chris Talkington, contributors.\n */\nvar engine = __webpack_require__(/*! zip-stream */ \"(ssr)/./node_modules/zip-stream/index.js\");\nvar util = __webpack_require__(/*! archiver-utils */ \"(ssr)/./node_modules/archiver-utils/index.js\");\n\n/**\n * @constructor\n * @param {ZipOptions} [options]\n * @param {String} [options.comment] Sets the zip archive comment.\n * @param {Boolean} [options.forceLocalTime=false] Forces the archive to contain local file times instead of UTC.\n * @param {Boolean} [options.forceZip64=false] Forces the archive to contain ZIP64 headers.\n * @param {Boolean} [options.namePrependSlash=false] Prepends a forward slash to archive file paths.\n * @param {Boolean} [options.store=false] Sets the compression method to STORE.\n * @param {Object} [options.zlib] Passed to [zlib]{@link https://nodejs.org/api/zlib.html#zlib_class_options}\n */\nvar Zip = function(options) {\n  if (!(this instanceof Zip)) {\n    return new Zip(options);\n  }\n\n  options = this.options = util.defaults(options, {\n    comment: '',\n    forceUTC: false,\n    namePrependSlash: false,\n    store: false\n  });\n\n  this.supports = {\n    directory: true,\n    symlink: true\n  };\n\n  this.engine = new engine(options);\n};\n\n/**\n * @param  {(Buffer|Stream)} source\n * @param  {ZipEntryData} data\n * @param  {String} data.name Sets the entry name including internal path.\n * @param  {(String|Date)} [data.date=NOW()] Sets the entry date.\n * @param  {Number} [data.mode=D:0755/F:0644] Sets the entry permissions.\n * @param  {String} [data.prefix] Sets a path prefix for the entry name. Useful\n * when working with methods like `directory` or `glob`.\n * @param  {fs.Stats} [data.stats] Sets the fs stat data for this entry allowing\n * for reduction of fs stat calls when stat data is already known.\n * @param  {Boolean} [data.store=ZipOptions.store] Sets the compression method to STORE.\n * @param  {Function} callback\n * @return void\n */\nZip.prototype.append = function(source, data, callback) {\n  this.engine.entry(source, data, callback);\n};\n\n/**\n * @return void\n */\nZip.prototype.finalize = function() {\n  this.engine.finalize();\n};\n\n/**\n * @return this.engine\n */\nZip.prototype.on = function() {\n  return this.engine.on.apply(this.engine, arguments);\n};\n\n/**\n * @return this.engine\n */\nZip.prototype.pipe = function() {\n  return this.engine.pipe.apply(this.engine, arguments);\n};\n\n/**\n * @return this.engine\n */\nZip.prototype.unpipe = function() {\n  return this.engine.unpipe.apply(this.engine, arguments);\n};\n\nmodule.exports = Zip;\n\n/**\n * @typedef {Object} ZipOptions\n * @global\n * @property {String} [comment] Sets the zip archive comment.\n * @property {Boolean} [forceLocalTime=false] Forces the archive to contain local file times instead of UTC.\n * @property {Boolean} [forceZip64=false] Forces the archive to contain ZIP64 headers.\n * @prpperty {Boolean} [namePrependSlash=false] Prepends a forward slash to archive file paths.\n * @property {Boolean} [store=false] Sets the compression method to STORE.\n * @property {Object} [zlib] Passed to [zlib]{@link https://nodejs.org/api/zlib.html#zlib_class_options}\n * to control compression.\n * @property {*} [*] See [zip-stream]{@link https://archiverjs.com/zip-stream/ZipStream.html} documentation for current list of properties.\n */\n\n/**\n * @typedef {Object} ZipEntryData\n * @global\n * @property {String} name Sets the entry name including internal path.\n * @property {(String|Date)} [date=NOW()] Sets the entry date.\n * @property {Number} [mode=D:0755/F:0644] Sets the entry permissions.\n * @property {Boolean} [namePrependSlash=ZipOptions.namePrependSlash] Prepends a forward slash to archive file paths.\n * @property {String} [prefix] Sets a path prefix for the entry name. Useful\n * when working with methods like `directory` or `glob`.\n * @property {fs.Stats} [stats] Sets the fs stat data for this entry allowing\n * for reduction of fs stat calls when stat data is already known.\n * @property {Boolean} [store=ZipOptions.store] Sets the compression method to STORE.\n */\n\n/**\n * ZipStream Module\n * @external ZipStream\n * @see {@link https://www.archiverjs.com/zip-stream/ZipStream.html}\n */\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/archiver/lib/plugins/zip.js\n");

/***/ })

};
;