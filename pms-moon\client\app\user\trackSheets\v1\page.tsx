import { getAllData, getCookie } from "@/lib/helpers";
import {
  carrier_routes,
  client_routes,
  employee_routes,
  associate_routes,
} from "@/lib/routePath";
import ManageWorkSheet from "./ManageTrackSheet";

const Page = async () => {
  const client = await getAllData(
    `${client_routes.GETALL_CLIENT}?minimal=true`
  );
  const clientData = client?.data;

  const carrier = await getAllData(
    `${carrier_routes.GETALL_CARRIER}?notIncludeWorkReport=true`
  );
  const carrierData = carrier?.data;

  const associate = await getAllData(associate_routes.GETALL_ASSOCIATE);
  const associateData = associate?.data;

  const userData = await getAllData(
    `${employee_routes.GETCURRENT_USER}?TracksheetUser=true`
  );

  console.timeEnd("Time taken for: " + employee_routes.GETCURRENT_USER);
  const userPermissions = userData?.role?.role_permission || [];

  const corporationCookie = await getCookie("corporationtoken");

  const permissions = corporationCookie ? ["allow_all"] : userPermissions;

  const actions = permissions?.map((item) => item?.permission?.action);

  return (
    <div className="w-full">
      <ManageWorkSheet
        client={clientData}
        carrier={carrierData}
        permissions={permissions}
        actions={actions}
        userData={userData}
        associate={associateData}
      />
    </div>
  );
};

export default Page;
