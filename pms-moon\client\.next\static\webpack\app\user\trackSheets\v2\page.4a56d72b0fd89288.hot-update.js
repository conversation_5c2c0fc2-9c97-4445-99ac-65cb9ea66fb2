"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/trackSheets/v2/page",{

/***/ "(app-pages-browser)/./app/user/trackSheets/v2/createTrackSheet.tsx":
/*!******************************************************!*\
  !*** ./app/user/trackSheets/v2/createTrackSheet.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./components/ui/form.tsx\");\n/* harmony import */ var _app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/_component/FormInput */ \"(app-pages-browser)/./app/_component/FormInput.tsx\");\n/* harmony import */ var _app_component_FormCheckboxGroup__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/_component/FormCheckboxGroup */ \"(app-pages-browser)/./app/_component/FormCheckboxGroup.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-minus.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-plus.js\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/helpers */ \"(app-pages-browser)/./lib/helpers.ts\");\n/* harmony import */ var _lib_routePath__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/routePath */ \"(app-pages-browser)/./lib/routePath.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var _app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/_component/SearchSelect */ \"(app-pages-browser)/./app/_component/SearchSelect.tsx\");\n/* harmony import */ var _app_component_PageInput__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/_component/PageInput */ \"(app-pages-browser)/./app/_component/PageInput.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./components/ui/tooltip.tsx\");\n/* harmony import */ var _LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./LegrandDetailsComponent */ \"(app-pages-browser)/./app/user/trackSheets/v2/LegrandDetailsComponent.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst validateFtpPageFormat = (value)=>{\n    if (!value || value.trim() === \"\") return false;\n    const ftpPageRegex = /^(\\d+)\\s+of\\s+(\\d+)$/i;\n    const match = value.match(ftpPageRegex);\n    if (!match) return false;\n    const currentPage = parseInt(match[1], 10);\n    const totalPages = parseInt(match[2], 10);\n    return currentPage > 0 && totalPages > 0 && currentPage <= totalPages;\n};\nconst trackSheetSchema = zod__WEBPACK_IMPORTED_MODULE_15__.z.object({\n    clientId: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Client is required\"),\n    entries: zod__WEBPACK_IMPORTED_MODULE_15__.z.array(zod__WEBPACK_IMPORTED_MODULE_15__.z.object({\n        company: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Company is required\"),\n        division: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        invoice: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Invoice is required\"),\n        masterInvoice: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        bol: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"BOL is required\"),\n        invoiceDate: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Invoice date is required\"),\n        receivedDate: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Received date is required\"),\n        shipmentDate: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Shipment date is required\"),\n        carrierName: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Carrier name is required\"),\n        invoiceStatus: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Invoice status is required\"),\n        manualMatching: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Manual matching is required\"),\n        invoiceType: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Invoice type is required\"),\n        currency: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Currency is required\"),\n        qtyShipped: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Quantity shipped is required\"),\n        weightUnitName: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Weight unit is required\"),\n        quantityBilledText: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        invoiceTotal: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Invoice total is required\"),\n        savings: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        ftpFileName: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"FTP File Name is required\"),\n        ftpPage: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"FTP Page is required\").refine((value)=>validateFtpPageFormat(value), (value)=>{\n            if (!value || value.trim() === \"\") {\n                return {\n                    message: \"FTP Page is required\"\n                };\n            }\n            const ftpPageRegex = /^(\\d+)\\s+of\\s+(\\d+)$/i;\n            const match = value.match(ftpPageRegex);\n            if (!match) {\n                return {\n                    message: \"\"\n                };\n            }\n            const currentPage = parseInt(match[1], 10);\n            const totalPages = parseInt(match[2], 10);\n            if (currentPage <= 0 || totalPages <= 0) {\n                return {\n                    message: \"Page numbers must be positive (greater than 0)\"\n                };\n            }\n            if (currentPage > totalPages) {\n                return {\n                    message: \"Please enter a page number between \".concat(totalPages, \" and \").concat(currentPage, \" \")\n                };\n            }\n            return {\n                message: \"Invalid page format\"\n            };\n        }),\n        docAvailable: zod__WEBPACK_IMPORTED_MODULE_15__.z.array(zod__WEBPACK_IMPORTED_MODULE_15__.z.string()).optional().default([]),\n        notes: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        mistake: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        legrandAlias: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        legrandCompanyName: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        legrandAddress: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        legrandZipcode: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        shipperAlias: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        shipperAddress: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        shipperZipcode: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        consigneeAlias: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        consigneeAddress: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        consigneeZipcode: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        billtoAlias: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        billtoAddress: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        billtoZipcode: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        customFields: zod__WEBPACK_IMPORTED_MODULE_15__.z.array(zod__WEBPACK_IMPORTED_MODULE_15__.z.object({\n            id: zod__WEBPACK_IMPORTED_MODULE_15__.z.string(),\n            name: zod__WEBPACK_IMPORTED_MODULE_15__.z.string(),\n            type: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n            value: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional()\n        })).default([])\n    }))\n});\nconst CreateTrackSheet = (param)=>{\n    let { client, carrier, associate, userData, initialAssociateId, initialClientId, showFullForm, setShowFullForm// Add this prop\n     } = param;\n    _s();\n    const companyFieldRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const [customFields, setCustomFields] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [generatedFilenames, setGeneratedFilenames] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filenameValidation, setFilenameValidation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [missingFields, setMissingFields] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [legrandData, setLegrandData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [customFieldsRefresh, setCustomFieldsRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Remove these states as they're now passed as props\n    // const [showFullForm, setShowFullForm] = useState(false);\n    // const [initialAssociateId, setInitialAssociateId] = useState(\"\");\n    // const [initialClientId, setInitialClientId] = useState(\"\");\n    // Remove the selectionForm as it's now in the parent component\n    const associateOptions = associate === null || associate === void 0 ? void 0 : associate.map((a)=>{\n        var _a_id;\n        return {\n            value: (_a_id = a.id) === null || _a_id === void 0 ? void 0 : _a_id.toString(),\n            label: a.name,\n            name: a.name\n        };\n    });\n    const carrierOptions = carrier === null || carrier === void 0 ? void 0 : carrier.map((c)=>{\n        var _c_id;\n        return {\n            value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n            label: c.name\n        };\n    });\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(trackSheetSchema),\n        defaultValues: {\n            associateId: \"\",\n            clientId: \"\",\n            entries: [\n                {\n                    company: \"\",\n                    division: \"\",\n                    invoice: \"\",\n                    masterInvoice: \"\",\n                    bol: \"\",\n                    invoiceDate: new Date().toISOString().split(\"T\")[0],\n                    receivedDate: new Date().toISOString().split(\"T\")[0],\n                    shipmentDate: new Date().toISOString().split(\"T\")[0],\n                    carrierName: \"\",\n                    invoiceStatus: \"\",\n                    manualMatching: \"\",\n                    invoiceType: \"\",\n                    currency: \"\",\n                    qtyShipped: \"\",\n                    weightUnitName: \"\",\n                    quantityBilledText: \"\",\n                    invoiceTotal: \"\",\n                    savings: \"\",\n                    ftpFileName: \"\",\n                    ftpPage: \"\",\n                    docAvailable: [],\n                    notes: \"\",\n                    mistake: \"\",\n                    legrandAlias: \"\",\n                    legrandCompanyName: \"\",\n                    legrandAddress: \"\",\n                    legrandZipcode: \"\",\n                    shipperAlias: \"\",\n                    shipperAddress: \"\",\n                    shipperZipcode: \"\",\n                    consigneeAlias: \"\",\n                    consigneeAddress: \"\",\n                    consigneeZipcode: \"\",\n                    billtoAlias: \"\",\n                    billtoAddress: \"\",\n                    billtoZipcode: \"\",\n                    customFields: []\n                }\n            ]\n        }\n    });\n    const getFilteredClientOptions = ()=>{\n        if (!initialAssociateId) {\n            return (client === null || client === void 0 ? void 0 : client.map((c)=>{\n                var _c_id;\n                return {\n                    value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n                    label: c.client_name,\n                    name: c.client_name\n                };\n            })) || [];\n        }\n        const filteredClients = (client === null || client === void 0 ? void 0 : client.filter((c)=>{\n            var _c_associateId;\n            return ((_c_associateId = c.associateId) === null || _c_associateId === void 0 ? void 0 : _c_associateId.toString()) === initialAssociateId;\n        })) || [];\n        return filteredClients.map((c)=>{\n            var _c_id;\n            return {\n                value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n                label: c.client_name,\n                name: c.client_name\n            };\n        });\n    };\n    const clientOptions = getFilteredClientOptions();\n    const entries = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useWatch)({\n        control: form.control,\n        name: \"entries\"\n    });\n    const validateClientForAssociate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((associateId, currentClientId)=>{\n        if (associateId && currentClientId) {\n            var _currentClient_associateId;\n            const currentClient = client === null || client === void 0 ? void 0 : client.find((c)=>{\n                var _c_id;\n                return ((_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString()) === currentClientId;\n            });\n            if (currentClient && ((_currentClient_associateId = currentClient.associateId) === null || _currentClient_associateId === void 0 ? void 0 : _currentClient_associateId.toString()) !== associateId) {\n                form.setValue(\"clientId\", \"\");\n                // setInitialClientId(\"\"); // Removed as we can't modify props directly\n                return false;\n            }\n        }\n        return true;\n    }, [\n        client,\n        form\n    ]);\n    const clearEntrySpecificClients = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const currentEntries = form.getValues(\"entries\") || [];\n        if (currentEntries.length > 0) {\n            const hasEntrySpecificClients = currentEntries.some((entry)=>entry.clientId);\n            if (hasEntrySpecificClients) {\n                const updatedEntries = currentEntries.map((entry)=>({\n                        ...entry,\n                        clientId: \"\"\n                    }));\n                form.setValue(\"entries\", updatedEntries);\n            }\n        }\n    }, [\n        form\n    ]);\n    const fetchLegrandData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            const response = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_7__.getAllData)(_lib_routePath__WEBPACK_IMPORTED_MODULE_8__.legrandMapping_routes.GET_LEGRAND_MAPPINGS);\n            if (response && Array.isArray(response)) {\n                setLegrandData(response);\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Error fetching LEGRAND mapping data:\", error);\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        fetchLegrandData();\n    }, [\n        fetchLegrandData\n    ]);\n    // Initialize form with passed associate and client IDs\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (initialAssociateId && initialClientId) {\n            form.setValue(\"associateId\", initialAssociateId);\n            form.setValue(\"clientId\", initialClientId);\n            setTimeout(()=>{\n                handleCompanyAutoPopulation(0, initialClientId);\n                handleCustomFieldsFetch(0, initialClientId);\n                updateFilenames();\n            }, 100);\n        }\n    }, [\n        initialAssociateId,\n        initialClientId,\n        form,\n        handleCompanyAutoPopulation,\n        handleCustomFieldsFetch,\n        updateFilenames\n    ]);\n    const handleLegrandDataChange = (entryIndex, businessUnit, divisionCode)=>{\n        form.setValue(\"entries.\".concat(entryIndex, \".company\"), businessUnit);\n        if (divisionCode) {\n            form.setValue(\"entries.\".concat(entryIndex, \".division\"), divisionCode);\n        } else {\n            form.setValue(\"entries.\".concat(entryIndex, \".division\"), \"\");\n        }\n    };\n    const fetchCustomFieldsForClient = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (clientId)=>{\n        if (!clientId) return [];\n        try {\n            const allCustomFieldsResponse = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_7__.getAllData)(\"\".concat(_lib_routePath__WEBPACK_IMPORTED_MODULE_8__.clientCustomFields_routes.GET_CLIENT_CUSTOM_FIELDS, \"/\").concat(clientId));\n            let customFieldsData = [];\n            if (allCustomFieldsResponse && allCustomFieldsResponse.custom_fields && allCustomFieldsResponse.custom_fields.length > 0) {\n                customFieldsData = allCustomFieldsResponse.custom_fields.map((field)=>{\n                    let autoFilledValue = \"\";\n                    if (field.type === \"AUTO\") {\n                        if (field.autoOption === \"DATE\") {\n                            autoFilledValue = new Date().toISOString().split(\"T\")[0];\n                        } else if (field.autoOption === \"USERNAME\") {\n                            autoFilledValue = (userData === null || userData === void 0 ? void 0 : userData.username) || \"\";\n                        }\n                    }\n                    return {\n                        id: field.id,\n                        name: field.name,\n                        type: field.type,\n                        autoOption: field.autoOption,\n                        value: autoFilledValue\n                    };\n                });\n            }\n            return customFieldsData;\n        } catch (error) {\n            return [];\n        }\n    }, [\n        userData\n    ]);\n    const { fields, append, remove } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useFieldArray)({\n        control: form.control,\n        name: \"entries\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        companyFieldRefs.current = companyFieldRefs.current.slice(0, fields.length);\n    }, [\n        fields.length\n    ]);\n    const generateFilename = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((entryIndex, formValues)=>{\n        try {\n            const entry = formValues.entries[entryIndex];\n            if (!entry) return {\n                filename: \"\",\n                isValid: false,\n                missing: [\n                    \"Entry data\"\n                ]\n            };\n            const missing = [];\n            const selectedAssociate = associate === null || associate === void 0 ? void 0 : associate.find((a)=>{\n                var _a_id;\n                return ((_a_id = a.id) === null || _a_id === void 0 ? void 0 : _a_id.toString()) === formValues.associateId;\n            });\n            const associateName = (selectedAssociate === null || selectedAssociate === void 0 ? void 0 : selectedAssociate.name) || \"\";\n            if (!associateName) {\n                missing.push(\"Associate\");\n            }\n            const entryClientId = entry.clientId || formValues.clientId;\n            const selectedClient = client === null || client === void 0 ? void 0 : client.find((c)=>{\n                var _c_id;\n                return ((_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString()) === entryClientId;\n            });\n            const clientName = (selectedClient === null || selectedClient === void 0 ? void 0 : selectedClient.client_name) || \"\";\n            if (!clientName) {\n                missing.push(\"Client\");\n            }\n            let carrierName = \"\";\n            if (entry.carrierName) {\n                const carrierOption = carrier === null || carrier === void 0 ? void 0 : carrier.find((c)=>{\n                    var _c_id;\n                    return ((_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString()) === entry.carrierName;\n                });\n                carrierName = (carrierOption === null || carrierOption === void 0 ? void 0 : carrierOption.name) || \"\";\n            }\n            if (!carrierName) {\n                missing.push(\"Carrier\");\n            }\n            const receivedDate = entry.receivedDate;\n            const invoiceDate = entry.invoiceDate;\n            const currentDate = new Date();\n            const year = currentDate.getFullYear().toString();\n            const month = currentDate.toLocaleString(\"default\", {\n                month: \"short\"\n            }).toUpperCase();\n            if (!invoiceDate) {\n                missing.push(\"Invoice Date\");\n            }\n            let receivedDateStr = \"\";\n            if (receivedDate) {\n                const date = new Date(receivedDate);\n                receivedDateStr = date.toISOString().split(\"T\")[0];\n            } else {\n                missing.push(\"Received Date\");\n            }\n            const ftpFileName = entry.ftpFileName || \"\";\n            const baseFilename = ftpFileName ? ftpFileName.endsWith(\".pdf\") ? ftpFileName : \"\".concat(ftpFileName, \".pdf\") : \"\";\n            if (!baseFilename) {\n                missing.push(\"FTP File Name\");\n            }\n            const isValid = missing.length === 0;\n            const filename = isValid ? \"/\".concat(associateName, \"/\").concat(clientName, \"/CARRIERINVOICES/\").concat(carrierName, \"/\").concat(year, \"/\").concat(month, \"/\").concat(receivedDateStr, \"/\").concat(baseFilename) : \"\";\n            return {\n                filename,\n                isValid,\n                missing\n            };\n        } catch (error) {\n            return {\n                filename: \"\",\n                isValid: false,\n                missing: [\n                    \"Error generating filename\"\n                ]\n            };\n        }\n    }, [\n        client,\n        carrier,\n        associate\n    ]);\n    const handleCompanyAutoPopulation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((entryIndex, entryClientId)=>{\n        var _clientOptions_find;\n        const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n        const currentEntry = form.getValues(\"entries.\".concat(entryIndex));\n        if (entryClientName && entryClientName !== \"LEGRAND\") {\n            form.setValue(\"entries.\".concat(entryIndex, \".company\"), entryClientName);\n        } else if (entryClientName === \"LEGRAND\") {\n            const shipperAlias = currentEntry.shipperAlias;\n            const consigneeAlias = currentEntry.consigneeAlias;\n            const billtoAlias = currentEntry.billtoAlias;\n            const hasAnyLegrandData = shipperAlias || consigneeAlias || billtoAlias;\n            if (!hasAnyLegrandData && currentEntry.company !== \"\") {\n                form.setValue(\"entries.\".concat(entryIndex, \".company\"), \"\");\n            }\n        } else {\n            if (currentEntry.company !== \"\") {\n                form.setValue(\"entries.\".concat(entryIndex, \".company\"), \"\");\n            }\n        }\n    }, [\n        form,\n        clientOptions\n    ]);\n    const handleCustomFieldsFetch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (entryIndex, entryClientId)=>{\n        var _currentCustomFields_, _currentCustomFields_1;\n        if (!entryClientId) {\n            const currentCustomFields = form.getValues(\"entries.\".concat(entryIndex, \".customFields\"));\n            if (currentCustomFields && currentCustomFields.length > 0) {\n                form.setValue(\"entries.\".concat(entryIndex, \".customFields\"), []);\n            }\n            return;\n        }\n        const currentCustomFields = form.getValues(\"entries.\".concat(entryIndex, \".customFields\")) || [];\n        const hasEmptyAutoUsernameFields = currentCustomFields.some((field)=>field.type === \"AUTO\" && field.autoOption === \"USERNAME\" && !field.value && (userData === null || userData === void 0 ? void 0 : userData.username));\n        const shouldFetchCustomFields = currentCustomFields.length === 0 || currentCustomFields.length > 0 && !((_currentCustomFields_ = currentCustomFields[0]) === null || _currentCustomFields_ === void 0 ? void 0 : _currentCustomFields_.clientId) || ((_currentCustomFields_1 = currentCustomFields[0]) === null || _currentCustomFields_1 === void 0 ? void 0 : _currentCustomFields_1.clientId) !== entryClientId || hasEmptyAutoUsernameFields;\n        if (shouldFetchCustomFields) {\n            const customFieldsData = await fetchCustomFieldsForClient(entryClientId);\n            const fieldsWithClientId = customFieldsData.map((field)=>({\n                    ...field,\n                    clientId: entryClientId\n                }));\n            form.setValue(\"entries.\".concat(entryIndex, \".customFields\"), fieldsWithClientId);\n            setTimeout(()=>{\n                fieldsWithClientId.forEach((field, fieldIndex)=>{\n                    const fieldPath = \"entries.\".concat(entryIndex, \".customFields.\").concat(fieldIndex, \".value\");\n                    if (field.value) {\n                        form.setValue(fieldPath, field.value);\n                    }\n                });\n                setCustomFieldsRefresh((prev)=>prev + 1);\n            }, 100);\n        }\n    }, [\n        form,\n        fetchCustomFieldsForClient,\n        userData === null || userData === void 0 ? void 0 : userData.username\n    ]);\n    const updateFilenames = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const formValues = form.getValues();\n        const newFilenames = [];\n        const newValidation = [];\n        const newMissingFields = [];\n        if (formValues.entries && Array.isArray(formValues.entries)) {\n            formValues.entries.forEach((_, index)=>{\n                const { filename, isValid, missing } = generateFilename(index, formValues);\n                newFilenames[index] = filename;\n                newValidation[index] = isValid;\n                newMissingFields[index] = missing || [];\n            });\n        }\n        setGeneratedFilenames(newFilenames);\n        setFilenameValidation(newValidation);\n        setMissingFields(newMissingFields);\n    }, [\n        form,\n        generateFilename\n    ]);\n    const handleInitialSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((associateId, clientId)=>{\n        form.setValue(\"associateId\", associateId);\n        form.setValue(\"clientId\", clientId);\n        setTimeout(()=>{\n            handleCompanyAutoPopulation(0, clientId);\n            handleCustomFieldsFetch(0, clientId);\n            updateFilenames();\n        }, 50);\n        setShowFullForm(true);\n    }, [\n        form,\n        handleCompanyAutoPopulation,\n        handleCustomFieldsFetch,\n        updateFilenames,\n        setShowFullForm\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        setTimeout(()=>{\n            updateFilenames();\n            const formValues = form.getValues();\n            if (formValues.entries && Array.isArray(formValues.entries)) {\n                formValues.entries.forEach((entry, index)=>{\n                    const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || (index === 0 ? formValues.clientId : \"\");\n                    if (entryClientId) {\n                        handleCustomFieldsFetch(index, entryClientId);\n                    }\n                });\n            }\n        }, 50);\n    }, [\n        updateFilenames,\n        handleCustomFieldsFetch,\n        form\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const subscription = form.watch((_, param)=>{\n            let { name } = param;\n            if (name && (name.includes(\"associateId\") || name.includes(\"clientId\") || name.includes(\"carrierName\") || name.includes(\"invoiceDate\") || name.includes(\"receivedDate\") || name.includes(\"ftpFileName\") || name.includes(\"company\") || name.includes(\"division\"))) {\n                updateFilenames();\n            }\n        });\n        return ()=>subscription.unsubscribe();\n    }, [\n        form,\n        updateFilenames\n    ]);\n    const onSubmit = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (values)=>{\n        try {\n            const currentFormValues = form.getValues();\n            const currentValidation = [];\n            const currentMissingFields = [];\n            const currentFilenames = [];\n            if (currentFormValues.entries && Array.isArray(currentFormValues.entries)) {\n                currentFormValues.entries.forEach((_, index)=>{\n                    const { filename, isValid, missing } = generateFilename(index, currentFormValues);\n                    currentValidation[index] = isValid;\n                    currentMissingFields[index] = missing || [];\n                    currentFilenames[index] = filename;\n                });\n            }\n            const allFilenamesValid = currentValidation.every((isValid)=>isValid);\n            if (!allFilenamesValid) {\n                const invalidEntries = currentValidation.map((isValid, index)=>({\n                        index,\n                        isValid,\n                        missing: currentMissingFields[index]\n                    })).filter((entry)=>!entry.isValid);\n                const errorDetails = invalidEntries.map((entry)=>\"Entry \".concat(entry.index + 1, \": \").concat(entry.missing.join(\", \"))).join(\" | \");\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Cannot submit: Missing fields - \".concat(errorDetails));\n                return;\n            }\n            const entries = values.entries.map((entry, index)=>{\n                var _entry_customFields;\n                return {\n                    company: entry.company,\n                    division: entry.division,\n                    invoice: entry.invoice,\n                    masterInvoice: entry.masterInvoice,\n                    bol: entry.bol,\n                    invoiceDate: entry.invoiceDate,\n                    receivedDate: entry.receivedDate,\n                    shipmentDate: entry.shipmentDate,\n                    carrierId: entry.carrierName,\n                    invoiceStatus: entry.invoiceStatus,\n                    manualMatching: entry.manualMatching,\n                    invoiceType: entry.invoiceType,\n                    currency: entry.currency,\n                    qtyShipped: entry.qtyShipped,\n                    weightUnitName: entry.weightUnitName,\n                    quantityBilledText: entry.quantityBilledText,\n                    invoiceTotal: entry.invoiceTotal,\n                    savings: entry.savings,\n                    ftpFileName: entry.ftpFileName,\n                    ftpPage: entry.ftpPage,\n                    docAvailable: entry.docAvailable,\n                    notes: entry.notes,\n                    mistake: entry.mistake,\n                    filePath: generatedFilenames[index],\n                    customFields: (_entry_customFields = entry.customFields) === null || _entry_customFields === void 0 ? void 0 : _entry_customFields.map((cf)=>({\n                            id: cf.id,\n                            value: cf.value\n                        }))\n                };\n            });\n            const formData = {\n                clientId: values.clientId,\n                entries: entries\n            };\n            const result = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_7__.formSubmit)(_lib_routePath__WEBPACK_IMPORTED_MODULE_8__.trackSheets_routes.CREATE_TRACK_SHEETS, \"POST\", formData);\n            if (result.success) {\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.success(\"All TrackSheets created successfully\");\n                form.reset();\n                setTimeout(()=>{\n                    handleInitialSelection(initialAssociateId, initialClientId);\n                }, 100);\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(result.message || \"Failed to create TrackSheets\");\n            }\n            router.refresh();\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"An error occurred while creating the TrackSheets\");\n        }\n    }, [\n        form,\n        router,\n        generateFilename,\n        initialAssociateId,\n        initialClientId,\n        handleInitialSelection,\n        generatedFilenames\n    ]);\n    const addNewEntry = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const newIndex = fields.length;\n        append({\n            clientId: initialClientId,\n            company: \"\",\n            division: \"\",\n            invoice: \"\",\n            masterInvoice: \"\",\n            bol: \"\",\n            invoiceDate: new Date().toISOString().split(\"T\")[0],\n            receivedDate: new Date().toISOString().split(\"T\")[0],\n            shipmentDate: new Date().toISOString().split(\"T\")[0],\n            carrierName: \"\",\n            invoiceStatus: \"\",\n            manualMatching: \"\",\n            invoiceType: \"\",\n            currency: \"\",\n            qtyShipped: \"\",\n            weightUnitName: \"\",\n            quantityBilledText: \"\",\n            invoiceTotal: \"\",\n            savings: \"\",\n            ftpFileName: \"\",\n            ftpPage: \"\",\n            docAvailable: [],\n            notes: \"\",\n            mistake: \"\",\n            legrandAlias: \"\",\n            legrandCompanyName: \"\",\n            legrandAddress: \"\",\n            legrandZipcode: \"\",\n            shipperAlias: \"\",\n            shipperAddress: \"\",\n            shipperZipcode: \"\",\n            consigneeAlias: \"\",\n            consigneeAddress: \"\",\n            consigneeZipcode: \"\",\n            billtoAlias: \"\",\n            billtoAddress: \"\",\n            billtoZipcode: \"\",\n            customFields: []\n        });\n        setTimeout(()=>{\n            handleCompanyAutoPopulation(newIndex, initialClientId);\n            handleCustomFieldsFetch(newIndex, initialClientId);\n            if (companyFieldRefs.current[newIndex]) {\n                var _companyFieldRefs_current_newIndex, _companyFieldRefs_current_newIndex1, _companyFieldRefs_current_newIndex2;\n                const inputElement = ((_companyFieldRefs_current_newIndex = companyFieldRefs.current[newIndex]) === null || _companyFieldRefs_current_newIndex === void 0 ? void 0 : _companyFieldRefs_current_newIndex.querySelector(\"input\")) || ((_companyFieldRefs_current_newIndex1 = companyFieldRefs.current[newIndex]) === null || _companyFieldRefs_current_newIndex1 === void 0 ? void 0 : _companyFieldRefs_current_newIndex1.querySelector(\"button\")) || ((_companyFieldRefs_current_newIndex2 = companyFieldRefs.current[newIndex]) === null || _companyFieldRefs_current_newIndex2 === void 0 ? void 0 : _companyFieldRefs_current_newIndex2.querySelector(\"select\"));\n                if (inputElement) {\n                    inputElement.focus();\n                    try {\n                        inputElement.click();\n                    } catch (e) {}\n                }\n            }\n            updateFilenames();\n        }, 200);\n    }, [\n        append,\n        fields.length,\n        updateFilenames,\n        initialClientId,\n        handleCompanyAutoPopulation,\n        handleCustomFieldsFetch\n    ]);\n    const handleFormKeyDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        if (e.ctrlKey && (e.key === \"s\" || e.key === \"S\")) {\n            e.preventDefault();\n            form.handleSubmit(onSubmit)();\n        } else if (e.shiftKey && e.key === \"Enter\") {\n            e.preventDefault();\n            addNewEntry();\n        } else if (e.key === \"Enter\" && !e.ctrlKey && !e.shiftKey && !e.altKey) {\n            const activeElement = document.activeElement;\n            const isSubmitButton = (activeElement === null || activeElement === void 0 ? void 0 : activeElement.getAttribute(\"type\")) === \"submit\";\n            if (isSubmitButton) {\n                e.preventDefault();\n                form.handleSubmit(onSubmit)();\n            }\n        }\n    }, [\n        form,\n        onSubmit,\n        addNewEntry\n    ]);\n    const removeEntry = (index)=>{\n        if (fields.length > 1) {\n            remove(index);\n        } else {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"You must have at least one entry\");\n        }\n    };\n    const getFilteredDivisionOptions = (company, entryIndex)=>{\n        if (!company || !legrandData.length) {\n            return [];\n        }\n        if (entryIndex !== undefined) {\n            var _formValues_entries, _clientOptions_find;\n            const formValues = form.getValues();\n            const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[entryIndex];\n            const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || (entryIndex === 0 ? formValues.clientId : \"\");\n            const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n            if (entryClientName === \"LEGRAND\") {\n                const shipperAlias = form.getValues(\"entries.\".concat(entryIndex, \".shipperAlias\"));\n                const consigneeAlias = form.getValues(\"entries.\".concat(entryIndex, \".consigneeAlias\"));\n                const billtoAlias = form.getValues(\"entries.\".concat(entryIndex, \".billtoAlias\"));\n                const currentAlias = shipperAlias || consigneeAlias || billtoAlias;\n                if (currentAlias) {\n                    const selectedData = legrandData.find((data)=>{\n                        const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n                        return uniqueKey === currentAlias;\n                    });\n                    if (selectedData) {\n                        const baseAliasName = selectedData.aliasShippingNames && selectedData.aliasShippingNames !== \"NONE\" ? selectedData.aliasShippingNames : selectedData.legalName;\n                        const sameAliasEntries = legrandData.filter((data)=>{\n                            const dataAliasName = data.aliasShippingNames && data.aliasShippingNames !== \"NONE\" ? data.aliasShippingNames : data.legalName;\n                            return dataAliasName === baseAliasName;\n                        });\n                        const allDivisions = [];\n                        sameAliasEntries.forEach((entry)=>{\n                            if (entry.customeCode) {\n                                if (entry.customeCode.includes(\"/\")) {\n                                    const splitDivisions = entry.customeCode.split(\"/\").map((d)=>d.trim());\n                                    allDivisions.push(...splitDivisions);\n                                } else {\n                                    allDivisions.push(entry.customeCode);\n                                }\n                            }\n                        });\n                        const uniqueDivisions = Array.from(new Set(allDivisions.filter((code)=>code)));\n                        if (uniqueDivisions.length > 1) {\n                            const contextDivisions = uniqueDivisions.sort().map((code)=>({\n                                    value: code,\n                                    label: code\n                                }));\n                            return contextDivisions;\n                        } else {}\n                    }\n                }\n            }\n        }\n        const allDivisions = [];\n        legrandData.filter((data)=>data.businessUnit === company && data.customeCode).forEach((data)=>{\n            if (data.customeCode.includes(\"/\")) {\n                const splitDivisions = data.customeCode.split(\"/\").map((d)=>d.trim());\n                allDivisions.push(...splitDivisions);\n            } else {\n                allDivisions.push(data.customeCode);\n            }\n        });\n        const divisions = Array.from(new Set(allDivisions.filter((code)=>code))).sort().map((code)=>({\n                value: code,\n                label: code\n            }));\n        return divisions;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full px-2 py-3\",\n                children: showFullForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_3__.Form, {\n                    ...form,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: form.handleSubmit(onSubmit),\n                        onKeyDown: handleFormKeyDown,\n                        className: \"space-y-3\",\n                        children: [\n                            fields.map((field, index)=>{\n                                var _missingFields_index;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2 bg-gray-100 rounded-md px-3 py-2 border border-gray-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-5 h-5 bg-gray-600 rounded-full flex items-center justify-center text-white font-semibold text-xs\",\n                                                        children: index + 1\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 989,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-sm font-semibold text-gray-900\",\n                                                        children: [\n                                                            \"Entry #\",\n                                                            index + 1\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 992,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                lineNumber: 988,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                            lineNumber: 987,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-3 pb-3 border-b border-gray-100\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-50 rounded-md p-2 mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1005,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-sm font-semibold text-gray-900\",\n                                                                        children: \"Client Information\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1006,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1004,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 mb-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                            className: \"mt-2\",\n                                                                            form: form,\n                                                                            label: \"FTP File Name\",\n                                                                            name: \"entries.\".concat(index, \".ftpFileName\"),\n                                                                            type: \"text\",\n                                                                            isRequired: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1013,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1012,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_PageInput__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"mt-2\",\n                                                                        form: form,\n                                                                        label: \"FTP Page\",\n                                                                        name: \"entries.\".concat(index, \".ftpPage\"),\n                                                                        isRequired: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1022,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1029,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    \" \"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1011,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            (()=>{\n                                                                var _formValues_entries, _clientOptions_find;\n                                                                const formValues = form.getValues();\n                                                                const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                return entryClientName === \"LEGRAND\";\n                                                            })() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-3 mb-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            form: form,\n                                                                            entryIndex: index,\n                                                                            onLegrandDataChange: handleLegrandDataChange,\n                                                                            blockTitle: \"Shipper\",\n                                                                            fieldPrefix: \"shipper\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1046,\n                                                                            columnNumber: 33\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            form: form,\n                                                                            entryIndex: index,\n                                                                            onLegrandDataChange: handleLegrandDataChange,\n                                                                            blockTitle: \"Consignee\",\n                                                                            fieldPrefix: \"consignee\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1053,\n                                                                            columnNumber: 33\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            form: form,\n                                                                            entryIndex: index,\n                                                                            onLegrandDataChange: handleLegrandDataChange,\n                                                                            blockTitle: \"Bill-to\",\n                                                                            fieldPrefix: \"billto\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1060,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1045,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1044,\n                                                                columnNumber: 29\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        ref: (el)=>{\n                                                                            companyFieldRefs.current[index] = el;\n                                                                        },\n                                                                        className: \"flex flex-col mb-1 [&_input]:h-10\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                            form: form,\n                                                                            label: \"Company\",\n                                                                            name: \"entries.\".concat(index, \".company\"),\n                                                                            type: \"text\",\n                                                                            disable: (()=>{\n                                                                                var _formValues_entries, _clientOptions_find;\n                                                                                const formValues = form.getValues();\n                                                                                const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                                const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                                const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                                return entryClientName === \"LEGRAND\";\n                                                                            })()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1079,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1073,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex flex-col\",\n                                                                        children: (()=>{\n                                                                            var _formValues_entries, _clientOptions_find, _entries_index;\n                                                                            const formValues = form.getValues();\n                                                                            const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                            const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                            const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                            const isLegrand = entryClientName === \"LEGRAND\";\n                                                                            return isLegrand ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                form: form,\n                                                                                name: \"entries.\".concat(index, \".division\"),\n                                                                                label: \"Division\",\n                                                                                placeholder: \"Search Division\",\n                                                                                disabled: false,\n                                                                                options: getFilteredDivisionOptions((entries === null || entries === void 0 ? void 0 : (_entries_index = entries[index]) === null || _entries_index === void 0 ? void 0 : _entries_index.company) || \"\", index)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1110,\n                                                                                columnNumber: 35\n                                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                form: form,\n                                                                                label: \"Division\",\n                                                                                name: \"entries.\".concat(index, \".division\"),\n                                                                                type: \"text\",\n                                                                                placeholder: \"Enter Division\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1122,\n                                                                                columnNumber: 35\n                                                                            }, undefined);\n                                                                        })()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1098,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex flex-col\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"mt-0\",\n                                                                            form: form,\n                                                                            name: \"entries.\".concat(index, \".carrierName\"),\n                                                                            label: \"Select Carrier\",\n                                                                            placeholder: \"Search Carrier\",\n                                                                            isRequired: true,\n                                                                            options: (carrierOptions === null || carrierOptions === void 0 ? void 0 : carrierOptions.filter((carrier)=>{\n                                                                                const currentEntries = form.getValues(\"entries\") || [];\n                                                                                const isSelectedInOtherEntries = currentEntries.some((entry, entryIndex)=>entryIndex !== index && entry.carrierName === carrier.value);\n                                                                                return !isSelectedInOtherEntries;\n                                                                            })) || [],\n                                                                            onValueChange: ()=>{\n                                                                                setTimeout(()=>updateFilenames(), 100);\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1133,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1132,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1072,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1003,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                    lineNumber: 1001,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-3 pb-3 border-b border-gray-100\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-orange-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1164,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-sm font-semibold text-gray-900\",\n                                                                    children: \"Document Information\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1165,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1163,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Master Invoice\",\n                                                                    name: \"entries.\".concat(index, \".masterInvoice\"),\n                                                                    type: \"text\",\n                                                                    onBlur: (e)=>{\n                                                                        const masterInvoiceValue = e.target.value;\n                                                                        if (masterInvoiceValue) {\n                                                                            form.setValue(\"entries.\".concat(index, \".invoice\"), masterInvoiceValue);\n                                                                        }\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1170,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Invoice\",\n                                                                    name: \"entries.\".concat(index, \".invoice\"),\n                                                                    type: \"text\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1185,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"BOL\",\n                                                                    name: \"entries.\".concat(index, \".bol\"),\n                                                                    type: \"text\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1192,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1169,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Received Date\",\n                                                                    name: \"entries.\".concat(index, \".receivedDate\"),\n                                                                    type: \"date\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1201,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Invoice Date\",\n                                                                    name: \"entries.\".concat(index, \".invoiceDate\"),\n                                                                    type: \"date\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1208,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Shipment Date\",\n                                                                    name: \"entries.\".concat(index, \".shipmentDate\"),\n                                                                    type: \"date\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1215,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1200,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                    lineNumber: 1162,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4 pb-4 border-b border-gray-100\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1228,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-sm font-semibold text-gray-900\",\n                                                                    children: \"Financial & Shipment\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1229,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1227,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Invoice Total\",\n                                                                    name: \"entries.\".concat(index, \".invoiceTotal\"),\n                                                                    type: \"number\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1234,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    form: form,\n                                                                    name: \"entries.\".concat(index, \".currency\"),\n                                                                    label: \"Currency\",\n                                                                    placeholder: \"Search currency\",\n                                                                    isRequired: true,\n                                                                    options: [\n                                                                        {\n                                                                            value: \"USD\",\n                                                                            label: \"USD\"\n                                                                        },\n                                                                        {\n                                                                            value: \"CAD\",\n                                                                            label: \"CAD\"\n                                                                        },\n                                                                        {\n                                                                            value: \"EUR\",\n                                                                            label: \"EUR\"\n                                                                        }\n                                                                    ]\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1241,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Quantity Shipped\",\n                                                                    name: \"entries.\".concat(index, \".qtyShipped\"),\n                                                                    type: \"number\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1253,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Weight Unit\",\n                                                                    name: \"entries.\".concat(index, \".weightUnitName\"),\n                                                                    type: \"text\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1260,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1233,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 mt-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Savings\",\n                                                                    name: \"entries.\".concat(index, \".savings\"),\n                                                                    type: \"text\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1269,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Invoice Type\",\n                                                                    name: \"entries.\".concat(index, \".invoiceType\"),\n                                                                    type: \"text\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1275,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Quantity Billed Text\",\n                                                                    name: \"entries.\".concat(index, \".quantityBilledText\"),\n                                                                    type: \"text\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1282,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Invoice Status\",\n                                                                    name: \"entries.\".concat(index, \".invoiceStatus\"),\n                                                                    type: \"text\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1288,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1268,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                    lineNumber: 1226,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-gray-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1301,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-sm font-semibold text-gray-900\",\n                                                                    children: \"Additional Information\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1302,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1300,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Manual Matching\",\n                                                                    name: \"entries.\".concat(index, \".manualMatching\"),\n                                                                    type: \"text\",\n                                                                    isRequired: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1307,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Notes\",\n                                                                    name: \"entries.\".concat(index, \".notes\"),\n                                                                    type: \"text\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1314,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    form: form,\n                                                                    label: \"Mistake\",\n                                                                    name: \"entries.\".concat(index, \".mistake\"),\n                                                                    type: \"text\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1320,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormCheckboxGroup__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Documents Available\",\n                                                                        name: \"entries.\".concat(index, \".docAvailable\"),\n                                                                        options: [\n                                                                            {\n                                                                                label: \"Invoice\",\n                                                                                value: \"Invoice\"\n                                                                            },\n                                                                            {\n                                                                                label: \"BOL\",\n                                                                                value: \"Bol\"\n                                                                            },\n                                                                            {\n                                                                                label: \"POD\",\n                                                                                value: \"Pod\"\n                                                                            },\n                                                                            {\n                                                                                label: \"Packages List\",\n                                                                                value: \"Packages List\"\n                                                                            }\n                                                                        ],\n                                                                        className: \"flex-row gap-2 text-xs\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1327,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1326,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1306,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                    lineNumber: 1299,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                (()=>{\n                                                    var _formValues_entries;\n                                                    const formValues = form.getValues();\n                                                    const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                    const customFields = (entry === null || entry === void 0 ? void 0 : entry.customFields) || [];\n                                                    return Array.isArray(customFields) && customFields.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"pt-3 border-t border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-purple-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1359,\n                                                                        columnNumber: 31\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-sm font-semibold text-gray-900\",\n                                                                        children: [\n                                                                            \"Custom Fields (\",\n                                                                            customFields.length,\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1360,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1358,\n                                                                columnNumber: 29\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2\",\n                                                                children: customFields.map((cf, cfIdx)=>{\n                                                                    const fieldType = cf.type || \"TEXT\";\n                                                                    const isAutoField = fieldType === \"AUTO\";\n                                                                    const autoOption = cf.autoOption;\n                                                                    let inputType = \"text\";\n                                                                    if (fieldType === \"DATE\" || isAutoField && autoOption === \"DATE\") {\n                                                                        inputType = \"date\";\n                                                                    } else if (fieldType === \"NUMBER\") {\n                                                                        inputType = \"number\";\n                                                                    }\n                                                                    const fieldLabel = isAutoField ? \"\".concat(cf.name, \" (Auto - \").concat(autoOption, \")\") : cf.name;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: fieldLabel,\n                                                                        name: \"entries.\".concat(index, \".customFields.\").concat(cfIdx, \".value\"),\n                                                                        type: inputType,\n                                                                        className: \"w-full\",\n                                                                        disable: isAutoField\n                                                                    }, cf.id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1385,\n                                                                        columnNumber: 35\n                                                                    }, undefined);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1364,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        ]\n                                                    }, \"custom-fields-\".concat(index, \"-\").concat(customFieldsRefresh), true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1354,\n                                                        columnNumber: 27\n                                                    }, undefined) : null;\n                                                })(),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"pt-3 border-t border-gray-100 mt-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-end space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.Tooltip, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipTrigger, {\n                                                                        asChild: true,\n                                                                        tabIndex: -1,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-5 h-5 rounded-full flex items-center justify-center text-white font-bold text-xs cursor-help transition-colors duration-200 \".concat(filenameValidation[index] ? \"bg-green-500 hover:bg-green-600\" : \"bg-orange-500 hover:bg-orange-600\"),\n                                                                            tabIndex: -1,\n                                                                            role: \"button\",\n                                                                            \"aria-label\": \"Entry \".concat(index + 1, \" filename status\"),\n                                                                            children: \"!\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1407,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1406,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipContent, {\n                                                                        side: \"top\",\n                                                                        align: \"center\",\n                                                                        className: \"z-[9999]\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm max-w-md\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium mb-1\",\n                                                                                    children: [\n                                                                                        \"Entry #\",\n                                                                                        index + 1,\n                                                                                        \" Filename\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1426,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                filenameValidation[index] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"font-medium text-green-600 mb-2\",\n                                                                                            children: \"Filename Generated\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1431,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs font-mono break-all bg-gray-100 p-2 rounded text-black\",\n                                                                                            children: generatedFilenames[index]\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1434,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1430,\n                                                                                    columnNumber: 35\n                                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"font-medium text-orange-600 mb-1\",\n                                                                                            children: \"Please fill the form to generate filename\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1440,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-xs text-gray-600 mb-2\",\n                                                                                            children: \"Missing fields:\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1443,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                                            className: \"list-disc list-inside space-y-1\",\n                                                                                            children: (_missingFields_index = missingFields[index]) === null || _missingFields_index === void 0 ? void 0 : _missingFields_index.map((field, fieldIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                                    className: \"text-xs\",\n                                                                                                    children: field\n                                                                                                }, fieldIndex, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                                    lineNumber: 1449,\n                                                                                                    columnNumber: 43\n                                                                                                }, undefined))\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1446,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1439,\n                                                                                    columnNumber: 35\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1425,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1420,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1405,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                type: \"button\",\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                className: \"h-7 w-7 p-0 hover:bg-red-50 hover:border-red-200\",\n                                                                onClick: ()=>removeEntry(index),\n                                                                disabled: fields.length <= 1,\n                                                                tabIndex: -1,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"h-3 w-3 text-red-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1473,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1464,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            index === fields.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.Tooltip, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipTrigger, {\n                                                                        asChild: true,\n                                                                        tabIndex: -1,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                            type: \"button\",\n                                                                            variant: \"outline\",\n                                                                            size: \"sm\",\n                                                                            className: \"h-7 w-7 p-0 hover:bg-green-50 hover:border-green-200\",\n                                                                            onClick: addNewEntry,\n                                                                            tabIndex: -1,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                className: \"h-3 w-3 text-green-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1486,\n                                                                                columnNumber: 35\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1478,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1477,\n                                                                        columnNumber: 31\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipContent, {\n                                                                        side: \"top\",\n                                                                        align: \"center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs\",\n                                                                            children: \"Add New Entry (Shift+Enter)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1490,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1489,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1476,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1403,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                                    lineNumber: 1402,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                            lineNumber: 999,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, field.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                    lineNumber: 985,\n                                    columnNumber: 19\n                                }, undefined);\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        type: \"submit\",\n                                        className: \"px-6 py-2 rounded-lg font-medium transition-all duration-200 shadow-md hover:shadow-lg text-sm\",\n                                        children: \"Save\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                        lineNumber: 1505,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                    lineNumber: 1504,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                                lineNumber: 1503,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                        lineNumber: 979,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                    lineNumber: 978,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n                lineNumber: 973,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n            lineNumber: 972,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\createTrackSheet.tsx\",\n        lineNumber: 971,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CreateTrackSheet, \"EGWYEKpsS82wG+3VGNaB7FHZtJs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useWatch,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useFieldArray\n    ];\n});\n_c = CreateTrackSheet;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CreateTrackSheet);\nvar _c;\n$RefreshReg$(_c, \"CreateTrackSheet\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/trackSheets/v2/createTrackSheet.tsx\n"));

/***/ })

});