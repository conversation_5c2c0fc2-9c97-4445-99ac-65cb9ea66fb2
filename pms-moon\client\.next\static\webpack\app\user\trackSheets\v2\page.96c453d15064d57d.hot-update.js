"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/trackSheets/v2/page",{

/***/ "(app-pages-browser)/./app/user/trackSheets/v2/ManageTrackSheet.tsx":
/*!******************************************************!*\
  !*** ./app/user/trackSheets/v2/ManageTrackSheet.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _createTrackSheet__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./createTrackSheet */ \"(app-pages-browser)/./app/user/trackSheets/v2/createTrackSheet.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _TrackSheetContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./TrackSheetContext */ \"(app-pages-browser)/./app/user/trackSheets/v2/TrackSheetContext.tsx\");\n/* harmony import */ var _ClientSelectPage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ClientSelectPage */ \"(app-pages-browser)/./app/user/trackSheets/v2/ClientSelectPage.tsx\");\n/* harmony import */ var _components_sidebar_Sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/sidebar/Sidebar */ \"(app-pages-browser)/./components/sidebar/Sidebar.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./components/ui/sidebar.tsx\");\n/* harmony import */ var _app_component_BreadCrumbs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/_component/BreadCrumbs */ \"(app-pages-browser)/./app/_component/BreadCrumbs.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Building2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/_component/SearchSelect */ \"(app-pages-browser)/./app/_component/SearchSelect.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst ManageWorkSheet = (param)=>{\n    let { permissions, client, carrier, associate, userData, actions } = param;\n    _s();\n    const [filterdata, setFilterData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [deleteData, setDeletedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [recievedFDate, setRecievedFDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [recievedTDate, setRecievedTDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [invoiceFDate, setInvoiceFDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [invoiceTDate, setInvoiceTDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [shipmentFDate, setShipmentFDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [shipmentTDate, setShipmentTDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [activeView, setActiveView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"view\");\n    const [initialAssociateId, setInitialAssociateId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [initialClientId, setInitialClientId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showFullForm, setShowFullForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm)({\n        defaultValues: {\n            associateId: \"\",\n            clientId: \"\"\n        }\n    });\n    const validateClientForAssociate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((associateId, currentClientId)=>{\n        if (associateId && currentClientId) {\n            var _currentClient_associateId;\n            const currentClient = client === null || client === void 0 ? void 0 : client.find((c)=>{\n                var _c_id;\n                return ((_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString()) === currentClientId;\n            });\n            if (currentClient && ((_currentClient_associateId = currentClient.associateId) === null || _currentClient_associateId === void 0 ? void 0 : _currentClient_associateId.toString()) !== associateId) {\n                form.setValue(\"clientId\", \"\");\n                setInitialClientId(\"\");\n                return false;\n            }\n        }\n        return true;\n    }, [\n        client,\n        form\n    ]);\n    const clearEntrySpecificClients = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const currentEntries = form.getValues(\"entries\") || [];\n        if (currentEntries.length > 0) {\n            const hasEntrySpecificClients = Array.isArray(currentEntries) && currentEntries.some((entry)=>entry.clientId);\n            if (hasEntrySpecificClients) {\n                const updatedEntries = currentEntries.map((entry)=>({\n                        ...entry,\n                        clientId: \"\"\n                    }));\n                form.setValue(\"entries\", updatedEntries);\n            }\n        }\n    }, [\n        form\n    ]);\n    const handleInitialSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((associateId, clientId)=>{\n        const finalAssociateId = associateId || initialAssociateId;\n        const finalClientId = clientId || initialClientId;\n        if (!finalAssociateId || !finalClientId) {\n            toast.error(\"Please select both Associate and Client to continue\");\n            return;\n        }\n        form.setValue(\"associateId\", finalAssociateId);\n        form.setValue(\"clientId\", finalClientId);\n        setTimeout(()=>{\n            handleCompanyAutoPopulation(0, finalClientId);\n            handleCustomFieldsFetch(0, finalClientId);\n            updateFilenames();\n            const formValues = form.getValues();\n            if (formValues.entries && Array.isArray(formValues.entries)) {\n                formValues.entries.forEach((entry, index)=>{\n                    if (index > 0) {\n                        const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || finalClientId;\n                        if (entryClientId) {\n                            handleCustomFieldsFetch(index, entryClientId);\n                        }\n                    }\n                });\n            }\n        }, 50);\n        setShowFullForm(true);\n    }, [\n        initialAssociateId,\n        initialClientId,\n        form,\n        handleCompanyAutoPopulation,\n        handleCustomFieldsFetch,\n        updateFilenames\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TrackSheetContext__WEBPACK_IMPORTED_MODULE_4__.TrackSheetContext.Provider, {\n        value: {\n            filterdata,\n            setFilterData,\n            deleteData,\n            setDeletedData,\n            recievedFDate,\n            recievedTDate,\n            setRecievedFDate,\n            setRecievedTDate,\n            invoiceFDate,\n            setInvoiceFDate,\n            invoiceTDate,\n            setInvoiceTDate,\n            shipmentFDate,\n            setShipmentFDate,\n            shipmentTDate,\n            setShipmentTDate\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_7__.SidebarProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex w-full min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sidebar_Sidebar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        permissions: permissions,\n                        profile: userData\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 w-full pl-3 overflow-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_BreadCrumbs__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    breadcrumblist: [\n                                        {\n                                            link: \"/user/trackSheets\",\n                                            name: \"TrackSheet\"\n                                        }\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-4 pl-3 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"default\",\n                                        onClick: ()=>setActiveView(\"view\"),\n                                        className: \"w-40 shadow-md rounded-xl text-base transition-all duration-200 \".concat(activeView === \"view\" ? \"bg-neutral-800 hover:bg-neutral-900 text-white\" : \"bg-white text-neutral-800 border border-neutral-300 hover:bg-neutral-100\"),\n                                        children: \"View TrackSheet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"default\",\n                                        onClick: ()=>setActiveView(\"create\"),\n                                        className: \"w-40 shadow-md rounded-xl text-base transition-all duration-200 \".concat(activeView === \"create\" ? \"bg-neutral-800 hover:bg-neutral-900 text-white\" : \"bg-white text-neutral-800 border border-neutral-300 hover:bg-neutral-100\"),\n                                        children: \"Create TrackSheet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-5 h-5 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 31\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-900\",\n                                                children: \"Create TrackSheet\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 31\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_10__.Form, {\n                                        ...form,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        form: form,\n                                                        name: \"associateId\",\n                                                        label: \"Select Associate\",\n                                                        placeholder: \"Search Associate...\",\n                                                        isRequired: true,\n                                                        options: (associate === null || associate === void 0 ? void 0 : associate.map((a)=>{\n                                                            var _a_id;\n                                                            return {\n                                                                value: (_a_id = a.id) === null || _a_id === void 0 ? void 0 : _a_id.toString(),\n                                                                label: a.name,\n                                                                name: a.name\n                                                            };\n                                                        })) || [],\n                                                        onValueChange: (value)=>{\n                                                            setInitialAssociateId(value);\n                                                            if (value && initialClientId) {\n                                                                validateClientForAssociate(value, initialClientId);\n                                                            } else {\n                                                                setInitialClientId(\"\");\n                                                                form.setValue(\"clientId\", \"\");\n                                                            }\n                                                            setShowFullForm(false);\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        form: form,\n                                                        name: \"clientId\",\n                                                        label: \"Select Client\",\n                                                        placeholder: \"Search Client...\",\n                                                        isRequired: true,\n                                                        disabled: !initialAssociateId,\n                                                        options: client.filter((c)=>{\n                                                            var _c_associate_id, _c_associate;\n                                                            return !initialAssociateId || ((_c_associate = c.associate) === null || _c_associate === void 0 ? void 0 : (_c_associate_id = _c_associate.id) === null || _c_associate_id === void 0 ? void 0 : _c_associate_id.toString()) === initialAssociateId;\n                                                        }).map((c)=>{\n                                                            var _c_id;\n                                                            return {\n                                                                value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n                                                                label: c.client_name,\n                                                                name: c.client_name\n                                                            };\n                                                        }) || [],\n                                                        onValueChange: (value)=>{\n                                                            setInitialClientId(value);\n                                                            if (showFullForm) {\n                                                                clearEntrySpecificClients();\n                                                            }\n                                                            if (value && initialAssociateId) {\n                                                                setTimeout(()=>{\n                                                                    handleInitialSelection(initialAssociateId, value);\n                                                                }, 100);\n                                                            } else {\n                                                                setShowFullForm(false);\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 31\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 27\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full animate-in fade-in duration-500 rounded-2xl shadow-sm  dark:bg-gray-800 p-1\",\n                                children: activeView === \"create\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_createTrackSheet__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    client: client,\n                                    carrier: carrier,\n                                    associate: associate,\n                                    userData: userData\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClientSelectPage__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    permissions: actions,\n                                    client: client\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                lineNumber: 136,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n            lineNumber: 135,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n        lineNumber: 115,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ManageWorkSheet, \"016kC22Ll7LYlShRaLKO3VLgbrs=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm\n    ];\n});\n_c = ManageWorkSheet;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ManageWorkSheet);\nvar _c;\n$RefreshReg$(_c, \"ManageWorkSheet\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/trackSheets/v2/ManageTrackSheet.tsx\n"));

/***/ })

});