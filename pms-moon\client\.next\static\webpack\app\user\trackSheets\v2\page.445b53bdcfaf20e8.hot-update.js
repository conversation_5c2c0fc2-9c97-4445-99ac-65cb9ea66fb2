"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/trackSheets/v2/page",{

/***/ "(app-pages-browser)/./app/user/trackSheets/v2/ManageTrackSheet.tsx":
/*!******************************************************!*\
  !*** ./app/user/trackSheets/v2/ManageTrackSheet.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _createTrackSheet__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./createTrackSheet */ \"(app-pages-browser)/./app/user/trackSheets/v2/createTrackSheet.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _TrackSheetContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./TrackSheetContext */ \"(app-pages-browser)/./app/user/trackSheets/v2/TrackSheetContext.tsx\");\n/* harmony import */ var _ClientSelectPage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ClientSelectPage */ \"(app-pages-browser)/./app/user/trackSheets/v2/ClientSelectPage.tsx\");\n/* harmony import */ var _components_sidebar_Sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/sidebar/Sidebar */ \"(app-pages-browser)/./components/sidebar/Sidebar.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./components/ui/sidebar.tsx\");\n/* harmony import */ var _app_component_BreadCrumbs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/_component/BreadCrumbs */ \"(app-pages-browser)/./app/_component/BreadCrumbs.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Building2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/_component/SearchSelect */ \"(app-pages-browser)/./app/_component/SearchSelect.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst ManageWorkSheet = (param)=>{\n    let { permissions, client, carrier, associate, userData, actions } = param;\n    _s();\n    const [filterdata, setFilterData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [deleteData, setDeletedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [recievedFDate, setRecievedFDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [recievedTDate, setRecievedTDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [invoiceFDate, setInvoiceFDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [invoiceTDate, setInvoiceTDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [shipmentFDate, setShipmentFDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [shipmentTDate, setShipmentTDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [activeView, setActiveView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"view\");\n    const [initialAssociateId, setInitialAssociateId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [initialClientId, setInitialClientId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showFullForm, setShowFullForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm)({\n        defaultValues: {\n            associateId: \"\",\n            clientId: \"\"\n        }\n    });\n    function validateClientForAssociate(value, initialClientId) {\n        throw new Error(\"Function not implemented.\");\n    }\n    const clearEntrySpecificClients = useCallback(()=>{\n        const currentEntries = form.getValues(\"entries\") || [];\n        if (currentEntries.length > 0) {\n            const hasEntrySpecificClients = currentEntries.some((entry)=>entry.clientId);\n            if (hasEntrySpecificClients) {\n                const updatedEntries = currentEntries.map((entry)=>({\n                        ...entry,\n                        clientId: \"\"\n                    }));\n                form.setValue(\"entries\", updatedEntries);\n            }\n        }\n    }, [\n        form\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TrackSheetContext__WEBPACK_IMPORTED_MODULE_4__.TrackSheetContext.Provider, {\n        value: {\n            filterdata,\n            setFilterData,\n            deleteData,\n            setDeletedData,\n            recievedFDate,\n            recievedTDate,\n            setRecievedFDate,\n            setRecievedTDate,\n            invoiceFDate,\n            setInvoiceFDate,\n            invoiceTDate,\n            setInvoiceTDate,\n            shipmentFDate,\n            setShipmentFDate,\n            shipmentTDate,\n            setShipmentTDate\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_7__.SidebarProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex w-full min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sidebar_Sidebar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        permissions: permissions,\n                        profile: userData\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 w-full pl-3 overflow-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_BreadCrumbs__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    breadcrumblist: [\n                                        {\n                                            link: \"/user/trackSheets\",\n                                            name: \"TrackSheet\"\n                                        }\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-4 pl-3 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"default\",\n                                        onClick: ()=>setActiveView(\"view\"),\n                                        className: \"w-40 shadow-md rounded-xl text-base transition-all duration-200 \".concat(activeView === \"view\" ? \"bg-neutral-800 hover:bg-neutral-900 text-white\" : \"bg-white text-neutral-800 border border-neutral-300 hover:bg-neutral-100\"),\n                                        children: \"View TrackSheet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"default\",\n                                        onClick: ()=>setActiveView(\"create\"),\n                                        className: \"w-40 shadow-md rounded-xl text-base transition-all duration-200 \".concat(activeView === \"create\" ? \"bg-neutral-800 hover:bg-neutral-900 text-white\" : \"bg-white text-neutral-800 border border-neutral-300 hover:bg-neutral-100\"),\n                                        children: \"Create TrackSheet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-5 h-5 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 31\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-900\",\n                                                children: \"Create TrackSheet\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 31\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_10__.Form, {\n                                        ...form,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        form: form,\n                                                        name: \"associateId\",\n                                                        label: \"Select Associate\",\n                                                        placeholder: \"Search Associate...\",\n                                                        isRequired: true,\n                                                        options: (associate === null || associate === void 0 ? void 0 : associate.map((a)=>{\n                                                            var _a_id;\n                                                            return {\n                                                                value: (_a_id = a.id) === null || _a_id === void 0 ? void 0 : _a_id.toString(),\n                                                                label: a.name,\n                                                                name: a.name\n                                                            };\n                                                        })) || [],\n                                                        onValueChange: (value)=>{\n                                                            setInitialAssociateId(value);\n                                                            if (value && initialClientId) {\n                                                                validateClientForAssociate(value, initialClientId);\n                                                            } else {\n                                                                setInitialClientId(\"\");\n                                                                form.setValue(\"clientId\", \"\");\n                                                            }\n                                                            setShowFullForm(false);\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        form: form,\n                                                        name: \"clientId\",\n                                                        label: \"Select Client\",\n                                                        placeholder: \"Search Client...\",\n                                                        isRequired: true,\n                                                        disabled: !initialAssociateId,\n                                                        options: client.filter((c)=>{\n                                                            var _c_associate_id, _c_associate;\n                                                            return !initialAssociateId || ((_c_associate = c.associate) === null || _c_associate === void 0 ? void 0 : (_c_associate_id = _c_associate.id) === null || _c_associate_id === void 0 ? void 0 : _c_associate_id.toString()) === initialAssociateId;\n                                                        }).map((c)=>{\n                                                            var _c_id;\n                                                            return {\n                                                                value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n                                                                label: c.client_name,\n                                                                name: c.client_name\n                                                            };\n                                                        }) || [],\n                                                        onValueChange: (value)=>{\n                                                            setInitialClientId(value);\n                                                            if (showFullForm) {\n                                                                clearEntrySpecificClients();\n                                                            }\n                                                            if (value && initialAssociateId) {\n                                                                setTimeout(()=>{\n                                                                    handleInitialSelection(initialAssociateId, value);\n                                                                }, 100);\n                                                            } else {\n                                                                setShowFullForm(false);\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 31\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 27\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full animate-in fade-in duration-500 rounded-2xl shadow-sm  dark:bg-gray-800 p-1\",\n                                children: activeView === \"create\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_createTrackSheet__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    client: client,\n                                    carrier: carrier,\n                                    associate: associate,\n                                    userData: userData\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClientSelectPage__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    permissions: actions,\n                                    client: client\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                lineNumber: 84,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n            lineNumber: 83,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ManageWorkSheet, \"UPGMassFFBzmIcpJOX/yY6JKo4w=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm\n    ];\n});\n_c = ManageWorkSheet;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ManageWorkSheet);\nvar _c;\n$RefreshReg$(_c, \"ManageWorkSheet\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC91c2VyL3RyYWNrU2hlZXRzL3YyL01hbmFnZVRyYWNrU2hlZXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDd0M7QUFDVTtBQUNGO0FBRVE7QUFDTjtBQUNDO0FBQ087QUFDSDtBQUNkO0FBQ0Y7QUFDa0I7QUFDZjtBQUUxQyxNQUFNYSxrQkFBa0I7UUFBQyxFQUN2QkMsV0FBVyxFQUNYQyxNQUFNLEVBQ05DLE9BQU8sRUFDUEMsU0FBUyxFQUNUQyxRQUFRLEVBQ1JDLE9BQU8sRUFDSDs7SUFDSixNQUFNLENBQUNDLFlBQVlDLGNBQWMsR0FBR3BCLCtDQUFRQSxDQUFDLEVBQUU7SUFDL0MsTUFBTSxDQUFDcUIsWUFBWUMsZUFBZSxHQUFHdEIsK0NBQVFBLENBQUM7SUFDOUMsTUFBTSxDQUFDdUIsZUFBZUMsaUJBQWlCLEdBQUd4QiwrQ0FBUUEsQ0FBQztJQUNuRCxNQUFNLENBQUN5QixlQUFlQyxpQkFBaUIsR0FBRzFCLCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQzJCLGNBQWNDLGdCQUFnQixHQUFHNUIsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDNkIsY0FBY0MsZ0JBQWdCLEdBQUc5QiwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUMrQixlQUFlQyxpQkFBaUIsR0FBR2hDLCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQ2lDLGVBQWVDLGlCQUFpQixHQUFHbEMsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDbUMsWUFBWUMsY0FBYyxHQUFHcEMsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDcUMsb0JBQW9CQyxzQkFBc0IsR0FBR3RDLCtDQUFRQSxDQUFDO0lBQzdELE1BQU0sQ0FBQ3VDLGlCQUFpQkMsbUJBQW1CLEdBQUd4QywrQ0FBUUEsQ0FBQztJQUN2RCxNQUFNLENBQUN5QyxjQUFjQyxnQkFBZ0IsR0FBRzFDLCtDQUFRQSxDQUFDO0lBRWpELE1BQU0yQyxPQUFPaEMseURBQU9BLENBQUM7UUFDbkJpQyxlQUFlO1lBQ2JDLGFBQWE7WUFDYkMsVUFBVTtRQUNaO0lBQ0Y7SUFFQSxTQUFTQywyQkFBMkJDLEtBQWEsRUFBRVQsZUFBdUI7UUFDeEUsTUFBTSxJQUFJVSxNQUFNO0lBQ2xCO0lBRUEsTUFBTUMsNEJBQTRCQyxZQUFZO1FBQzFDLE1BQU1DLGlCQUFpQlQsS0FBS1UsU0FBUyxDQUFDLGNBQWMsRUFBRTtRQUN0RCxJQUFJRCxlQUFlRSxNQUFNLEdBQUcsR0FBRztZQUM3QixNQUFNQywwQkFBMEJILGVBQWVJLElBQUksQ0FBQyxDQUFDQyxRQUFlQSxNQUFNWCxRQUFRO1lBQ2xGLElBQUlTLHlCQUF5QjtnQkFDM0IsTUFBTUcsaUJBQWlCTixlQUFlTyxHQUFHLENBQUMsQ0FBQ0YsUUFBZ0I7d0JBQ3pELEdBQUdBLEtBQUs7d0JBQ1JYLFVBQVU7b0JBQ1o7Z0JBQ0FILEtBQUtpQixRQUFRLENBQUMsV0FBV0Y7WUFDM0I7UUFDRjtJQUNGLEdBQUc7UUFBQ2Y7S0FBSztJQUVYLHFCQUNFLDhEQUFDeEMsaUVBQWlCQSxDQUFDMEQsUUFBUTtRQUN6QmIsT0FBTztZQUNMN0I7WUFDQUM7WUFDQUM7WUFDQUM7WUFDQUM7WUFDQUU7WUFDQUQ7WUFDQUU7WUFDQUM7WUFDQUM7WUFDQUM7WUFDQUM7WUFDQUM7WUFDQUM7WUFDQUM7WUFDQUM7UUFDRjtrQkFFQSw0RUFBQzVCLG1FQUFlQTtzQkFDZCw0RUFBQ3dEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQzFELG1FQUFPQTt3QkFBQ1EsYUFBYUE7d0JBQWFtRCxTQUFTL0M7Ozs7OztrQ0FDNUMsOERBQUNnRDt3QkFBS0YsV0FBVTs7MENBQ2QsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDeEQsa0VBQVdBO29DQUNWMkQsZ0JBQWdCO3dDQUNkOzRDQUFFQyxNQUFNOzRDQUFxQkMsTUFBTTt3Q0FBYTtxQ0FDakQ7Ozs7Ozs7Ozs7OzBDQUtMLDhEQUFDTjtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUM3RCx5REFBTUE7d0NBQ0xtRSxTQUFRO3dDQUNSQyxTQUFTLElBQU1sQyxjQUFjO3dDQUM3QjJCLFdBQVcsbUVBSVYsT0FIQzVCLGVBQWUsU0FDWCxtREFDQTtrREFFUDs7Ozs7O2tEQUdELDhEQUFDakMseURBQU1BO3dDQUNMbUUsU0FBUTt3Q0FDUkMsU0FBUyxJQUFNbEMsY0FBYzt3Q0FDN0IyQixXQUFXLG1FQUlWLE9BSEM1QixlQUFlLFdBQ1gsbURBQ0E7a0RBRVA7Ozs7Ozs7Ozs7OzswQ0FLVyw4REFBQzJCO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDdkQsc0ZBQVNBO2dEQUFDdUQsV0FBVTs7Ozs7OzBEQUNyQiw4REFBQ1E7Z0RBQUdSLFdBQVU7MERBQXNDOzs7Ozs7Ozs7Ozs7a0RBS3RELDhEQUFDdEQsa0RBQUlBO3dDQUFFLEdBQUdrQyxJQUFJO2tEQUNaLDRFQUFDbUI7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDs4REFDQyw0RUFBQ3BELG1FQUFZQTt3REFDWGlDLE1BQU1BO3dEQUNOeUIsTUFBSzt3REFDTEksT0FBTTt3REFDTkMsYUFBWTt3REFDWkMsVUFBVTt3REFDVkMsU0FBUzNELENBQUFBLHNCQUFBQSxnQ0FBQUEsVUFBVzJDLEdBQUcsQ0FBQyxDQUFDaUI7Z0VBQ2hCQTttRUFENEI7Z0VBQ25DNUIsS0FBSyxHQUFFNEIsUUFBQUEsRUFBRUMsRUFBRSxjQUFKRCw0QkFBQUEsTUFBTUUsUUFBUTtnRUFDckJOLE9BQU9JLEVBQUVSLElBQUk7Z0VBQ2JBLE1BQU1RLEVBQUVSLElBQUk7NERBQ2Q7K0RBQU8sRUFBRTt3REFDVFcsZUFBZSxDQUFDL0I7NERBQ2RWLHNCQUFzQlU7NERBRXRCLElBQUlBLFNBQVNULGlCQUFpQjtnRUFDNUJRLDJCQUEyQkMsT0FBT1Q7NERBQ3BDLE9BQU87Z0VBQ0xDLG1CQUFtQjtnRUFDbkJHLEtBQUtpQixRQUFRLENBQUMsWUFBWTs0REFDNUI7NERBQ0FsQixnQkFBZ0I7d0RBQ2xCOzs7Ozs7Ozs7Ozs4REFJSiw4REFBQ29COzhEQUNDLDRFQUFDcEQsbUVBQVlBO3dEQUNYaUMsTUFBTUE7d0RBQ055QixNQUFLO3dEQUNMSSxPQUFNO3dEQUNOQyxhQUFZO3dEQUNaQyxVQUFVO3dEQUNWTSxVQUFVLENBQUMzQzt3REFDWHNDLFNBQVM3RCxPQUNObUUsTUFBTSxDQUFDLENBQUNDO2dFQUFrQ0EsaUJBQUFBO21FQUF2QixDQUFDN0Msc0JBQXNCNkMsRUFBQUEsZUFBQUEsRUFBRWxFLFNBQVMsY0FBWGtFLG9DQUFBQSxrQkFBQUEsYUFBYUwsRUFBRSxjQUFmSyxzQ0FBQUEsZ0JBQWlCSixRQUFRLFFBQU96QzsyREFDMUVzQixHQUFHLENBQUMsQ0FBQ3VCO2dFQUNHQTttRUFEUztnRUFDaEJsQyxLQUFLLEdBQUVrQyxRQUFBQSxFQUFFTCxFQUFFLGNBQUpLLDRCQUFBQSxNQUFNSixRQUFRO2dFQUNyQk4sT0FBT1UsRUFBRUMsV0FBVztnRUFDcEJmLE1BQU1jLEVBQUVDLFdBQVc7NERBQ3JCOzhEQUFPLEVBQUU7d0RBQ1hKLGVBQWUsQ0FBQy9COzREQUNkUixtQkFBbUJROzREQUVuQixJQUFJUCxjQUFjO2dFQUNoQlM7NERBQ0Y7NERBRUEsSUFBSUYsU0FBU1gsb0JBQW9CO2dFQUUvQitDLFdBQVc7b0VBQ1RDLHVCQUF1QmhELG9CQUFvQlc7Z0VBQzdDLEdBQUc7NERBQ0wsT0FBTztnRUFDTE4sZ0JBQWdCOzREQUNsQjt3REFDRjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FPeEIsOERBQUNvQjtnQ0FBSUMsV0FBVTswQ0FDWjVCLGVBQWUseUJBQ2QsOERBQUNsQyx5REFBZ0JBO29DQUFDYSxRQUFRQTtvQ0FBUUMsU0FBU0E7b0NBQVNDLFdBQVdBO29DQUFXQyxVQUFVQTs7Ozs7OERBRXBGLDhEQUFDYix5REFBZ0JBO29DQUNmUyxhQUFhSztvQ0FDYkosUUFBUUE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVMxQjtHQWpNTUY7O1FBcUJTRCxxREFBT0E7OztLQXJCaEJDO0FBbU1OLCtEQUFlQSxlQUFlQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2FwcC91c2VyL3RyYWNrU2hlZXRzL3YyL01hbmFnZVRyYWNrU2hlZXQudHN4P2IwM2UiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XHJcbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSB9IGZyb20gXCJyZWFjdFwiO1xyXG5pbXBvcnQgQ3JlYXRlVHJhY2tTaGVldCBmcm9tIFwiLi9jcmVhdGVUcmFja1NoZWV0XCI7XHJcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYnV0dG9uXCI7XHJcbmltcG9ydCB7IHVzZVNlYXJjaFBhcmFtcyB9IGZyb20gXCJuZXh0L25hdmlnYXRpb25cIjtcclxuaW1wb3J0IHsgVHJhY2tTaGVldENvbnRleHQgfSBmcm9tIFwiLi9UcmFja1NoZWV0Q29udGV4dFwiO1xyXG5pbXBvcnQgQ2xpZW50U2VsZWN0UGFnZSBmcm9tIFwiLi9DbGllbnRTZWxlY3RQYWdlXCI7XHJcbmltcG9ydCBTaWRlYmFyIGZyb20gXCJAL2NvbXBvbmVudHMvc2lkZWJhci9TaWRlYmFyXCI7XHJcbmltcG9ydCB7IFNpZGViYXJQcm92aWRlciB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvc2lkZWJhclwiO1xyXG5pbXBvcnQgQnJlYWRDcnVtYnMgZnJvbSBcIkAvYXBwL19jb21wb25lbnQvQnJlYWRDcnVtYnNcIjtcclxuaW1wb3J0IHsgQnVpbGRpbmcyIH0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiO1xyXG5pbXBvcnQgeyBGb3JtIH0gZnJvbSBcInJlYWN0LWhvb2stZm9ybVwiO1xyXG5pbXBvcnQgU2VhcmNoU2VsZWN0IGZyb20gXCJAL2FwcC9fY29tcG9uZW50L1NlYXJjaFNlbGVjdFwiO1xyXG5pbXBvcnQgeyB1c2VGb3JtIH0gZnJvbSBcInJlYWN0LWhvb2stZm9ybVwiO1xyXG5cclxuY29uc3QgTWFuYWdlV29ya1NoZWV0ID0gKHtcclxuICBwZXJtaXNzaW9ucyxcclxuICBjbGllbnQsXHJcbiAgY2FycmllcixcclxuICBhc3NvY2lhdGUsXHJcbiAgdXNlckRhdGEsXHJcbiAgYWN0aW9ucyxcclxufTogYW55KSA9PiB7XHJcbiAgY29uc3QgW2ZpbHRlcmRhdGEsIHNldEZpbHRlckRhdGFdID0gdXNlU3RhdGUoW10pO1xyXG4gIGNvbnN0IFtkZWxldGVEYXRhLCBzZXREZWxldGVkRGF0YV0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW3JlY2lldmVkRkRhdGUsIHNldFJlY2lldmVkRkRhdGVdID0gdXNlU3RhdGUoXCJcIik7XHJcbiAgY29uc3QgW3JlY2lldmVkVERhdGUsIHNldFJlY2lldmVkVERhdGVdID0gdXNlU3RhdGUoXCJcIik7XHJcbiAgY29uc3QgW2ludm9pY2VGRGF0ZSwgc2V0SW52b2ljZUZEYXRlXSA9IHVzZVN0YXRlKFwiXCIpO1xyXG4gIGNvbnN0IFtpbnZvaWNlVERhdGUsIHNldEludm9pY2VURGF0ZV0gPSB1c2VTdGF0ZShcIlwiKTtcclxuICBjb25zdCBbc2hpcG1lbnRGRGF0ZSwgc2V0U2hpcG1lbnRGRGF0ZV0gPSB1c2VTdGF0ZShcIlwiKTtcclxuICBjb25zdCBbc2hpcG1lbnRURGF0ZSwgc2V0U2hpcG1lbnRURGF0ZV0gPSB1c2VTdGF0ZShcIlwiKTtcclxuICBjb25zdCBbYWN0aXZlVmlldywgc2V0QWN0aXZlVmlld10gPSB1c2VTdGF0ZShcInZpZXdcIik7XHJcbiAgY29uc3QgW2luaXRpYWxBc3NvY2lhdGVJZCwgc2V0SW5pdGlhbEFzc29jaWF0ZUlkXSA9IHVzZVN0YXRlKFwiXCIpO1xyXG4gIGNvbnN0IFtpbml0aWFsQ2xpZW50SWQsIHNldEluaXRpYWxDbGllbnRJZF0gPSB1c2VTdGF0ZShcIlwiKTtcclxuICBjb25zdCBbc2hvd0Z1bGxGb3JtLCBzZXRTaG93RnVsbEZvcm1dID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIFxyXG4gIGNvbnN0IGZvcm0gPSB1c2VGb3JtKHtcclxuICAgIGRlZmF1bHRWYWx1ZXM6IHtcclxuICAgICAgYXNzb2NpYXRlSWQ6IFwiXCIsXHJcbiAgICAgIGNsaWVudElkOiBcIlwiLFxyXG4gICAgfSxcclxuICB9KTsgXHJcblxyXG4gIGZ1bmN0aW9uIHZhbGlkYXRlQ2xpZW50Rm9yQXNzb2NpYXRlKHZhbHVlOiBzdHJpbmcsIGluaXRpYWxDbGllbnRJZDogc3RyaW5nKSB7XHJcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJGdW5jdGlvbiBub3QgaW1wbGVtZW50ZWQuXCIpO1xyXG4gIH1cclxuXHJcbiAgY29uc3QgY2xlYXJFbnRyeVNwZWNpZmljQ2xpZW50cyA9IHVzZUNhbGxiYWNrKCgpID0+IHtcclxuICAgICAgY29uc3QgY3VycmVudEVudHJpZXMgPSBmb3JtLmdldFZhbHVlcyhcImVudHJpZXNcIikgfHwgW107XHJcbiAgICAgIGlmIChjdXJyZW50RW50cmllcy5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgY29uc3QgaGFzRW50cnlTcGVjaWZpY0NsaWVudHMgPSBjdXJyZW50RW50cmllcy5zb21lKChlbnRyeTogYW55KSA9PiBlbnRyeS5jbGllbnRJZCk7XHJcbiAgICAgICAgaWYgKGhhc0VudHJ5U3BlY2lmaWNDbGllbnRzKSB7XHJcbiAgICAgICAgICBjb25zdCB1cGRhdGVkRW50cmllcyA9IGN1cnJlbnRFbnRyaWVzLm1hcCgoZW50cnk6IGFueSkgPT4gKHtcclxuICAgICAgICAgICAgLi4uZW50cnksXHJcbiAgICAgICAgICAgIGNsaWVudElkOiBcIlwiLCBcclxuICAgICAgICAgIH0pKTtcclxuICAgICAgICAgIGZvcm0uc2V0VmFsdWUoXCJlbnRyaWVzXCIsIHVwZGF0ZWRFbnRyaWVzKTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH0sIFtmb3JtXSk7XHJcbiAgICBcclxuICByZXR1cm4gKFxyXG4gICAgPFRyYWNrU2hlZXRDb250ZXh0LlByb3ZpZGVyXHJcbiAgICAgIHZhbHVlPXt7XHJcbiAgICAgICAgZmlsdGVyZGF0YSxcclxuICAgICAgICBzZXRGaWx0ZXJEYXRhLFxyXG4gICAgICAgIGRlbGV0ZURhdGEsXHJcbiAgICAgICAgc2V0RGVsZXRlZERhdGEsXHJcbiAgICAgICAgcmVjaWV2ZWRGRGF0ZSxcclxuICAgICAgICByZWNpZXZlZFREYXRlLFxyXG4gICAgICAgIHNldFJlY2lldmVkRkRhdGUsXHJcbiAgICAgICAgc2V0UmVjaWV2ZWRURGF0ZSxcclxuICAgICAgICBpbnZvaWNlRkRhdGUsXHJcbiAgICAgICAgc2V0SW52b2ljZUZEYXRlLFxyXG4gICAgICAgIGludm9pY2VURGF0ZSxcclxuICAgICAgICBzZXRJbnZvaWNlVERhdGUsXHJcbiAgICAgICAgc2hpcG1lbnRGRGF0ZSxcclxuICAgICAgICBzZXRTaGlwbWVudEZEYXRlLFxyXG4gICAgICAgIHNoaXBtZW50VERhdGUsXHJcbiAgICAgICAgc2V0U2hpcG1lbnRURGF0ZSxcclxuICAgICAgfX1cclxuICAgID5cclxuICAgICAgPFNpZGViYXJQcm92aWRlcj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggdy1mdWxsIG1pbi1oLXNjcmVlblwiPlxyXG4gICAgICAgICAgPFNpZGViYXIgcGVybWlzc2lvbnM9e3Blcm1pc3Npb25zfSBwcm9maWxlPXt1c2VyRGF0YX0gLz5cclxuICAgICAgICAgIDxtYWluIGNsYXNzTmFtZT1cImZsZXgtMSB3LWZ1bGwgcGwtMyBvdmVyZmxvdy1hdXRvXCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNlwiPlxyXG4gICAgICAgICAgICAgIDxCcmVhZENydW1ic1xyXG4gICAgICAgICAgICAgICAgYnJlYWRjcnVtYmxpc3Q9e1tcclxuICAgICAgICAgICAgICAgICAgeyBsaW5rOiBcIi91c2VyL3RyYWNrU2hlZXRzXCIsIG5hbWU6IFwiVHJhY2tTaGVldFwiIH0sXHJcbiAgICAgICAgICAgICAgICBdfVxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgey8qIFRvZ2dsZSBCdXR0b25zICovfVxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTQgcGwtMyBtYi02XCI+XHJcbiAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cImRlZmF1bHRcIlxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0QWN0aXZlVmlldyhcInZpZXdcIil9XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LTQwIHNoYWRvdy1tZCByb3VuZGVkLXhsIHRleHQtYmFzZSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgJHtcclxuICAgICAgICAgICAgICAgICAgYWN0aXZlVmlldyA9PT0gXCJ2aWV3XCJcclxuICAgICAgICAgICAgICAgICAgICA/IFwiYmctbmV1dHJhbC04MDAgaG92ZXI6YmctbmV1dHJhbC05MDAgdGV4dC13aGl0ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgOiBcImJnLXdoaXRlIHRleHQtbmV1dHJhbC04MDAgYm9yZGVyIGJvcmRlci1uZXV0cmFsLTMwMCBob3ZlcjpiZy1uZXV0cmFsLTEwMFwiXHJcbiAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICBWaWV3IFRyYWNrU2hlZXRcclxuICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICB2YXJpYW50PVwiZGVmYXVsdFwiXHJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRBY3RpdmVWaWV3KFwiY3JlYXRlXCIpfVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy00MCBzaGFkb3ctbWQgcm91bmRlZC14bCB0ZXh0LWJhc2UgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwICR7XHJcbiAgICAgICAgICAgICAgICAgIGFjdGl2ZVZpZXcgPT09IFwiY3JlYXRlXCJcclxuICAgICAgICAgICAgICAgICAgICA/IFwiYmctbmV1dHJhbC04MDAgaG92ZXI6YmctbmV1dHJhbC05MDAgdGV4dC13aGl0ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgOiBcImJnLXdoaXRlIHRleHQtbmV1dHJhbC04MDAgYm9yZGVyIGJvcmRlci1uZXV0cmFsLTMwMCBob3ZlcjpiZy1uZXV0cmFsLTEwMFwiXHJcbiAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICBDcmVhdGUgVHJhY2tTaGVldFxyXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIHsvKiBBc3NvY2lhdGUgYW5kIENsaWVudCBTZWxlY3Rpb24gU2VjdGlvbiAtIEFsd2F5cyBWaXNpYmxlICovfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctc20gYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBwLTQgbWItNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgbWItNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QnVpbGRpbmcyIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1ibHVlLTYwMFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIENyZWF0ZSBUcmFja1NoZWV0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvaDI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEZvcm0gey4uLmZvcm19PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFNlYXJjaFNlbGVjdFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb3JtPXtmb3JtfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBuYW1lPVwiYXNzb2NpYXRlSWRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbD1cIlNlbGVjdCBBc3NvY2lhdGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlNlYXJjaCBBc3NvY2lhdGUuLi5cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc1JlcXVpcmVkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9wdGlvbnM9e2Fzc29jaWF0ZT8ubWFwKChhOiBhbnkpID0+ICh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IGEuaWQ/LnRvU3RyaW5nKCksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw6IGEubmFtZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBuYW1lOiBhLm5hbWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pKSB8fCBbXX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25WYWx1ZUNoYW5nZT17KHZhbHVlKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0SW5pdGlhbEFzc29jaWF0ZUlkKHZhbHVlKTtcclxuICAgICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmICh2YWx1ZSAmJiBpbml0aWFsQ2xpZW50SWQpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbGlkYXRlQ2xpZW50Rm9yQXNzb2NpYXRlKHZhbHVlLCBpbml0aWFsQ2xpZW50SWQpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRJbml0aWFsQ2xpZW50SWQoXCJcIik7IFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9ybS5zZXRWYWx1ZShcImNsaWVudElkXCIsIFwiXCIpOyBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0U2hvd0Z1bGxGb3JtKGZhbHNlKTsgXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTZWFyY2hTZWxlY3RcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9ybT17Zm9ybX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbmFtZT1cImNsaWVudElkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw9XCJTZWxlY3QgQ2xpZW50XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTZWFyY2ggQ2xpZW50Li4uXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaXNSZXF1aXJlZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17IWluaXRpYWxBc3NvY2lhdGVJZH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb3B0aW9ucz17Y2xpZW50XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLmZpbHRlcigoYzogYW55KSA9PiAhaW5pdGlhbEFzc29jaWF0ZUlkIHx8IGMuYXNzb2NpYXRlPy5pZD8udG9TdHJpbmcoKSA9PT0gaW5pdGlhbEFzc29jaWF0ZUlkKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5tYXAoKGM6IGFueSkgPT4gKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlOiBjLmlkPy50b1N0cmluZygpLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw6IGMuY2xpZW50X25hbWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBuYW1lOiBjLmNsaWVudF9uYW1lXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSkpIHx8IFtdfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvblZhbHVlQ2hhbmdlPXsodmFsdWUpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRJbml0aWFsQ2xpZW50SWQodmFsdWUpO1xyXG4gICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHNob3dGdWxsRm9ybSkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xlYXJFbnRyeVNwZWNpZmljQ2xpZW50cygpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmICh2YWx1ZSAmJiBpbml0aWFsQXNzb2NpYXRlSWQpIHtcclxuICAgICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZUluaXRpYWxTZWxlY3Rpb24oaW5pdGlhbEFzc29jaWF0ZUlkLCB2YWx1ZSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LCAxMDApO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRTaG93RnVsbEZvcm0oZmFsc2UpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Gb3JtPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGFuaW1hdGUtaW4gZmFkZS1pbiBkdXJhdGlvbi01MDAgcm91bmRlZC0yeGwgc2hhZG93LXNtICBkYXJrOmJnLWdyYXktODAwIHAtMVwiPlxyXG4gICAgICAgICAgICAgIHthY3RpdmVWaWV3ID09PSBcImNyZWF0ZVwiID8gKFxyXG4gICAgICAgICAgICAgICAgPENyZWF0ZVRyYWNrU2hlZXQgY2xpZW50PXtjbGllbnR9IGNhcnJpZXI9e2NhcnJpZXJ9IGFzc29jaWF0ZT17YXNzb2NpYXRlfSB1c2VyRGF0YT17dXNlckRhdGF9IC8+XHJcbiAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgIDxDbGllbnRTZWxlY3RQYWdlXHJcbiAgICAgICAgICAgICAgICAgIHBlcm1pc3Npb25zPXthY3Rpb25zfVxyXG4gICAgICAgICAgICAgICAgICBjbGllbnQ9e2NsaWVudH1cclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L21haW4+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvU2lkZWJhclByb3ZpZGVyPlxyXG4gICAgPC9UcmFja1NoZWV0Q29udGV4dC5Qcm92aWRlcj5cclxuICApO1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgTWFuYWdlV29ya1NoZWV0O1xyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsIkNyZWF0ZVRyYWNrU2hlZXQiLCJCdXR0b24iLCJUcmFja1NoZWV0Q29udGV4dCIsIkNsaWVudFNlbGVjdFBhZ2UiLCJTaWRlYmFyIiwiU2lkZWJhclByb3ZpZGVyIiwiQnJlYWRDcnVtYnMiLCJCdWlsZGluZzIiLCJGb3JtIiwiU2VhcmNoU2VsZWN0IiwidXNlRm9ybSIsIk1hbmFnZVdvcmtTaGVldCIsInBlcm1pc3Npb25zIiwiY2xpZW50IiwiY2FycmllciIsImFzc29jaWF0ZSIsInVzZXJEYXRhIiwiYWN0aW9ucyIsImZpbHRlcmRhdGEiLCJzZXRGaWx0ZXJEYXRhIiwiZGVsZXRlRGF0YSIsInNldERlbGV0ZWREYXRhIiwicmVjaWV2ZWRGRGF0ZSIsInNldFJlY2lldmVkRkRhdGUiLCJyZWNpZXZlZFREYXRlIiwic2V0UmVjaWV2ZWRURGF0ZSIsImludm9pY2VGRGF0ZSIsInNldEludm9pY2VGRGF0ZSIsImludm9pY2VURGF0ZSIsInNldEludm9pY2VURGF0ZSIsInNoaXBtZW50RkRhdGUiLCJzZXRTaGlwbWVudEZEYXRlIiwic2hpcG1lbnRURGF0ZSIsInNldFNoaXBtZW50VERhdGUiLCJhY3RpdmVWaWV3Iiwic2V0QWN0aXZlVmlldyIsImluaXRpYWxBc3NvY2lhdGVJZCIsInNldEluaXRpYWxBc3NvY2lhdGVJZCIsImluaXRpYWxDbGllbnRJZCIsInNldEluaXRpYWxDbGllbnRJZCIsInNob3dGdWxsRm9ybSIsInNldFNob3dGdWxsRm9ybSIsImZvcm0iLCJkZWZhdWx0VmFsdWVzIiwiYXNzb2NpYXRlSWQiLCJjbGllbnRJZCIsInZhbGlkYXRlQ2xpZW50Rm9yQXNzb2NpYXRlIiwidmFsdWUiLCJFcnJvciIsImNsZWFyRW50cnlTcGVjaWZpY0NsaWVudHMiLCJ1c2VDYWxsYmFjayIsImN1cnJlbnRFbnRyaWVzIiwiZ2V0VmFsdWVzIiwibGVuZ3RoIiwiaGFzRW50cnlTcGVjaWZpY0NsaWVudHMiLCJzb21lIiwiZW50cnkiLCJ1cGRhdGVkRW50cmllcyIsIm1hcCIsInNldFZhbHVlIiwiUHJvdmlkZXIiLCJkaXYiLCJjbGFzc05hbWUiLCJwcm9maWxlIiwibWFpbiIsImJyZWFkY3J1bWJsaXN0IiwibGluayIsIm5hbWUiLCJ2YXJpYW50Iiwib25DbGljayIsImgyIiwibGFiZWwiLCJwbGFjZWhvbGRlciIsImlzUmVxdWlyZWQiLCJvcHRpb25zIiwiYSIsImlkIiwidG9TdHJpbmciLCJvblZhbHVlQ2hhbmdlIiwiZGlzYWJsZWQiLCJmaWx0ZXIiLCJjIiwiY2xpZW50X25hbWUiLCJzZXRUaW1lb3V0IiwiaGFuZGxlSW5pdGlhbFNlbGVjdGlvbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/trackSheets/v2/ManageTrackSheet.tsx\n"));

/***/ })

});