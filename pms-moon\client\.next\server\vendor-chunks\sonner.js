"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/sonner";
exports.ids = ["vendor-chunks/sonner"];
exports.modules = {

/***/ "(ssr)/./node_modules/sonner/dist/index.mjs":
/*!********************************************!*\
  !*** ./node_modules/sonner/dist/index.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ $e),\n/* harmony export */   toast: () => (/* binding */ ue),\n/* harmony export */   useSonner: () => (/* binding */ Oe)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* __next_internal_client_entry_do_not_use__ Toaster,toast,useSonner auto */ \n\n\nvar jt = (n)=>{\n    switch(n){\n        case \"success\":\n            return ee;\n        case \"info\":\n            return ae;\n        case \"warning\":\n            return oe;\n        case \"error\":\n            return se;\n        default:\n            return null;\n    }\n}, te = Array(12).fill(0), Yt = ({ visible: n, className: e })=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: [\n            \"sonner-loading-wrapper\",\n            e\n        ].filter(Boolean).join(\" \"),\n        \"data-visible\": n\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: \"sonner-spinner\"\n    }, te.map((t, a)=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: \"sonner-loading-bar\",\n            key: `spinner-bar-${a}`\n        })))), ee = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z\",\n    clipRule: \"evenodd\"\n})), oe = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z\",\n    clipRule: \"evenodd\"\n})), ae = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z\",\n    clipRule: \"evenodd\"\n})), se = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z\",\n    clipRule: \"evenodd\"\n})), Ot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n    x1: \"18\",\n    y1: \"6\",\n    x2: \"6\",\n    y2: \"18\"\n}), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n    x1: \"6\",\n    y1: \"6\",\n    x2: \"18\",\n    y2: \"18\"\n}));\n\nvar Ft = ()=>{\n    let [n, e] = react__WEBPACK_IMPORTED_MODULE_0__.useState(document.hidden);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        let t = ()=>{\n            e(document.hidden);\n        };\n        return document.addEventListener(\"visibilitychange\", t), ()=>window.removeEventListener(\"visibilitychange\", t);\n    }, []), n;\n};\n\nvar bt = 1, yt = class {\n    constructor(){\n        this.subscribe = (e)=>(this.subscribers.push(e), ()=>{\n                let t = this.subscribers.indexOf(e);\n                this.subscribers.splice(t, 1);\n            });\n        this.publish = (e)=>{\n            this.subscribers.forEach((t)=>t(e));\n        };\n        this.addToast = (e)=>{\n            this.publish(e), this.toasts = [\n                ...this.toasts,\n                e\n            ];\n        };\n        this.create = (e)=>{\n            var S;\n            let { message: t, ...a } = e, u = typeof (e == null ? void 0 : e.id) == \"number\" || ((S = e.id) == null ? void 0 : S.length) > 0 ? e.id : bt++, f = this.toasts.find((g)=>g.id === u), w = e.dismissible === void 0 ? !0 : e.dismissible;\n            return this.dismissedToasts.has(u) && this.dismissedToasts.delete(u), f ? this.toasts = this.toasts.map((g)=>g.id === u ? (this.publish({\n                    ...g,\n                    ...e,\n                    id: u,\n                    title: t\n                }), {\n                    ...g,\n                    ...e,\n                    id: u,\n                    dismissible: w,\n                    title: t\n                }) : g) : this.addToast({\n                title: t,\n                ...a,\n                dismissible: w,\n                id: u\n            }), u;\n        };\n        this.dismiss = (e)=>(this.dismissedToasts.add(e), e || this.toasts.forEach((t)=>{\n                this.subscribers.forEach((a)=>a({\n                        id: t.id,\n                        dismiss: !0\n                    }));\n            }), this.subscribers.forEach((t)=>t({\n                    id: e,\n                    dismiss: !0\n                })), e);\n        this.message = (e, t)=>this.create({\n                ...t,\n                message: e\n            });\n        this.error = (e, t)=>this.create({\n                ...t,\n                message: e,\n                type: \"error\"\n            });\n        this.success = (e, t)=>this.create({\n                ...t,\n                type: \"success\",\n                message: e\n            });\n        this.info = (e, t)=>this.create({\n                ...t,\n                type: \"info\",\n                message: e\n            });\n        this.warning = (e, t)=>this.create({\n                ...t,\n                type: \"warning\",\n                message: e\n            });\n        this.loading = (e, t)=>this.create({\n                ...t,\n                type: \"loading\",\n                message: e\n            });\n        this.promise = (e, t)=>{\n            if (!t) return;\n            let a;\n            t.loading !== void 0 && (a = this.create({\n                ...t,\n                promise: e,\n                type: \"loading\",\n                message: t.loading,\n                description: typeof t.description != \"function\" ? t.description : void 0\n            }));\n            let u = e instanceof Promise ? e : e(), f = a !== void 0, w, S = u.then(async (i)=>{\n                if (w = [\n                    \"resolve\",\n                    i\n                ], /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(i)) f = !1, this.create({\n                    id: a,\n                    type: \"default\",\n                    message: i\n                });\n                else if (ie(i) && !i.ok) {\n                    f = !1;\n                    let T = typeof t.error == \"function\" ? await t.error(`HTTP error! status: ${i.status}`) : t.error, F = typeof t.description == \"function\" ? await t.description(`HTTP error! status: ${i.status}`) : t.description;\n                    this.create({\n                        id: a,\n                        type: \"error\",\n                        message: T,\n                        description: F\n                    });\n                } else if (t.success !== void 0) {\n                    f = !1;\n                    let T = typeof t.success == \"function\" ? await t.success(i) : t.success, F = typeof t.description == \"function\" ? await t.description(i) : t.description;\n                    this.create({\n                        id: a,\n                        type: \"success\",\n                        message: T,\n                        description: F\n                    });\n                }\n            }).catch(async (i)=>{\n                if (w = [\n                    \"reject\",\n                    i\n                ], t.error !== void 0) {\n                    f = !1;\n                    let D = typeof t.error == \"function\" ? await t.error(i) : t.error, T = typeof t.description == \"function\" ? await t.description(i) : t.description;\n                    this.create({\n                        id: a,\n                        type: \"error\",\n                        message: D,\n                        description: T\n                    });\n                }\n            }).finally(()=>{\n                var i;\n                f && (this.dismiss(a), a = void 0), (i = t.finally) == null || i.call(t);\n            }), g = ()=>new Promise((i, D)=>S.then(()=>w[0] === \"reject\" ? D(w[1]) : i(w[1])).catch(D));\n            return typeof a != \"string\" && typeof a != \"number\" ? {\n                unwrap: g\n            } : Object.assign(a, {\n                unwrap: g\n            });\n        };\n        this.custom = (e, t)=>{\n            let a = (t == null ? void 0 : t.id) || bt++;\n            return this.create({\n                jsx: e(a),\n                id: a,\n                ...t\n            }), a;\n        };\n        this.getActiveToasts = ()=>this.toasts.filter((e)=>!this.dismissedToasts.has(e.id));\n        this.subscribers = [], this.toasts = [], this.dismissedToasts = new Set;\n    }\n}, v = new yt, ne = (n, e)=>{\n    let t = (e == null ? void 0 : e.id) || bt++;\n    return v.addToast({\n        title: n,\n        ...e,\n        id: t\n    }), t;\n}, ie = (n)=>n && typeof n == \"object\" && \"ok\" in n && typeof n.ok == \"boolean\" && \"status\" in n && typeof n.status == \"number\", le = ne, ce = ()=>v.toasts, de = ()=>v.getActiveToasts(), ue = Object.assign(le, {\n    success: v.success,\n    info: v.info,\n    warning: v.warning,\n    error: v.error,\n    custom: v.custom,\n    message: v.message,\n    promise: v.promise,\n    dismiss: v.dismiss,\n    loading: v.loading\n}, {\n    getHistory: ce,\n    getToasts: de\n});\nfunction wt(n, { insertAt: e } = {}) {\n    if (!n || typeof document == \"undefined\") return;\n    let t = document.head || document.getElementsByTagName(\"head\")[0], a = document.createElement(\"style\");\n    a.type = \"text/css\", e === \"top\" && t.firstChild ? t.insertBefore(a, t.firstChild) : t.appendChild(a), a.styleSheet ? a.styleSheet.cssText = n : a.appendChild(document.createTextNode(n));\n}\nwt(`:where(html[dir=\"ltr\"]),:where([data-sonner-toaster][dir=\"ltr\"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir=\"rtl\"]),:where([data-sonner-toaster][dir=\"rtl\"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999;transition:transform .4s ease}:where([data-sonner-toaster][data-lifted=\"true\"]){transform:translateY(-10px)}@media (hover: none) and (pointer: coarse){:where([data-sonner-toaster][data-lifted=\"true\"]){transform:none}}:where([data-sonner-toaster][data-x-position=\"right\"]){right:var(--offset-right)}:where([data-sonner-toaster][data-x-position=\"left\"]){left:var(--offset-left)}:where([data-sonner-toaster][data-x-position=\"center\"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position=\"top\"]){top:var(--offset-top)}:where([data-sonner-toaster][data-y-position=\"bottom\"]){bottom:var(--offset-bottom)}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled=\"true\"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position=\"top\"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position=\"bottom\"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise=\"true\"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme=\"dark\"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast] [data-close-button]{background:var(--gray1)}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled=\"true\"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping=\"true\"]):before{content:\"\";position:absolute;left:-50%;right:-50%;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position=\"top\"][data-swiping=\"true\"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position=\"bottom\"][data-swiping=\"true\"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping=\"false\"][data-removed=\"true\"]):before{content:\"\";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:\"\";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted=\"true\"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded=\"false\"][data-front=\"false\"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded=\"false\"][data-front=\"false\"][data-styled=\"true\"])>*{opacity:0}:where([data-sonner-toast][data-visible=\"false\"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted=\"true\"][data-expanded=\"true\"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"true\"][data-swipe-out=\"false\"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"false\"][data-swipe-out=\"false\"][data-expanded=\"true\"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"false\"][data-swipe-out=\"false\"][data-expanded=\"false\"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"false\"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y, 0px)) translate(var(--swipe-amount-x, 0px));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-bg-hover: hsl(0, 0%, 12%);--normal-border: hsl(0, 0%, 20%);--normal-border-hover: hsl(0, 0%, 25%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}\n`);\nfunction tt(n) {\n    return n.label !== void 0;\n}\nvar pe = 3, me = \"32px\", ge = \"16px\", Wt = 4e3, he = 356, be = 14, ye = 20, we = 200;\nfunction M(...n) {\n    return n.filter(Boolean).join(\" \");\n}\nfunction xe(n) {\n    let [e, t] = n.split(\"-\"), a = [];\n    return e && a.push(e), t && a.push(t), a;\n}\nvar ve = (n)=>{\n    var Dt, Pt, Nt, Bt, Ct, kt, It, Mt, Ht, At, Lt;\n    let { invert: e, toast: t, unstyled: a, interacting: u, setHeights: f, visibleToasts: w, heights: S, index: g, toasts: i, expanded: D, removeToast: T, defaultRichColors: F, closeButton: et, style: ut, cancelButtonStyle: ft, actionButtonStyle: l, className: ot = \"\", descriptionClassName: at = \"\", duration: X, position: st, gap: pt, loadingIcon: rt, expandByDefault: B, classNames: s, icons: P, closeButtonAriaLabel: nt = \"Close toast\", pauseWhenPageIsHidden: it } = n, [Y, C] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null), [lt, J] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null), [W, H] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [A, mt] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [L, z] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [ct, d] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [h, y] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [R, j] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0), [p, _] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0), O = react__WEBPACK_IMPORTED_MODULE_0__.useRef(t.duration || X || Wt), G = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), k = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), Vt = g === 0, Ut = g + 1 <= w, N = t.type, V = t.dismissible !== !1, Kt = t.className || \"\", Xt = t.descriptionClassName || \"\", dt = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>S.findIndex((r)=>r.toastId === t.id) || 0, [\n        S,\n        t.id\n    ]), Jt = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        var r;\n        return (r = t.closeButton) != null ? r : et;\n    }, [\n        t.closeButton,\n        et\n    ]), Tt = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>t.duration || X || Wt, [\n        t.duration,\n        X\n    ]), gt = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0), U = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0), St = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0), K = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), [Gt, Qt] = st.split(\"-\"), Rt = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>S.reduce((r, m, c)=>c >= dt ? r : r + m.height, 0), [\n        S,\n        dt\n    ]), Et = Ft(), qt = t.invert || e, ht = N === \"loading\";\n    U.current = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>dt * pt + Rt, [\n        dt,\n        Rt\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        O.current = Tt;\n    }, [\n        Tt\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        H(!0);\n    }, []), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        let r = k.current;\n        if (r) {\n            let m = r.getBoundingClientRect().height;\n            return _(m), f((c)=>[\n                    {\n                        toastId: t.id,\n                        height: m,\n                        position: t.position\n                    },\n                    ...c\n                ]), ()=>f((c)=>c.filter((b)=>b.toastId !== t.id));\n        }\n    }, [\n        f,\n        t.id\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect(()=>{\n        if (!W) return;\n        let r = k.current, m = r.style.height;\n        r.style.height = \"auto\";\n        let c = r.getBoundingClientRect().height;\n        r.style.height = m, _(c), f((b)=>b.find((x)=>x.toastId === t.id) ? b.map((x)=>x.toastId === t.id ? {\n                    ...x,\n                    height: c\n                } : x) : [\n                {\n                    toastId: t.id,\n                    height: c,\n                    position: t.position\n                },\n                ...b\n            ]);\n    }, [\n        W,\n        t.title,\n        t.description,\n        f,\n        t.id\n    ]);\n    let $ = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        mt(!0), j(U.current), f((r)=>r.filter((m)=>m.toastId !== t.id)), setTimeout(()=>{\n            T(t);\n        }, we);\n    }, [\n        t,\n        T,\n        f,\n        U\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (t.promise && N === \"loading\" || t.duration === 1 / 0 || t.type === \"loading\") return;\n        let r;\n        return D || u || it && Et ? (()=>{\n            if (St.current < gt.current) {\n                let b = new Date().getTime() - gt.current;\n                O.current = O.current - b;\n            }\n            St.current = new Date().getTime();\n        })() : (()=>{\n            O.current !== 1 / 0 && (gt.current = new Date().getTime(), r = setTimeout(()=>{\n                var b;\n                (b = t.onAutoClose) == null || b.call(t, t), $();\n            }, O.current));\n        })(), ()=>clearTimeout(r);\n    }, [\n        D,\n        u,\n        t,\n        N,\n        it,\n        Et,\n        $\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        t.delete && $();\n    }, [\n        $,\n        t.delete\n    ]);\n    function Zt() {\n        var r, m, c;\n        return P != null && P.loading ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: M(s == null ? void 0 : s.loader, (r = t == null ? void 0 : t.classNames) == null ? void 0 : r.loader, \"sonner-loader\"),\n            \"data-visible\": N === \"loading\"\n        }, P.loading) : rt ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: M(s == null ? void 0 : s.loader, (m = t == null ? void 0 : t.classNames) == null ? void 0 : m.loader, \"sonner-loader\"),\n            \"data-visible\": N === \"loading\"\n        }, rt) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Yt, {\n            className: M(s == null ? void 0 : s.loader, (c = t == null ? void 0 : t.classNames) == null ? void 0 : c.loader),\n            visible: N === \"loading\"\n        });\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"li\", {\n        tabIndex: 0,\n        ref: k,\n        className: M(ot, Kt, s == null ? void 0 : s.toast, (Dt = t == null ? void 0 : t.classNames) == null ? void 0 : Dt.toast, s == null ? void 0 : s.default, s == null ? void 0 : s[N], (Pt = t == null ? void 0 : t.classNames) == null ? void 0 : Pt[N]),\n        \"data-sonner-toast\": \"\",\n        \"data-rich-colors\": (Nt = t.richColors) != null ? Nt : F,\n        \"data-styled\": !(t.jsx || t.unstyled || a),\n        \"data-mounted\": W,\n        \"data-promise\": !!t.promise,\n        \"data-swiped\": h,\n        \"data-removed\": A,\n        \"data-visible\": Ut,\n        \"data-y-position\": Gt,\n        \"data-x-position\": Qt,\n        \"data-index\": g,\n        \"data-front\": Vt,\n        \"data-swiping\": L,\n        \"data-dismissible\": V,\n        \"data-type\": N,\n        \"data-invert\": qt,\n        \"data-swipe-out\": ct,\n        \"data-swipe-direction\": lt,\n        \"data-expanded\": !!(D || B && W),\n        style: {\n            \"--index\": g,\n            \"--toasts-before\": g,\n            \"--z-index\": i.length - g,\n            \"--offset\": `${A ? R : U.current}px`,\n            \"--initial-height\": B ? \"auto\" : `${p}px`,\n            ...ut,\n            ...t.style\n        },\n        onDragEnd: ()=>{\n            z(!1), C(null), K.current = null;\n        },\n        onPointerDown: (r)=>{\n            ht || !V || (G.current = new Date, j(U.current), r.target.setPointerCapture(r.pointerId), r.target.tagName !== \"BUTTON\" && (z(!0), K.current = {\n                x: r.clientX,\n                y: r.clientY\n            }));\n        },\n        onPointerUp: ()=>{\n            var x, Q, q, Z;\n            if (ct || !V) return;\n            K.current = null;\n            let r = Number(((x = k.current) == null ? void 0 : x.style.getPropertyValue(\"--swipe-amount-x\").replace(\"px\", \"\")) || 0), m = Number(((Q = k.current) == null ? void 0 : Q.style.getPropertyValue(\"--swipe-amount-y\").replace(\"px\", \"\")) || 0), c = new Date().getTime() - ((q = G.current) == null ? void 0 : q.getTime()), b = Y === \"x\" ? r : m, I = Math.abs(b) / c;\n            if (Math.abs(b) >= ye || I > .11) {\n                j(U.current), (Z = t.onDismiss) == null || Z.call(t, t), J(Y === \"x\" ? r > 0 ? \"right\" : \"left\" : m > 0 ? \"down\" : \"up\"), $(), d(!0), y(!1);\n                return;\n            }\n            z(!1), C(null);\n        },\n        onPointerMove: (r)=>{\n            var Q, q, Z, zt;\n            if (!K.current || !V || ((Q = window.getSelection()) == null ? void 0 : Q.toString().length) > 0) return;\n            let c = r.clientY - K.current.y, b = r.clientX - K.current.x, I = (q = n.swipeDirections) != null ? q : xe(st);\n            !Y && (Math.abs(b) > 1 || Math.abs(c) > 1) && C(Math.abs(b) > Math.abs(c) ? \"x\" : \"y\");\n            let x = {\n                x: 0,\n                y: 0\n            };\n            Y === \"y\" ? (I.includes(\"top\") || I.includes(\"bottom\")) && (I.includes(\"top\") && c < 0 || I.includes(\"bottom\") && c > 0) && (x.y = c) : Y === \"x\" && (I.includes(\"left\") || I.includes(\"right\")) && (I.includes(\"left\") && b < 0 || I.includes(\"right\") && b > 0) && (x.x = b), (Math.abs(x.x) > 0 || Math.abs(x.y) > 0) && y(!0), (Z = k.current) == null || Z.style.setProperty(\"--swipe-amount-x\", `${x.x}px`), (zt = k.current) == null || zt.style.setProperty(\"--swipe-amount-y\", `${x.y}px`);\n        }\n    }, Jt && !t.jsx ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"aria-label\": nt,\n        \"data-disabled\": ht,\n        \"data-close-button\": !0,\n        onClick: ht || !V ? ()=>{} : ()=>{\n            var r;\n            $(), (r = t.onDismiss) == null || r.call(t, t);\n        },\n        className: M(s == null ? void 0 : s.closeButton, (Bt = t == null ? void 0 : t.classNames) == null ? void 0 : Bt.closeButton)\n    }, (Ct = P == null ? void 0 : P.close) != null ? Ct : Ot) : null, t.jsx || /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(t.title) ? t.jsx ? t.jsx : typeof t.title == \"function\" ? t.title() : t.title : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, N || t.icon || t.promise ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-icon\": \"\",\n        className: M(s == null ? void 0 : s.icon, (kt = t == null ? void 0 : t.classNames) == null ? void 0 : kt.icon)\n    }, t.promise || t.type === \"loading\" && !t.icon ? t.icon || Zt() : null, t.type !== \"loading\" ? t.icon || (P == null ? void 0 : P[N]) || jt(N) : null) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-content\": \"\",\n        className: M(s == null ? void 0 : s.content, (It = t == null ? void 0 : t.classNames) == null ? void 0 : It.content)\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-title\": \"\",\n        className: M(s == null ? void 0 : s.title, (Mt = t == null ? void 0 : t.classNames) == null ? void 0 : Mt.title)\n    }, typeof t.title == \"function\" ? t.title() : t.title), t.description ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-description\": \"\",\n        className: M(at, Xt, s == null ? void 0 : s.description, (Ht = t == null ? void 0 : t.classNames) == null ? void 0 : Ht.description)\n    }, typeof t.description == \"function\" ? t.description() : t.description) : null), /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(t.cancel) ? t.cancel : t.cancel && tt(t.cancel) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"data-button\": !0,\n        \"data-cancel\": !0,\n        style: t.cancelButtonStyle || ft,\n        onClick: (r)=>{\n            var m, c;\n            tt(t.cancel) && V && ((c = (m = t.cancel).onClick) == null || c.call(m, r), $());\n        },\n        className: M(s == null ? void 0 : s.cancelButton, (At = t == null ? void 0 : t.classNames) == null ? void 0 : At.cancelButton)\n    }, t.cancel.label) : null, /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(t.action) ? t.action : t.action && tt(t.action) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"data-button\": !0,\n        \"data-action\": !0,\n        style: t.actionButtonStyle || l,\n        onClick: (r)=>{\n            var m, c;\n            tt(t.action) && ((c = (m = t.action).onClick) == null || c.call(m, r), !r.defaultPrevented && $());\n        },\n        className: M(s == null ? void 0 : s.actionButton, (Lt = t == null ? void 0 : t.classNames) == null ? void 0 : Lt.actionButton)\n    }, t.action.label) : null));\n};\nfunction _t() {\n    if (true) return \"ltr\";\n    let n = document.documentElement.getAttribute(\"dir\");\n    return n === \"auto\" || !n ? window.getComputedStyle(document.documentElement).direction : n;\n}\nfunction Te(n, e) {\n    let t = {};\n    return [\n        n,\n        e\n    ].forEach((a, u)=>{\n        let f = u === 1, w = f ? \"--mobile-offset\" : \"--offset\", S = f ? ge : me;\n        function g(i) {\n            [\n                \"top\",\n                \"right\",\n                \"bottom\",\n                \"left\"\n            ].forEach((D)=>{\n                t[`${w}-${D}`] = typeof i == \"number\" ? `${i}px` : i;\n            });\n        }\n        typeof a == \"number\" || typeof a == \"string\" ? g(a) : typeof a == \"object\" ? [\n            \"top\",\n            \"right\",\n            \"bottom\",\n            \"left\"\n        ].forEach((i)=>{\n            a[i] === void 0 ? t[`${w}-${i}`] = S : t[`${w}-${i}`] = typeof a[i] == \"number\" ? `${a[i]}px` : a[i];\n        }) : g(S);\n    }), t;\n}\nfunction Oe() {\n    let [n, e] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>v.subscribe((t)=>{\n            if (t.dismiss) {\n                setTimeout(()=>{\n                    react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(()=>{\n                        e((a)=>a.filter((u)=>u.id !== t.id));\n                    });\n                });\n                return;\n            }\n            setTimeout(()=>{\n                react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(()=>{\n                    e((a)=>{\n                        let u = a.findIndex((f)=>f.id === t.id);\n                        return u !== -1 ? [\n                            ...a.slice(0, u),\n                            {\n                                ...a[u],\n                                ...t\n                            },\n                            ...a.slice(u + 1)\n                        ] : [\n                            t,\n                            ...a\n                        ];\n                    });\n                });\n            });\n        }), []), {\n        toasts: n\n    };\n}\nvar $e = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(function(e, t) {\n    let { invert: a, position: u = \"bottom-right\", hotkey: f = [\n        \"altKey\",\n        \"KeyT\"\n    ], expand: w, closeButton: S, className: g, offset: i, mobileOffset: D, theme: T = \"light\", richColors: F, duration: et, style: ut, visibleToasts: ft = pe, toastOptions: l, dir: ot = _t(), gap: at = be, loadingIcon: X, icons: st, containerAriaLabel: pt = \"Notifications\", pauseWhenPageIsHidden: rt } = e, [B, s] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]), P = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>Array.from(new Set([\n            u\n        ].concat(B.filter((d)=>d.position).map((d)=>d.position)))), [\n        B,\n        u\n    ]), [nt, it] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]), [Y, C] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [lt, J] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [W, H] = react__WEBPACK_IMPORTED_MODULE_0__.useState(T !== \"system\" ? T :  false ? 0 : \"light\"), A = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), mt = f.join(\"+\").replace(/Key/g, \"\").replace(/Digit/g, \"\"), L = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), z = react__WEBPACK_IMPORTED_MODULE_0__.useRef(!1), ct = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((d)=>{\n        s((h)=>{\n            var y;\n            return (y = h.find((R)=>R.id === d.id)) != null && y.delete || v.dismiss(d.id), h.filter(({ id: R })=>R !== d.id);\n        });\n    }, []);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>v.subscribe((d)=>{\n            if (d.dismiss) {\n                s((h)=>h.map((y)=>y.id === d.id ? {\n                            ...y,\n                            delete: !0\n                        } : y));\n                return;\n            }\n            setTimeout(()=>{\n                react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(()=>{\n                    s((h)=>{\n                        let y = h.findIndex((R)=>R.id === d.id);\n                        return y !== -1 ? [\n                            ...h.slice(0, y),\n                            {\n                                ...h[y],\n                                ...d\n                            },\n                            ...h.slice(y + 1)\n                        ] : [\n                            d,\n                            ...h\n                        ];\n                    });\n                });\n            });\n        }), []), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (T !== \"system\") {\n            H(T);\n            return;\n        }\n        if (T === \"system\" && (window.matchMedia && window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? H(\"dark\") : H(\"light\")), \"undefined\" == \"undefined\") return;\n        let d = window.matchMedia(\"(prefers-color-scheme: dark)\");\n        try {\n            d.addEventListener(\"change\", ({ matches: h })=>{\n                H(h ? \"dark\" : \"light\");\n            });\n        } catch (h) {\n            d.addListener(({ matches: y })=>{\n                try {\n                    H(y ? \"dark\" : \"light\");\n                } catch (R) {\n                    console.error(R);\n                }\n            });\n        }\n    }, [\n        T\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        B.length <= 1 && C(!1);\n    }, [\n        B\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        let d = (h)=>{\n            var R, j;\n            f.every((p)=>h[p] || h.code === p) && (C(!0), (R = A.current) == null || R.focus()), h.code === \"Escape\" && (document.activeElement === A.current || (j = A.current) != null && j.contains(document.activeElement)) && C(!1);\n        };\n        return document.addEventListener(\"keydown\", d), ()=>document.removeEventListener(\"keydown\", d);\n    }, [\n        f\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (A.current) return ()=>{\n            L.current && (L.current.focus({\n                preventScroll: !0\n            }), L.current = null, z.current = !1);\n        };\n    }, [\n        A.current\n    ]), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"section\", {\n        ref: t,\n        \"aria-label\": `${pt} ${mt}`,\n        tabIndex: -1,\n        \"aria-live\": \"polite\",\n        \"aria-relevant\": \"additions text\",\n        \"aria-atomic\": \"false\",\n        suppressHydrationWarning: !0\n    }, P.map((d, h)=>{\n        var j;\n        let [y, R] = d.split(\"-\");\n        return B.length ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"ol\", {\n            key: d,\n            dir: ot === \"auto\" ? _t() : ot,\n            tabIndex: -1,\n            ref: A,\n            className: g,\n            \"data-sonner-toaster\": !0,\n            \"data-theme\": W,\n            \"data-y-position\": y,\n            \"data-lifted\": Y && B.length > 1 && !w,\n            \"data-x-position\": R,\n            style: {\n                \"--front-toast-height\": `${((j = nt[0]) == null ? void 0 : j.height) || 0}px`,\n                \"--width\": `${he}px`,\n                \"--gap\": `${at}px`,\n                ...ut,\n                ...Te(i, D)\n            },\n            onBlur: (p)=>{\n                z.current && !p.currentTarget.contains(p.relatedTarget) && (z.current = !1, L.current && (L.current.focus({\n                    preventScroll: !0\n                }), L.current = null));\n            },\n            onFocus: (p)=>{\n                p.target instanceof HTMLElement && p.target.dataset.dismissible === \"false\" || z.current || (z.current = !0, L.current = p.relatedTarget);\n            },\n            onMouseEnter: ()=>C(!0),\n            onMouseMove: ()=>C(!0),\n            onMouseLeave: ()=>{\n                lt || C(!1);\n            },\n            onDragEnd: ()=>C(!1),\n            onPointerDown: (p)=>{\n                p.target instanceof HTMLElement && p.target.dataset.dismissible === \"false\" || J(!0);\n            },\n            onPointerUp: ()=>J(!1)\n        }, B.filter((p)=>!p.position && h === 0 || p.position === d).map((p, _)=>{\n            var O, G;\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(ve, {\n                key: p.id,\n                icons: st,\n                index: _,\n                toast: p,\n                defaultRichColors: F,\n                duration: (O = l == null ? void 0 : l.duration) != null ? O : et,\n                className: l == null ? void 0 : l.className,\n                descriptionClassName: l == null ? void 0 : l.descriptionClassName,\n                invert: a,\n                visibleToasts: ft,\n                closeButton: (G = l == null ? void 0 : l.closeButton) != null ? G : S,\n                interacting: lt,\n                position: d,\n                style: l == null ? void 0 : l.style,\n                unstyled: l == null ? void 0 : l.unstyled,\n                classNames: l == null ? void 0 : l.classNames,\n                cancelButtonStyle: l == null ? void 0 : l.cancelButtonStyle,\n                actionButtonStyle: l == null ? void 0 : l.actionButtonStyle,\n                removeToast: ct,\n                toasts: B.filter((k)=>k.position == p.position),\n                heights: nt.filter((k)=>k.position == p.position),\n                setHeights: it,\n                expandByDefault: w,\n                gap: at,\n                loadingIcon: X,\n                expanded: Y,\n                pauseWhenPageIsHidden: rt,\n                swipeDirections: e.swipeDirections\n            });\n        })) : null;\n    }));\n});\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/sonner/dist/index.mjs\n");

/***/ })

};
;