"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/trackSheets/v2/page",{

/***/ "(app-pages-browser)/./app/user/trackSheets/v2/ManageTrackSheet.tsx":
/*!******************************************************!*\
  !*** ./app/user/trackSheets/v2/ManageTrackSheet.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _createTrackSheet__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./createTrackSheet */ \"(app-pages-browser)/./app/user/trackSheets/v2/createTrackSheet.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _TrackSheetContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./TrackSheetContext */ \"(app-pages-browser)/./app/user/trackSheets/v2/TrackSheetContext.tsx\");\n/* harmony import */ var _ClientSelectPage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ClientSelectPage */ \"(app-pages-browser)/./app/user/trackSheets/v2/ClientSelectPage.tsx\");\n/* harmony import */ var _components_sidebar_Sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/sidebar/Sidebar */ \"(app-pages-browser)/./components/sidebar/Sidebar.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./components/ui/sidebar.tsx\");\n/* harmony import */ var _app_component_BreadCrumbs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/_component/BreadCrumbs */ \"(app-pages-browser)/./app/_component/BreadCrumbs.tsx\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./components/ui/form.tsx\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/_component/SearchSelect */ \"(app-pages-browser)/./app/_component/SearchSelect.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst ManageWorkSheet = (param)=>{\n    let { permissions, client, carrier, associate, userData, actions } = param;\n    _s();\n    const [filterdata, setFilterData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [deleteData, setDeletedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [recievedFDate, setRecievedFDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [recievedTDate, setRecievedTDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [invoiceFDate, setInvoiceFDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [invoiceTDate, setInvoiceTDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [shipmentFDate, setShipmentFDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [shipmentTDate, setShipmentTDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [activeView, setActiveView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"view\"); // Default to view mode\n    // Add these states for the selection form\n    const [showFullForm, setShowFullForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [initialAssociateId, setInitialAssociateId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [initialClientId, setInitialClientId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const selectionForm = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_11__.useForm)({\n        defaultValues: {\n            associateId: \"\",\n            clientId: \"\"\n        }\n    });\n    const associateOptions = associate === null || associate === void 0 ? void 0 : associate.map((a)=>{\n        var _a_id;\n        return {\n            value: (_a_id = a.id) === null || _a_id === void 0 ? void 0 : _a_id.toString(),\n            label: a.name,\n            name: a.name\n        };\n    });\n    const getFilteredClientOptions = ()=>{\n        if (!initialAssociateId) {\n            return (client === null || client === void 0 ? void 0 : client.map((c)=>{\n                var _c_id;\n                return {\n                    value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n                    label: c.client_name,\n                    name: c.client_name\n                };\n            })) || [];\n        }\n        const filteredClients = (client === null || client === void 0 ? void 0 : client.filter((c)=>{\n            var _c_associateId;\n            return ((_c_associateId = c.associateId) === null || _c_associateId === void 0 ? void 0 : _c_associateId.toString()) === initialAssociateId;\n        })) || [];\n        return filteredClients.map((c)=>{\n            var _c_id;\n            return {\n                value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n                label: c.client_name,\n                name: c.client_name\n            };\n        });\n    };\n    const clientOptions = getFilteredClientOptions();\n    const validateClientForAssociate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((associateId, currentClientId)=>{\n        if (associateId && currentClientId) {\n            var _currentClient_associateId;\n            const currentClient = client === null || client === void 0 ? void 0 : client.find((c)=>{\n                var _c_id;\n                return ((_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString()) === currentClientId;\n            });\n            if (currentClient && ((_currentClient_associateId = currentClient.associateId) === null || _currentClient_associateId === void 0 ? void 0 : _currentClient_associateId.toString()) !== associateId) {\n                selectionForm.setValue(\"clientId\", \"\");\n                setInitialClientId(\"\");\n                return false;\n            }\n        }\n        return true;\n    }, [\n        client,\n        selectionForm\n    ]);\n    const clearEntrySpecificClients = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n    // This is a placeholder - the actual implementation would be in CreateTrackSheet\n    // We'll pass this as a prop to CreateTrackSheet\n    }, []);\n    const handleInitialSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((associateId, clientId)=>{\n        // This is a placeholder - the actual implementation would be in CreateTrackSheet\n        // We'll pass these values as props to CreateTrackSheet\n        setShowFullForm(true);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TrackSheetContext__WEBPACK_IMPORTED_MODULE_4__.TrackSheetContext.Provider, {\n        value: {\n            filterdata,\n            setFilterData,\n            deleteData,\n            setDeletedData,\n            recievedFDate,\n            recievedTDate,\n            setRecievedFDate,\n            setRecievedTDate,\n            invoiceFDate,\n            setInvoiceFDate,\n            invoiceTDate,\n            setInvoiceTDate,\n            shipmentFDate,\n            setShipmentFDate,\n            shipmentTDate,\n            setShipmentTDate\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_7__.SidebarProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex w-full min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sidebar_Sidebar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        permissions: permissions,\n                        profile: userData\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 w-full pl-3 overflow-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_BreadCrumbs__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    breadcrumblist: [\n                                        {\n                                            link: \"/user/trackSheets\",\n                                            name: \"TrackSheet\"\n                                        }\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4 pl-3 mb-\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"default\",\n                                        onClick: ()=>setActiveView(\"view\"),\n                                        className: \"w-40 shadow-md rounded-xl text-base transition-all duration-200 \".concat(activeView === \"view\" ? \"bg-neutral-800 hover:bg-neutral-900 text-white\" : \"bg-white text-neutral-800 border border-neutral-300 hover:bg-neutral-100\"),\n                                        children: \"View TrackSheet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"default\",\n                                        onClick: ()=>setActiveView(\"create\"),\n                                        className: \"w-40 shadow-md rounded-xl text-base transition-all duration-200 \".concat(activeView === \"create\" ? \"bg-neutral-800 hover:bg-neutral-900 text-white\" : \"bg-white text-neutral-800 border border-neutral-300 hover:bg-neutral-100\"),\n                                        children: \"Create TrackSheet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    activeView === \"create\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 bg-white rounded-lg shadow-sm border border-gray-200 p-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_9__.Form, {\n                                            ...selectionForm,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            form: selectionForm,\n                                                            name: \"associateId\",\n                                                            label: \"Select Associate\",\n                                                            placeholder: \"Search Associate...\",\n                                                            isRequired: true,\n                                                            options: associateOptions || [],\n                                                            onValueChange: (value)=>{\n                                                                setInitialAssociateId(value);\n                                                                if (value && initialClientId) {\n                                                                    validateClientForAssociate(value, initialClientId);\n                                                                } else {\n                                                                    setInitialClientId(\"\");\n                                                                    selectionForm.setValue(\"clientId\", \"\");\n                                                                }\n                                                                setShowFullForm(false);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            form: selectionForm,\n                                                            name: \"clientId\",\n                                                            label: \"Select Client\",\n                                                            placeholder: \"Search Client...\",\n                                                            isRequired: true,\n                                                            disabled: !initialAssociateId,\n                                                            options: clientOptions || [],\n                                                            onValueChange: (value)=>{\n                                                                setInitialClientId(value);\n                                                                if (showFullForm) {\n                                                                    clearEntrySpecificClients();\n                                                                }\n                                                                if (value && initialAssociateId) {\n                                                                    setTimeout(()=>{\n                                                                        handleInitialSelection(initialAssociateId, value);\n                                                                    }, 100);\n                                                                } else {\n                                                                    setShowFullForm(false);\n                                                                }\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full animate-in fade-in duration-500 rounded-2xl shadow-sm dark:bg-gray-800 p-1\",\n                                children: activeView === \"create\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_createTrackSheet__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    client: client,\n                                    carrier: carrier,\n                                    associate: associate,\n                                    userData: userData,\n                                    initialAssociateId: initialAssociateId,\n                                    initialClientId: initialClientId,\n                                    showFullForm: showFullForm\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClientSelectPage__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    permissions: actions,\n                                    client: client\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                lineNumber: 119,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n            lineNumber: 118,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ManageWorkSheet, \"N8CE5J0ob4eJypEFhc5HP89xZbQ=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_11__.useForm\n    ];\n});\n_c = ManageWorkSheet;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ManageWorkSheet);\nvar _c;\n$RefreshReg$(_c, \"ManageWorkSheet\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC91c2VyL3RyYWNrU2hlZXRzL3YyL01hbmFnZVRyYWNrU2hlZXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDcUQ7QUFDSDtBQUNGO0FBRVE7QUFDTjtBQUNDO0FBQ087QUFDSDtBQUVYO0FBQ0Y7QUFDZTtBQUV6RCxNQUFNYSxrQkFBa0I7UUFBQyxFQUN2QkMsV0FBVyxFQUNYQyxNQUFNLEVBQ05DLE9BQU8sRUFDUEMsU0FBUyxFQUNUQyxRQUFRLEVBQ1JDLE9BQU8sRUFDSDs7SUFDSixNQUFNLENBQUNDLFlBQVlDLGNBQWMsR0FBR3BCLCtDQUFRQSxDQUFDLEVBQUU7SUFDL0MsTUFBTSxDQUFDcUIsWUFBWUMsZUFBZSxHQUFHdEIsK0NBQVFBLENBQUM7SUFDOUMsTUFBTSxDQUFDdUIsZUFBZUMsaUJBQWlCLEdBQUd4QiwrQ0FBUUEsQ0FBQztJQUNuRCxNQUFNLENBQUN5QixlQUFlQyxpQkFBaUIsR0FBRzFCLCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQzJCLGNBQWNDLGdCQUFnQixHQUFHNUIsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDNkIsY0FBY0MsZ0JBQWdCLEdBQUc5QiwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUMrQixlQUFlQyxpQkFBaUIsR0FBR2hDLCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQ2lDLGVBQWVDLGlCQUFpQixHQUFHbEMsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDbUMsWUFBWUMsY0FBYyxHQUFHcEMsK0NBQVFBLENBQUMsU0FBUyx1QkFBdUI7SUFFN0UsMENBQTBDO0lBQzFDLE1BQU0sQ0FBQ3FDLGNBQWNDLGdCQUFnQixHQUFHdEMsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDdUMsb0JBQW9CQyxzQkFBc0IsR0FBR3hDLCtDQUFRQSxDQUFDO0lBQzdELE1BQU0sQ0FBQ3lDLGlCQUFpQkMsbUJBQW1CLEdBQUcxQywrQ0FBUUEsQ0FBQztJQUV2RCxNQUFNMkMsZ0JBQWdCakMseURBQU9BLENBQUM7UUFDNUJrQyxlQUFlO1lBQ2JDLGFBQWE7WUFDYkMsVUFBVTtRQUNaO0lBQ0Y7SUFFQSxNQUFNQyxtQkFBbUIvQixzQkFBQUEsZ0NBQUFBLFVBQVdnQyxHQUFHLENBQUMsQ0FBQ0M7WUFDaENBO2VBRDRDO1lBQ25EQyxLQUFLLEdBQUVELFFBQUFBLEVBQUVFLEVBQUUsY0FBSkYsNEJBQUFBLE1BQU1HLFFBQVE7WUFDckJDLE9BQU9KLEVBQUVLLElBQUk7WUFDYkEsTUFBTUwsRUFBRUssSUFBSTtRQUNkOztJQUVBLE1BQU1DLDJCQUEyQjtRQUMvQixJQUFJLENBQUNoQixvQkFBb0I7WUFDdkIsT0FBT3pCLENBQUFBLG1CQUFBQSw2QkFBQUEsT0FBUWtDLEdBQUcsQ0FBQyxDQUFDUTtvQkFDWEE7dUJBRHVCO29CQUM5Qk4sS0FBSyxHQUFFTSxRQUFBQSxFQUFFTCxFQUFFLGNBQUpLLDRCQUFBQSxNQUFNSixRQUFRO29CQUNyQkMsT0FBT0csRUFBRUMsV0FBVztvQkFDcEJILE1BQU1FLEVBQUVDLFdBQVc7Z0JBQ3JCO21CQUFPLEVBQUU7UUFDWDtRQUVBLE1BQU1DLGtCQUFrQjVDLENBQUFBLG1CQUFBQSw2QkFBQUEsT0FBUTZDLE1BQU0sQ0FBQyxDQUFDSDtnQkFDdENBO21CQUFBQSxFQUFBQSxpQkFBQUEsRUFBRVgsV0FBVyxjQUFiVyxxQ0FBQUEsZUFBZUosUUFBUSxRQUFPYjtlQUMzQixFQUFFO1FBRVAsT0FBT21CLGdCQUFnQlYsR0FBRyxDQUFDLENBQUNRO2dCQUNuQkE7bUJBRCtCO2dCQUN0Q04sS0FBSyxHQUFFTSxRQUFBQSxFQUFFTCxFQUFFLGNBQUpLLDRCQUFBQSxNQUFNSixRQUFRO2dCQUNyQkMsT0FBT0csRUFBRUMsV0FBVztnQkFDcEJILE1BQU1FLEVBQUVDLFdBQVc7WUFDckI7O0lBQ0Y7SUFFQSxNQUFNRyxnQkFBZ0JMO0lBRXRCLE1BQU1NLDZCQUE2QjVELGtEQUFXQSxDQUFDLENBQUM0QyxhQUFxQmlCO1FBQ25FLElBQUlqQixlQUFlaUIsaUJBQWlCO2dCQUViQztZQURyQixNQUFNQSxnQkFBZ0JqRCxtQkFBQUEsNkJBQUFBLE9BQVFrRCxJQUFJLENBQUMsQ0FBQ1I7b0JBQVdBO3VCQUFBQSxFQUFBQSxRQUFBQSxFQUFFTCxFQUFFLGNBQUpLLDRCQUFBQSxNQUFNSixRQUFRLFFBQU9VOztZQUNwRSxJQUFJQyxpQkFBaUJBLEVBQUFBLDZCQUFBQSxjQUFjbEIsV0FBVyxjQUF6QmtCLGlEQUFBQSwyQkFBMkJYLFFBQVEsUUFBT1AsYUFBYTtnQkFDMUVGLGNBQWNzQixRQUFRLENBQUMsWUFBWTtnQkFDbkN2QixtQkFBbUI7Z0JBQ25CLE9BQU87WUFDVDtRQUNGO1FBQ0EsT0FBTztJQUNULEdBQUc7UUFBQzVCO1FBQVE2QjtLQUFjO0lBRTFCLE1BQU11Qiw0QkFBNEJqRSxrREFBV0EsQ0FBQztJQUM1QyxpRkFBaUY7SUFDakYsZ0RBQWdEO0lBQ2xELEdBQUcsRUFBRTtJQUVMLE1BQU1rRSx5QkFBeUJsRSxrREFBV0EsQ0FBQyxDQUFDNEMsYUFBcUJDO1FBQy9ELGlGQUFpRjtRQUNqRix1REFBdUQ7UUFDdkRSLGdCQUFnQjtJQUNsQixHQUFHLEVBQUU7SUFFTCxxQkFDRSw4REFBQ2xDLGlFQUFpQkEsQ0FBQ2dFLFFBQVE7UUFDekJsQixPQUFPO1lBQ0wvQjtZQUNBQztZQUNBQztZQUNBQztZQUNBQztZQUNBRTtZQUNBRDtZQUNBRTtZQUNBQztZQUNBQztZQUNBQztZQUNBQztZQUNBQztZQUNBQztZQUNBQztZQUNBQztRQUNGO2tCQUVBLDRFQUFDM0IsbUVBQWVBO3NCQUNkLDRFQUFDOEQ7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDaEUsbUVBQU9BO3dCQUFDTyxhQUFhQTt3QkFBYTBELFNBQVN0RDs7Ozs7O2tDQUM1Qyw4REFBQ3VEO3dCQUFLRixXQUFVOzswQ0FDZCw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUM5RCxrRUFBV0E7b0NBQ1ZpRSxnQkFBZ0I7d0NBQ2Q7NENBQUVDLE1BQU07NENBQXFCcEIsTUFBTTt3Q0FBYTtxQ0FDakQ7Ozs7Ozs7Ozs7OzBDQUtMLDhEQUFDZTtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNuRSx5REFBTUE7d0NBQ0x3RSxTQUFRO3dDQUNSQyxTQUFTLElBQU14QyxjQUFjO3dDQUM3QmtDLFdBQVcsbUVBSVYsT0FIQ25DLGVBQWUsU0FDWCxtREFDQTtrREFFUDs7Ozs7O2tEQUdELDhEQUFDaEMseURBQU1BO3dDQUNMd0UsU0FBUTt3Q0FDUkMsU0FBUyxJQUFNeEMsY0FBYzt3Q0FDN0JrQyxXQUFXLG1FQUlWLE9BSENuQyxlQUFlLFdBQ1gsbURBQ0E7a0RBRVA7Ozs7OztvQ0FLQUEsZUFBZSwwQkFDZCw4REFBQ2tDO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDN0QscURBQUlBOzRDQUFFLEdBQUdrQyxhQUFhO3NEQUNyQiw0RUFBQzBCO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7d0RBQUlDLFdBQVU7a0VBQ2IsNEVBQUMzRCxvRUFBWUE7NERBQ1hrRSxNQUFNbEM7NERBQ05XLE1BQUs7NERBQ0xELE9BQU07NERBQ055QixhQUFZOzREQUNaQyxVQUFVOzREQUNWQyxTQUFTakMsb0JBQW9CLEVBQUU7NERBQy9Ca0MsZUFBZSxDQUFDL0I7Z0VBQ2RWLHNCQUFzQlU7Z0VBRXRCLElBQUlBLFNBQVNULGlCQUFpQjtvRUFDNUJvQiwyQkFBMkJYLE9BQU9UO2dFQUNwQyxPQUFPO29FQUNMQyxtQkFBbUI7b0VBQ25CQyxjQUFjc0IsUUFBUSxDQUFDLFlBQVk7Z0VBQ3JDO2dFQUNBM0IsZ0JBQWdCOzREQUNsQjs7Ozs7Ozs7Ozs7a0VBSUosOERBQUMrQjt3REFBSUMsV0FBVTtrRUFDYiw0RUFBQzNELG9FQUFZQTs0REFDWGtFLE1BQU1sQzs0REFDTlcsTUFBSzs0REFDTEQsT0FBTTs0REFDTnlCLGFBQVk7NERBQ1pDLFVBQVU7NERBQ1ZHLFVBQVUsQ0FBQzNDOzREQUNYeUMsU0FBU3BCLGlCQUFpQixFQUFFOzREQUM1QnFCLGVBQWUsQ0FBQy9CO2dFQUNkUixtQkFBbUJRO2dFQUVuQixJQUFJYixjQUFjO29FQUNoQjZCO2dFQUNGO2dFQUVBLElBQUloQixTQUFTWCxvQkFBb0I7b0VBQy9CNEMsV0FBVzt3RUFDVGhCLHVCQUF1QjVCLG9CQUFvQlc7b0VBQzdDLEdBQUc7Z0VBQ0wsT0FBTztvRUFDTFosZ0JBQWdCO2dFQUNsQjs0REFDRjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQVNkLDhEQUFDK0I7Z0NBQUlDLFdBQVU7MENBQ1puQyxlQUFlLHlCQUNkLDhEQUFDakMseURBQWdCQTtvQ0FDZlksUUFBUUE7b0NBQ1JDLFNBQVNBO29DQUNUQyxXQUFXQTtvQ0FDWEMsVUFBVUE7b0NBQ1ZzQixvQkFBb0JBO29DQUNwQkUsaUJBQWlCQTtvQ0FDakJKLGNBQWNBOzs7Ozs4REFHaEIsOERBQUNoQyx5REFBZ0JBO29DQUNmUSxhQUFhSztvQ0FDYkosUUFBUUE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVMxQjtHQTdOTUY7O1FBdUJrQkYscURBQU9BOzs7S0F2QnpCRTtBQStOTiwrREFBZUEsZUFBZUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9hcHAvdXNlci90cmFja1NoZWV0cy92Mi9NYW5hZ2VUcmFja1NoZWV0LnRzeD9iMDNlIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xyXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUNhbGxiYWNrIH0gZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCBDcmVhdGVUcmFja1NoZWV0IGZyb20gXCIuL2NyZWF0ZVRyYWNrU2hlZXRcIjtcclxuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9idXR0b25cIjtcclxuaW1wb3J0IHsgdXNlU2VhcmNoUGFyYW1zIH0gZnJvbSBcIm5leHQvbmF2aWdhdGlvblwiO1xyXG5pbXBvcnQgeyBUcmFja1NoZWV0Q29udGV4dCB9IGZyb20gXCIuL1RyYWNrU2hlZXRDb250ZXh0XCI7XHJcbmltcG9ydCBDbGllbnRTZWxlY3RQYWdlIGZyb20gXCIuL0NsaWVudFNlbGVjdFBhZ2VcIjtcclxuaW1wb3J0IFNpZGViYXIgZnJvbSBcIkAvY29tcG9uZW50cy9zaWRlYmFyL1NpZGViYXJcIjtcclxuaW1wb3J0IHsgU2lkZWJhclByb3ZpZGVyIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9zaWRlYmFyXCI7XHJcbmltcG9ydCBCcmVhZENydW1icyBmcm9tIFwiQC9hcHAvX2NvbXBvbmVudC9CcmVhZENydW1ic1wiO1xyXG5pbXBvcnQgeyBCdWlsZGluZzIgfSBmcm9tIFwibHVjaWRlLXJlYWN0XCI7XHJcbmltcG9ydCB7IEZvcm0gfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2Zvcm1cIjtcclxuaW1wb3J0IHsgdXNlRm9ybSB9IGZyb20gXCJyZWFjdC1ob29rLWZvcm1cIjtcclxuaW1wb3J0IFNlYXJjaFNlbGVjdCBmcm9tIFwiQC9hcHAvX2NvbXBvbmVudC9TZWFyY2hTZWxlY3RcIjtcclxuXHJcbmNvbnN0IE1hbmFnZVdvcmtTaGVldCA9ICh7XHJcbiAgcGVybWlzc2lvbnMsXHJcbiAgY2xpZW50LFxyXG4gIGNhcnJpZXIsXHJcbiAgYXNzb2NpYXRlLFxyXG4gIHVzZXJEYXRhLFxyXG4gIGFjdGlvbnMsXHJcbn06IGFueSkgPT4ge1xyXG4gIGNvbnN0IFtmaWx0ZXJkYXRhLCBzZXRGaWx0ZXJEYXRhXSA9IHVzZVN0YXRlKFtdKTtcclxuICBjb25zdCBbZGVsZXRlRGF0YSwgc2V0RGVsZXRlZERhdGFdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtyZWNpZXZlZEZEYXRlLCBzZXRSZWNpZXZlZEZEYXRlXSA9IHVzZVN0YXRlKFwiXCIpO1xyXG4gIGNvbnN0IFtyZWNpZXZlZFREYXRlLCBzZXRSZWNpZXZlZFREYXRlXSA9IHVzZVN0YXRlKFwiXCIpO1xyXG4gIGNvbnN0IFtpbnZvaWNlRkRhdGUsIHNldEludm9pY2VGRGF0ZV0gPSB1c2VTdGF0ZShcIlwiKTtcclxuICBjb25zdCBbaW52b2ljZVREYXRlLCBzZXRJbnZvaWNlVERhdGVdID0gdXNlU3RhdGUoXCJcIik7XHJcbiAgY29uc3QgW3NoaXBtZW50RkRhdGUsIHNldFNoaXBtZW50RkRhdGVdID0gdXNlU3RhdGUoXCJcIik7XHJcbiAgY29uc3QgW3NoaXBtZW50VERhdGUsIHNldFNoaXBtZW50VERhdGVdID0gdXNlU3RhdGUoXCJcIik7XHJcbiAgY29uc3QgW2FjdGl2ZVZpZXcsIHNldEFjdGl2ZVZpZXddID0gdXNlU3RhdGUoXCJ2aWV3XCIpOyAvLyBEZWZhdWx0IHRvIHZpZXcgbW9kZVxyXG4gIFxyXG4gIC8vIEFkZCB0aGVzZSBzdGF0ZXMgZm9yIHRoZSBzZWxlY3Rpb24gZm9ybVxyXG4gIGNvbnN0IFtzaG93RnVsbEZvcm0sIHNldFNob3dGdWxsRm9ybV0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2luaXRpYWxBc3NvY2lhdGVJZCwgc2V0SW5pdGlhbEFzc29jaWF0ZUlkXSA9IHVzZVN0YXRlKFwiXCIpO1xyXG4gIGNvbnN0IFtpbml0aWFsQ2xpZW50SWQsIHNldEluaXRpYWxDbGllbnRJZF0gPSB1c2VTdGF0ZShcIlwiKTtcclxuXHJcbiAgY29uc3Qgc2VsZWN0aW9uRm9ybSA9IHVzZUZvcm0oe1xyXG4gICAgZGVmYXVsdFZhbHVlczoge1xyXG4gICAgICBhc3NvY2lhdGVJZDogXCJcIixcclxuICAgICAgY2xpZW50SWQ6IFwiXCIsXHJcbiAgICB9LFxyXG4gIH0pO1xyXG5cclxuICBjb25zdCBhc3NvY2lhdGVPcHRpb25zID0gYXNzb2NpYXRlPy5tYXAoKGE6IGFueSkgPT4gKHtcclxuICAgIHZhbHVlOiBhLmlkPy50b1N0cmluZygpLFxyXG4gICAgbGFiZWw6IGEubmFtZSxcclxuICAgIG5hbWU6IGEubmFtZSxcclxuICB9KSk7XHJcblxyXG4gIGNvbnN0IGdldEZpbHRlcmVkQ2xpZW50T3B0aW9ucyA9ICgpID0+IHtcclxuICAgIGlmICghaW5pdGlhbEFzc29jaWF0ZUlkKSB7XHJcbiAgICAgIHJldHVybiBjbGllbnQ/Lm1hcCgoYzogYW55KSA9PiAoe1xyXG4gICAgICAgIHZhbHVlOiBjLmlkPy50b1N0cmluZygpLFxyXG4gICAgICAgIGxhYmVsOiBjLmNsaWVudF9uYW1lLFxyXG4gICAgICAgIG5hbWU6IGMuY2xpZW50X25hbWUsXHJcbiAgICAgIH0pKSB8fCBbXTtcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBmaWx0ZXJlZENsaWVudHMgPSBjbGllbnQ/LmZpbHRlcigoYzogYW55KSA9PlxyXG4gICAgICBjLmFzc29jaWF0ZUlkPy50b1N0cmluZygpID09PSBpbml0aWFsQXNzb2NpYXRlSWRcclxuICAgICkgfHwgW107XHJcblxyXG4gICAgcmV0dXJuIGZpbHRlcmVkQ2xpZW50cy5tYXAoKGM6IGFueSkgPT4gKHtcclxuICAgICAgdmFsdWU6IGMuaWQ/LnRvU3RyaW5nKCksXHJcbiAgICAgIGxhYmVsOiBjLmNsaWVudF9uYW1lLFxyXG4gICAgICBuYW1lOiBjLmNsaWVudF9uYW1lLFxyXG4gICAgfSkpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGNsaWVudE9wdGlvbnMgPSBnZXRGaWx0ZXJlZENsaWVudE9wdGlvbnMoKTtcclxuXHJcbiAgY29uc3QgdmFsaWRhdGVDbGllbnRGb3JBc3NvY2lhdGUgPSB1c2VDYWxsYmFjaygoYXNzb2NpYXRlSWQ6IHN0cmluZywgY3VycmVudENsaWVudElkOiBzdHJpbmcpID0+IHtcclxuICAgIGlmIChhc3NvY2lhdGVJZCAmJiBjdXJyZW50Q2xpZW50SWQpIHtcclxuICAgICAgY29uc3QgY3VycmVudENsaWVudCA9IGNsaWVudD8uZmluZCgoYzogYW55KSA9PiBjLmlkPy50b1N0cmluZygpID09PSBjdXJyZW50Q2xpZW50SWQpO1xyXG4gICAgICBpZiAoY3VycmVudENsaWVudCAmJiBjdXJyZW50Q2xpZW50LmFzc29jaWF0ZUlkPy50b1N0cmluZygpICE9PSBhc3NvY2lhdGVJZCkge1xyXG4gICAgICAgIHNlbGVjdGlvbkZvcm0uc2V0VmFsdWUoXCJjbGllbnRJZFwiLCBcIlwiKTtcclxuICAgICAgICBzZXRJbml0aWFsQ2xpZW50SWQoXCJcIik7XHJcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgICByZXR1cm4gdHJ1ZTtcclxuICB9LCBbY2xpZW50LCBzZWxlY3Rpb25Gb3JtXSk7XHJcblxyXG4gIGNvbnN0IGNsZWFyRW50cnlTcGVjaWZpY0NsaWVudHMgPSB1c2VDYWxsYmFjaygoKSA9PiB7XHJcbiAgICAvLyBUaGlzIGlzIGEgcGxhY2Vob2xkZXIgLSB0aGUgYWN0dWFsIGltcGxlbWVudGF0aW9uIHdvdWxkIGJlIGluIENyZWF0ZVRyYWNrU2hlZXRcclxuICAgIC8vIFdlJ2xsIHBhc3MgdGhpcyBhcyBhIHByb3AgdG8gQ3JlYXRlVHJhY2tTaGVldFxyXG4gIH0sIFtdKTtcclxuXHJcbiAgY29uc3QgaGFuZGxlSW5pdGlhbFNlbGVjdGlvbiA9IHVzZUNhbGxiYWNrKChhc3NvY2lhdGVJZDogc3RyaW5nLCBjbGllbnRJZDogc3RyaW5nKSA9PiB7XHJcbiAgICAvLyBUaGlzIGlzIGEgcGxhY2Vob2xkZXIgLSB0aGUgYWN0dWFsIGltcGxlbWVudGF0aW9uIHdvdWxkIGJlIGluIENyZWF0ZVRyYWNrU2hlZXRcclxuICAgIC8vIFdlJ2xsIHBhc3MgdGhlc2UgdmFsdWVzIGFzIHByb3BzIHRvIENyZWF0ZVRyYWNrU2hlZXRcclxuICAgIHNldFNob3dGdWxsRm9ybSh0cnVlKTtcclxuICB9LCBbXSk7XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8VHJhY2tTaGVldENvbnRleHQuUHJvdmlkZXJcclxuICAgICAgdmFsdWU9e3tcclxuICAgICAgICBmaWx0ZXJkYXRhLFxyXG4gICAgICAgIHNldEZpbHRlckRhdGEsXHJcbiAgICAgICAgZGVsZXRlRGF0YSxcclxuICAgICAgICBzZXREZWxldGVkRGF0YSxcclxuICAgICAgICByZWNpZXZlZEZEYXRlLFxyXG4gICAgICAgIHJlY2lldmVkVERhdGUsXHJcbiAgICAgICAgc2V0UmVjaWV2ZWRGRGF0ZSxcclxuICAgICAgICBzZXRSZWNpZXZlZFREYXRlLFxyXG4gICAgICAgIGludm9pY2VGRGF0ZSxcclxuICAgICAgICBzZXRJbnZvaWNlRkRhdGUsXHJcbiAgICAgICAgaW52b2ljZVREYXRlLFxyXG4gICAgICAgIHNldEludm9pY2VURGF0ZSxcclxuICAgICAgICBzaGlwbWVudEZEYXRlLFxyXG4gICAgICAgIHNldFNoaXBtZW50RkRhdGUsXHJcbiAgICAgICAgc2hpcG1lbnRURGF0ZSxcclxuICAgICAgICBzZXRTaGlwbWVudFREYXRlLFxyXG4gICAgICB9fVxyXG4gICAgPlxyXG4gICAgICA8U2lkZWJhclByb3ZpZGVyPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCB3LWZ1bGwgbWluLWgtc2NyZWVuXCI+XHJcbiAgICAgICAgICA8U2lkZWJhciBwZXJtaXNzaW9ucz17cGVybWlzc2lvbnN9IHByb2ZpbGU9e3VzZXJEYXRhfSAvPlxyXG4gICAgICAgICAgPG1haW4gY2xhc3NOYW1lPVwiZmxleC0xIHctZnVsbCBwbC0zIG92ZXJmbG93LWF1dG9cIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi0yXCI+XHJcbiAgICAgICAgICAgICAgPEJyZWFkQ3J1bWJzXHJcbiAgICAgICAgICAgICAgICBicmVhZGNydW1ibGlzdD17W1xyXG4gICAgICAgICAgICAgICAgICB7IGxpbms6IFwiL3VzZXIvdHJhY2tTaGVldHNcIiwgbmFtZTogXCJUcmFja1NoZWV0XCIgfSxcclxuICAgICAgICAgICAgICAgIF19XHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICB7LyogVG9nZ2xlIEJ1dHRvbnMgYW5kIFNlbGVjdGlvbiBGb3JtIGluIG9uZSByb3cgKi99XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTQgcGwtMyBtYi1cIj5cclxuICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICB2YXJpYW50PVwiZGVmYXVsdFwiXHJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRBY3RpdmVWaWV3KFwidmlld1wiKX1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctNDAgc2hhZG93LW1kIHJvdW5kZWQteGwgdGV4dC1iYXNlIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCAke1xyXG4gICAgICAgICAgICAgICAgICBhY3RpdmVWaWV3ID09PSBcInZpZXdcIlxyXG4gICAgICAgICAgICAgICAgICAgID8gXCJiZy1uZXV0cmFsLTgwMCBob3ZlcjpiZy1uZXV0cmFsLTkwMCB0ZXh0LXdoaXRlXCJcclxuICAgICAgICAgICAgICAgICAgICA6IFwiYmctd2hpdGUgdGV4dC1uZXV0cmFsLTgwMCBib3JkZXIgYm9yZGVyLW5ldXRyYWwtMzAwIGhvdmVyOmJnLW5ldXRyYWwtMTAwXCJcclxuICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIFZpZXcgVHJhY2tTaGVldFxyXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJkZWZhdWx0XCJcclxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEFjdGl2ZVZpZXcoXCJjcmVhdGVcIil9XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LTQwIHNoYWRvdy1tZCByb3VuZGVkLXhsIHRleHQtYmFzZSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgJHtcclxuICAgICAgICAgICAgICAgICAgYWN0aXZlVmlldyA9PT0gXCJjcmVhdGVcIlxyXG4gICAgICAgICAgICAgICAgICAgID8gXCJiZy1uZXV0cmFsLTgwMCBob3ZlcjpiZy1uZXV0cmFsLTkwMCB0ZXh0LXdoaXRlXCJcclxuICAgICAgICAgICAgICAgICAgICA6IFwiYmctd2hpdGUgdGV4dC1uZXV0cmFsLTgwMCBib3JkZXIgYm9yZGVyLW5ldXRyYWwtMzAwIGhvdmVyOmJnLW5ldXRyYWwtMTAwXCJcclxuICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIENyZWF0ZSBUcmFja1NoZWV0XHJcbiAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgey8qIElubGluZSBTZWxlY3Rpb24gRm9ybSAqL31cclxuICAgICAgICAgICAgICB7YWN0aXZlVmlldyA9PT0gXCJjcmVhdGVcIiAmJiAoXHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1zbSBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHAtM1wiPlxyXG4gICAgICAgICAgICAgICAgICA8Rm9ybSB7Li4uc2VsZWN0aW9uRm9ybX0+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPFNlYXJjaFNlbGVjdFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGZvcm09e3NlbGVjdGlvbkZvcm19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgbmFtZT1cImFzc29jaWF0ZUlkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbD1cIlNlbGVjdCBBc3NvY2lhdGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU2VhcmNoIEFzc29jaWF0ZS4uLlwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaXNSZXF1aXJlZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9wdGlvbnM9e2Fzc29jaWF0ZU9wdGlvbnMgfHwgW119XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25WYWx1ZUNoYW5nZT17KHZhbHVlKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRJbml0aWFsQXNzb2NpYXRlSWQodmFsdWUpO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmICh2YWx1ZSAmJiBpbml0aWFsQ2xpZW50SWQpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsaWRhdGVDbGllbnRGb3JBc3NvY2lhdGUodmFsdWUsIGluaXRpYWxDbGllbnRJZCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRJbml0aWFsQ2xpZW50SWQoXCJcIik7IFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZWxlY3Rpb25Gb3JtLnNldFZhbHVlKFwiY2xpZW50SWRcIiwgXCJcIik7IFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0U2hvd0Z1bGxGb3JtKGZhbHNlKTsgXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxTZWFyY2hTZWxlY3RcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBmb3JtPXtzZWxlY3Rpb25Gb3JtfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG5hbWU9XCJjbGllbnRJZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw9XCJTZWxlY3QgQ2xpZW50XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlNlYXJjaCBDbGllbnQuLi5cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGlzUmVxdWlyZWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17IWluaXRpYWxBc3NvY2lhdGVJZH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBvcHRpb25zPXtjbGllbnRPcHRpb25zIHx8IFtdfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uVmFsdWVDaGFuZ2U9eyh2YWx1ZSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0SW5pdGlhbENsaWVudElkKHZhbHVlKTtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoc2hvd0Z1bGxGb3JtKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsZWFyRW50cnlTcGVjaWZpY0NsaWVudHMoKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAodmFsdWUgJiYgaW5pdGlhbEFzc29jaWF0ZUlkKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZUluaXRpYWxTZWxlY3Rpb24oaW5pdGlhbEFzc29jaWF0ZUlkLCB2YWx1ZSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sIDEwMCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRTaG93RnVsbEZvcm0oZmFsc2UpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPC9Gb3JtPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBhbmltYXRlLWluIGZhZGUtaW4gZHVyYXRpb24tNTAwIHJvdW5kZWQtMnhsIHNoYWRvdy1zbSBkYXJrOmJnLWdyYXktODAwIHAtMVwiPlxyXG4gICAgICAgICAgICAgIHthY3RpdmVWaWV3ID09PSBcImNyZWF0ZVwiID8gKFxyXG4gICAgICAgICAgICAgICAgPENyZWF0ZVRyYWNrU2hlZXQgXHJcbiAgICAgICAgICAgICAgICAgIGNsaWVudD17Y2xpZW50fSBcclxuICAgICAgICAgICAgICAgICAgY2Fycmllcj17Y2Fycmllcn0gXHJcbiAgICAgICAgICAgICAgICAgIGFzc29jaWF0ZT17YXNzb2NpYXRlfSBcclxuICAgICAgICAgICAgICAgICAgdXNlckRhdGE9e3VzZXJEYXRhfVxyXG4gICAgICAgICAgICAgICAgICBpbml0aWFsQXNzb2NpYXRlSWQ9e2luaXRpYWxBc3NvY2lhdGVJZH1cclxuICAgICAgICAgICAgICAgICAgaW5pdGlhbENsaWVudElkPXtpbml0aWFsQ2xpZW50SWR9XHJcbiAgICAgICAgICAgICAgICAgIHNob3dGdWxsRm9ybT17c2hvd0Z1bGxGb3JtfVxyXG4gICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgPENsaWVudFNlbGVjdFBhZ2VcclxuICAgICAgICAgICAgICAgICAgcGVybWlzc2lvbnM9e2FjdGlvbnN9XHJcbiAgICAgICAgICAgICAgICAgIGNsaWVudD17Y2xpZW50fVxyXG4gICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvbWFpbj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9TaWRlYmFyUHJvdmlkZXI+XHJcbiAgICA8L1RyYWNrU2hlZXRDb250ZXh0LlByb3ZpZGVyPlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBNYW5hZ2VXb3JrU2hlZXQ7XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlQ2FsbGJhY2siLCJDcmVhdGVUcmFja1NoZWV0IiwiQnV0dG9uIiwiVHJhY2tTaGVldENvbnRleHQiLCJDbGllbnRTZWxlY3RQYWdlIiwiU2lkZWJhciIsIlNpZGViYXJQcm92aWRlciIsIkJyZWFkQ3J1bWJzIiwiRm9ybSIsInVzZUZvcm0iLCJTZWFyY2hTZWxlY3QiLCJNYW5hZ2VXb3JrU2hlZXQiLCJwZXJtaXNzaW9ucyIsImNsaWVudCIsImNhcnJpZXIiLCJhc3NvY2lhdGUiLCJ1c2VyRGF0YSIsImFjdGlvbnMiLCJmaWx0ZXJkYXRhIiwic2V0RmlsdGVyRGF0YSIsImRlbGV0ZURhdGEiLCJzZXREZWxldGVkRGF0YSIsInJlY2lldmVkRkRhdGUiLCJzZXRSZWNpZXZlZEZEYXRlIiwicmVjaWV2ZWRURGF0ZSIsInNldFJlY2lldmVkVERhdGUiLCJpbnZvaWNlRkRhdGUiLCJzZXRJbnZvaWNlRkRhdGUiLCJpbnZvaWNlVERhdGUiLCJzZXRJbnZvaWNlVERhdGUiLCJzaGlwbWVudEZEYXRlIiwic2V0U2hpcG1lbnRGRGF0ZSIsInNoaXBtZW50VERhdGUiLCJzZXRTaGlwbWVudFREYXRlIiwiYWN0aXZlVmlldyIsInNldEFjdGl2ZVZpZXciLCJzaG93RnVsbEZvcm0iLCJzZXRTaG93RnVsbEZvcm0iLCJpbml0aWFsQXNzb2NpYXRlSWQiLCJzZXRJbml0aWFsQXNzb2NpYXRlSWQiLCJpbml0aWFsQ2xpZW50SWQiLCJzZXRJbml0aWFsQ2xpZW50SWQiLCJzZWxlY3Rpb25Gb3JtIiwiZGVmYXVsdFZhbHVlcyIsImFzc29jaWF0ZUlkIiwiY2xpZW50SWQiLCJhc3NvY2lhdGVPcHRpb25zIiwibWFwIiwiYSIsInZhbHVlIiwiaWQiLCJ0b1N0cmluZyIsImxhYmVsIiwibmFtZSIsImdldEZpbHRlcmVkQ2xpZW50T3B0aW9ucyIsImMiLCJjbGllbnRfbmFtZSIsImZpbHRlcmVkQ2xpZW50cyIsImZpbHRlciIsImNsaWVudE9wdGlvbnMiLCJ2YWxpZGF0ZUNsaWVudEZvckFzc29jaWF0ZSIsImN1cnJlbnRDbGllbnRJZCIsImN1cnJlbnRDbGllbnQiLCJmaW5kIiwic2V0VmFsdWUiLCJjbGVhckVudHJ5U3BlY2lmaWNDbGllbnRzIiwiaGFuZGxlSW5pdGlhbFNlbGVjdGlvbiIsIlByb3ZpZGVyIiwiZGl2IiwiY2xhc3NOYW1lIiwicHJvZmlsZSIsIm1haW4iLCJicmVhZGNydW1ibGlzdCIsImxpbmsiLCJ2YXJpYW50Iiwib25DbGljayIsImZvcm0iLCJwbGFjZWhvbGRlciIsImlzUmVxdWlyZWQiLCJvcHRpb25zIiwib25WYWx1ZUNoYW5nZSIsImRpc2FibGVkIiwic2V0VGltZW91dCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/trackSheets/v2/ManageTrackSheet.tsx\n"));

/***/ })

});