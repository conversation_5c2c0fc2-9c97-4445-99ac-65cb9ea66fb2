"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.viewManualMatchingMappingById = exports.viewManualMatchingMapping = void 0;
const helpers_1 = require("../../../utils/helpers");
const viewManualMatchingMapping = async (req, res) => {
    try {
        const data = await prisma.manualMatchingMapping.findMany({
            orderBy: { id: "desc" },
        });
        if (data) {
            return res.status(200).json(data);
        }
        return res.status(404).json({ message: "ManualMatchingMapping not found" });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.viewManualMatchingMapping = viewManualMatchingMapping;
const viewManualMatchingMappingById = async (req, res) => {
    try {
        const { id } = req.params;
        const data = await prisma.manualMatchingMapping.findUnique({
            where: { id: id },
        });
        if (data) {
            return res.status(200).json(data);
        }
        return res.status(404).json({ message: "ManualMatchingMapping not found" });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.viewManualMatchingMappingById = viewManualMatchingMappingById;
//# sourceMappingURL=view.js.map