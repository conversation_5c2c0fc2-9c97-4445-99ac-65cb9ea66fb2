"use client";
import React, { useState, useCallback, useRef, useMemo } from "react";
import { useForm, useFieldArray, useWatch } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form } from "@/components/ui/form";
import FormInput from "@/app/_component/FormInput";
import FormCheckboxGroup from "@/app/_component/FormCheckboxGroup";
import { Button } from "@/components/ui/button";
import {
  MinusCircle,
  PlusCircle,
  Info,
  DollarSign,
  FileText,
  Building2,
  Hash,
} from "lucide-react";
import { formSubmit, getAllData } from "@/lib/helpers";
import {
  clientCustomFields_routes,
  trackSheets_routes,
  legrandMapping_routes,
} from "@/lib/routePath";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { z } from "zod";
import SearchSelect from "@/app/_component/SearchSelect";
import PageInput from "@/app/_component/PageInput";
import {
  Too<PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import LegrandDetailsComponent from "./LegrandDetailsComponent";

const validateFtpPageFormat = (value: string): boolean => {
  if (!value || value.trim() === "") return false;

  const ftpPageRegex = /^(\d+)\s+of\s+(\d+)$/i;
  const match = value.match(ftpPageRegex);

  if (!match) return false;

  const currentPage = parseInt(match[1], 10);
  const totalPages = parseInt(match[2], 10);

  return currentPage > 0 && totalPages > 0 && currentPage <= totalPages;
};

const trackSheetSchema = z.object({
  clientId: z.string().min(1, "Client is required"),
  entries: z.array(
    z.object({
      company: z.string().min(1, "Company is required"),
      division: z.string().optional(),
      invoice: z.string().min(1, "Invoice is required"),
      masterInvoice: z.string().optional(),
      bol: z.string().min(1, "BOL is required"),
      invoiceDate: z.string().min(1, "Invoice date is required"),
      receivedDate: z.string().min(1, "Received date is required"),
      shipmentDate: z.string().min(1, "Shipment date is required"),
      carrierName: z.string().min(1, "Carrier name is required"),
      invoiceStatus: z.string().min(1, "Invoice status is required"),
      manualMatching: z.string().min(1, "Manual matching is required"),
      invoiceType: z.string().min(1, "Invoice type is required"),
      currency: z.string().min(1, "Currency is required"),
      qtyShipped: z.string().min(1, "Quantity shipped is required"),
      weightUnitName: z.string().min(1, "Weight unit is required"),
      quantityBilledText: z.string().optional(),
      invoiceTotal: z.string().min(1, "Invoice total is required"),
      savings: z.string().optional(),
      ftpFileName: z.string().min(1, "FTP File Name is required"),
      ftpPage: z
        .string()
        .min(1, "FTP Page is required")
        .refine(
          (value) => validateFtpPageFormat(value),
          (value) => {
            if (!value || value.trim() === "") {
              return { message: "FTP Page is required" };
            }

            const ftpPageRegex = /^(\d+)\s+of\s+(\d+)$/i;
            const match = value.match(ftpPageRegex);

            if (!match) {
              return { message: "" };
            }

            const currentPage = parseInt(match[1], 10);
            const totalPages = parseInt(match[2], 10);

            if (currentPage <= 0 || totalPages <= 0) {
              return {
                message: "Page numbers must be positive (greater than 0)",
              };
            }

            if (currentPage > totalPages) {
              return {
                message: `Please enter a page number between ${totalPages} and ${currentPage} `,
              };
            }

            return { message: "Invalid page format" };
          }
        ),
      docAvailable: z.array(z.string()).optional().default([]),
      notes: z.string().optional(),
      mistake: z.string().optional(),
      legrandAlias: z.string().optional(),
      legrandCompanyName: z.string().optional(),
      legrandAddress: z.string().optional(),
      legrandZipcode: z.string().optional(),
      shipperAlias: z.string().optional(),
      shipperAddress: z.string().optional(),
      shipperZipcode: z.string().optional(),
      consigneeAlias: z.string().optional(),
      consigneeAddress: z.string().optional(),
      consigneeZipcode: z.string().optional(),
      billtoAlias: z.string().optional(),
      billtoAddress: z.string().optional(),
      billtoZipcode: z.string().optional(),
      customFields: z
        .array(
          z.object({
            id: z.string(),
            name: z.string(),
            type: z.string().optional(),
            value: z.string().optional(),
          })
        )
        .default([]),
    })
  ),
});

type CustomField = {
  id: string;
  name: string;
  type?: string;
  value?: string;
};

type FormValues = {
  associateId: string;
  clientId: string;
  entries: Array<{
    company: string;
    division: string;
    invoice: string;
    masterInvoice: string;
    bol: string;
    invoiceDate: string;
    receivedDate: string;
    shipmentDate: string;
    carrierName: string;
    invoiceStatus: string;
    manualMatching: string;
    invoiceType: string;
    currency: string;
    qtyShipped: string;
    weightUnitName: string;
    quantityBilledText: string;
    invoiceTotal: string;
    savings: string;
    ftpFileName: string;
    ftpPage: string;
    docAvailable: string[];
    notes: string;
    mistake: string;
    legrandAlias?: string;
    legrandCompanyName?: string;
    legrandAddress?: string;
    legrandZipcode?: string;
    shipperAlias?: string;
    shipperAddress?: string;
    shipperZipcode?: string;
    consigneeAlias?: string;
    consigneeAddress?: string;
    consigneeZipcode?: string;
    billtoAlias?: string;
    billtoAddress?: string;
    billtoZipcode?: string;
    customFields?: CustomField[];
  }>;
};

const CreateTrackSheet = ({ 
  client, 
  carrier, 
  associate, 
  userData,
  initialAssociateId,
  initialClientId,
  showFullForm,
  setShowFullForm  // Add this prop
}: any) => {
  const companyFieldRefs = useRef<(HTMLElement | null)[]>([]);
  const [customFields, setCustomFields] = useState<any[]>([]);
  const [generatedFilenames, setGeneratedFilenames] = useState<string[]>([]);
  const [filenameValidation, setFilenameValidation] = useState<boolean[]>([]);
  const [missingFields, setMissingFields] = useState<string[][]>([]);
  const [legrandData, setLegrandData] = useState<any[]>([]);
  const [customFieldsRefresh, setCustomFieldsRefresh] = useState<number>(0);

  // Remove these states as they're now passed as props
  // const [showFullForm, setShowFullForm] = useState(false);
  // const [initialAssociateId, setInitialAssociateId] = useState("");
  // const [initialClientId, setInitialClientId] = useState("");

  // Remove the selectionForm as it's now in the parent component

  const associateOptions = associate?.map((a: any) => ({
    value: a.id?.toString(),
    label: a.name,
    name: a.name,
  }));

  const carrierOptions = carrier?.map((c: any) => ({
    value: c.id?.toString(),
    label: c.name,
  }));

  const router = useRouter();

  const form = useForm({
    resolver: zodResolver(trackSheetSchema),
    defaultValues: {
      associateId: "",
      clientId: "",
      entries: [
        {
          company: "",
          division: "",
          invoice: "",
          masterInvoice: "",
          bol: "",
          invoiceDate: new Date().toISOString().split("T")[0],
          receivedDate: new Date().toISOString().split("T")[0],
          shipmentDate: new Date().toISOString().split("T")[0],
          carrierName: "",
          invoiceStatus: "",
          manualMatching: "",
          invoiceType: "",
          currency: "",
          qtyShipped: "",
          weightUnitName: "",
          quantityBilledText: "",
          invoiceTotal: "",
          savings: "",
          ftpFileName: "",
          ftpPage: "",
          docAvailable: [],
          notes: "",
          mistake: "",
          legrandAlias: "",
          legrandCompanyName: "",
          legrandAddress: "",
          legrandZipcode: "",
          shipperAlias: "",
          shipperAddress: "",
          shipperZipcode: "",
          consigneeAlias: "",
          consigneeAddress: "",
          consigneeZipcode: "",
          billtoAlias: "",
          billtoAddress: "",
          billtoZipcode: "",
          customFields: [],
        },
      ],
    },
  });

  const getFilteredClientOptions = () => {
    if (!initialAssociateId) {
      return client?.map((c: any) => ({
        value: c.id?.toString(),
        label: c.client_name,
        name: c.client_name,
      })) || [];
    }

    const filteredClients = client?.filter((c: any) =>
      c.associateId?.toString() === initialAssociateId
    ) || [];

    return filteredClients.map((c: any) => ({
      value: c.id?.toString(),
      label: c.client_name,
      name: c.client_name,
    }));
  };

  const clientOptions = getFilteredClientOptions();

  const entries = useWatch({ control: form.control, name: "entries" });

  const validateClientForAssociate = useCallback((associateId: string, currentClientId: string) => {
    if (associateId && currentClientId) {
      const currentClient = client?.find((c: any) => c.id?.toString() === currentClientId);
      if (currentClient && currentClient.associateId?.toString() !== associateId) {
        form.setValue("clientId", "");
        // setInitialClientId(""); // Removed as we can't modify props directly
        return false;
      }
    }
    return true;
  }, [client, form]);

  const clearEntrySpecificClients = useCallback(() => {
    const currentEntries = form.getValues("entries") || [];
    if (currentEntries.length > 0) {
      const hasEntrySpecificClients = currentEntries.some((entry: any) => entry.clientId);
      if (hasEntrySpecificClients) {
        const updatedEntries = currentEntries.map((entry: any) => ({
          ...entry,
          clientId: "", 
        }));
        form.setValue("entries", updatedEntries);
      }
    }
  }, [form]);

  const fetchLegrandData = useCallback(async () => {
    try {
      const response = await getAllData(
        legrandMapping_routes.GET_LEGRAND_MAPPINGS
      );
      if (response && Array.isArray(response)) {
        setLegrandData(response);
      }
    } catch (error) {
      toast.error("Error fetching LEGRAND mapping data:", error);
    }
  }, []);

  useMemo(() => {
    fetchLegrandData();
  }, [fetchLegrandData]);

  const handleLegrandDataChange = (
    entryIndex: number,
    businessUnit: string,
    divisionCode: string
  ) => {
    form.setValue(`entries.${entryIndex}.company`, businessUnit);

    if (divisionCode) {
      form.setValue(`entries.${entryIndex}.division`, divisionCode);
    } else {
      form.setValue(`entries.${entryIndex}.division`, "");
    }
  };

  const fetchCustomFieldsForClient = useCallback(
    async (clientId: string) => {
      if (!clientId) return [];

      try {
        const allCustomFieldsResponse = await getAllData(
          `${clientCustomFields_routes.GET_CLIENT_CUSTOM_FIELDS}/${clientId}`
        );

        let customFieldsData: any[] = [];
        if (
          allCustomFieldsResponse &&
          allCustomFieldsResponse.custom_fields &&
          allCustomFieldsResponse.custom_fields.length > 0
        ) {
          customFieldsData = allCustomFieldsResponse.custom_fields.map(
            (field: any) => {
              let autoFilledValue = "";

              if (field.type === "AUTO") {
                if (field.autoOption === "DATE") {
                  autoFilledValue = new Date().toISOString().split("T")[0];
                } else if (field.autoOption === "USERNAME") {
                  autoFilledValue = userData?.username || "";
                }
              }

              return {
                id: field.id,
                name: field.name,
                type: field.type,
                autoOption: field.autoOption,
                value: autoFilledValue,
              };
            }
          );
        }

        return customFieldsData;
      } catch (error) {
        return [];
      }
    },
    [userData]
  );

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "entries",
  });

  useMemo(() => {
    companyFieldRefs.current = companyFieldRefs.current.slice(0, fields.length);
  }, [fields.length]);

  const generateFilename = useCallback(
    (entryIndex: number, formValues: any) => {
      try {
        const entry = formValues.entries[entryIndex];
        if (!entry)
          return { filename: "", isValid: false, missing: ["Entry data"] };

        const missing: string[] = [];

        const selectedAssociate = associate?.find(
          (a: any) => a.id?.toString() === formValues.associateId
        );
        const associateName = selectedAssociate?.name || "";
        if (!associateName) {
          missing.push("Associate");
        }

        const entryClientId = (entry as any).clientId || formValues.clientId;
        const selectedClient = client?.find(
          (c: any) => c.id?.toString() === entryClientId
        );
        const clientName = selectedClient?.client_name || "";
        if (!clientName) {
          missing.push("Client");
        }

        let carrierName = "";
        if (entry.carrierName) {
          const carrierOption = carrier?.find(
            (c: any) => c.id?.toString() === entry.carrierName
          );
          carrierName = carrierOption?.name || "";
        }

        if (!carrierName) {
          missing.push("Carrier");
        }

        const receivedDate = entry.receivedDate;
        const invoiceDate = entry.invoiceDate;

        const currentDate = new Date();
        const year = currentDate.getFullYear().toString();
        const month = currentDate
          .toLocaleString("default", { month: "short" })
          .toUpperCase();

        if (!invoiceDate) {
          missing.push("Invoice Date");
        }

        let receivedDateStr = "";
        if (receivedDate) {
          const date = new Date(receivedDate);
          receivedDateStr = date.toISOString().split("T")[0];
        } else {
          missing.push("Received Date");
        }

        const ftpFileName = entry.ftpFileName || "";
        const baseFilename = ftpFileName
          ? ftpFileName.endsWith(".pdf")
            ? ftpFileName
            : `${ftpFileName}.pdf`
          : "";
        if (!baseFilename) {
          missing.push("FTP File Name");
        }

        const isValid = missing.length === 0;

        const filename = isValid
          ? `/${associateName}/${clientName}/CARRIERINVOICES/${carrierName}/${year}/${month}/${receivedDateStr}/${baseFilename}`
          : "";

        return { filename, isValid, missing };
      } catch (error) {
        return {
          filename: "",
          isValid: false,
          missing: ["Error generating filename"],
        };
      }
    },
    [client, carrier, associate]
  );
  const handleCompanyAutoPopulation = useCallback(
    (entryIndex: number, entryClientId: string) => {
      const entryClientName =
        clientOptions?.find((c: any) => c.value === entryClientId)?.name || "";
      const currentEntry = form.getValues(`entries.${entryIndex}`);

      if (entryClientName && entryClientName !== "LEGRAND") {
        form.setValue(`entries.${entryIndex}.company`, entryClientName);
      } else if (entryClientName === "LEGRAND") {
        const shipperAlias = (currentEntry as any).shipperAlias;
        const consigneeAlias = (currentEntry as any).consigneeAlias;
        const billtoAlias = (currentEntry as any).billtoAlias;
        const hasAnyLegrandData = shipperAlias || consigneeAlias || billtoAlias;

        if (!hasAnyLegrandData && currentEntry.company !== "") {
          form.setValue(`entries.${entryIndex}.company`, "");
        }
      } else {
        if (currentEntry.company !== "") {
          form.setValue(`entries.${entryIndex}.company`, "");
        }
      }
    },
    [form, clientOptions]
  );

  const handleCustomFieldsFetch = useCallback(
    async (entryIndex: number, entryClientId: string) => {
      if (!entryClientId) {
        const currentCustomFields = form.getValues(
          `entries.${entryIndex}.customFields`
        );
        if (currentCustomFields && currentCustomFields.length > 0) {
          form.setValue(`entries.${entryIndex}.customFields`, []);
        }
        return;
      }

      const currentCustomFields =
        form.getValues(`entries.${entryIndex}.customFields`) || [];

      const hasEmptyAutoUsernameFields = currentCustomFields.some(
        (field: any) =>
          field.type === "AUTO" &&
          field.autoOption === "USERNAME" &&
          !field.value &&
          userData?.username
      );

      const shouldFetchCustomFields =
        currentCustomFields.length === 0 ||
        (currentCustomFields.length > 0 && !currentCustomFields[0]?.clientId) ||
        currentCustomFields[0]?.clientId !== entryClientId ||
        hasEmptyAutoUsernameFields;

      if (shouldFetchCustomFields) {
        const customFieldsData = await fetchCustomFieldsForClient(
          entryClientId
        );

        const fieldsWithClientId = customFieldsData.map((field: any) => ({
          ...field,
          clientId: entryClientId, 
        }));

        form.setValue(`entries.${entryIndex}.customFields`, fieldsWithClientId);

        setTimeout(() => {
          fieldsWithClientId.forEach((field: any, fieldIndex: number) => {
            const fieldPath =
              `entries.${entryIndex}.customFields.${fieldIndex}.value` as any;
            if (field.value) {
              form.setValue(fieldPath, field.value);
            }
          });
          setCustomFieldsRefresh((prev) => prev + 1);
        }, 100);
      }
    },
    [form, fetchCustomFieldsForClient, userData?.username]
  );

  const updateFilenames = useCallback(() => {
    const formValues = form.getValues();
    const newFilenames: string[] = [];
    const newValidation: boolean[] = [];
    const newMissingFields: string[][] = [];

    if (formValues.entries && Array.isArray(formValues.entries)) {
      formValues.entries.forEach((_, index) => {
        const { filename, isValid, missing } = generateFilename(
          index,
          formValues
        );
        newFilenames[index] = filename;
        newValidation[index] = isValid;
        newMissingFields[index] = missing || [];
      });
    }

    setGeneratedFilenames(newFilenames);
    setFilenameValidation(newValidation);
    setMissingFields(newMissingFields);
  }, [form, generateFilename]);

  const handleInitialSelection = useCallback((associateId: string, clientId: string) => {
    form.setValue("associateId", associateId);
    form.setValue("clientId", clientId);

    setTimeout(() => {
      handleCompanyAutoPopulation(0, clientId);
      handleCustomFieldsFetch(0, clientId);
      updateFilenames();
    }, 50);

    setShowFullForm(true);
  }, [form, handleCompanyAutoPopulation, handleCustomFieldsFetch, updateFilenames, setShowFullForm]);

  useMemo(() => {
    setTimeout(() => {
      updateFilenames();
      const formValues = form.getValues();
      if (formValues.entries && Array.isArray(formValues.entries)) {
        formValues.entries.forEach((entry: any, index: number) => {
          const entryClientId =
            entry?.clientId || (index === 0 ? formValues.clientId : "");
          if (entryClientId) {
            handleCustomFieldsFetch(index, entryClientId);
          }
        });
      }
    }, 50);
  }, [updateFilenames, handleCustomFieldsFetch, form]);

  useMemo(() => {
    const subscription = form.watch((_, { name }) => {
      if (
        name &&
        (name.includes("associateId") ||
          name.includes("clientId") ||
          name.includes("carrierName") ||
          name.includes("invoiceDate") ||
          name.includes("receivedDate") ||
          name.includes("ftpFileName") ||
          name.includes("company") ||
          name.includes("division"))
      ) {
        updateFilenames();
      }
    });

    return () => subscription.unsubscribe();
  }, [form, updateFilenames]);

  const onSubmit = useCallback(
    async (values: FormValues) => {
      try {
        const currentFormValues = form.getValues();
        const currentValidation: boolean[] = [];
        const currentMissingFields: string[][] = [];
        const currentFilenames: string[] = [];

        if (
          currentFormValues.entries &&
          Array.isArray(currentFormValues.entries)
        ) {
          currentFormValues.entries.forEach((_, index) => {
            const { filename, isValid, missing } = generateFilename(
              index,
              currentFormValues
            );
            currentValidation[index] = isValid;
            currentMissingFields[index] = missing || [];
            currentFilenames[index] = filename;
          });
        }

        const allFilenamesValid = currentValidation.every((isValid) => isValid);

        if (!allFilenamesValid) {
          const invalidEntries = currentValidation
            .map((isValid, index) => ({
              index,
              isValid,
              missing: currentMissingFields[index],
            }))
            .filter((entry) => !entry.isValid);

          const errorDetails = invalidEntries
            .map(
              (entry) => `Entry ${entry.index + 1}: ${entry.missing.join(", ")}`
            )
            .join(" | ");

          toast.error(`Cannot submit: Missing fields - ${errorDetails}`);
          return;
        }
        const entries = values.entries.map((entry, index) => ({
          company: entry.company,
          division: entry.division,
          invoice: entry.invoice,
          masterInvoice: entry.masterInvoice,
          bol: entry.bol,
          invoiceDate: entry.invoiceDate,
          receivedDate: entry.receivedDate,
          shipmentDate: entry.shipmentDate,
          carrierId: entry.carrierName,
          invoiceStatus: entry.invoiceStatus,
          manualMatching: entry.manualMatching,
          invoiceType: entry.invoiceType,
          currency: entry.currency,
          qtyShipped: entry.qtyShipped,
          weightUnitName: entry.weightUnitName,
          quantityBilledText: entry.quantityBilledText,
          invoiceTotal: entry.invoiceTotal,
          savings: entry.savings,
          ftpFileName: entry.ftpFileName,
          ftpPage: entry.ftpPage,
          docAvailable: entry.docAvailable,
          notes: entry.notes,
          mistake: entry.mistake,
          filePath: generatedFilenames[index],
          customFields: entry.customFields?.map((cf) => ({
            id: cf.id,
            value: cf.value,
          })),
        }));
        const formData = {
          clientId: values.clientId,
          entries: entries,
        };

        const result = await formSubmit(
          trackSheets_routes.CREATE_TRACK_SHEETS,
          "POST",
          formData
        );

        if (result.success) {
          toast.success("All TrackSheets created successfully");
          form.reset();
          setTimeout(() => {
            handleInitialSelection(initialAssociateId, initialClientId);
          }, 100);
        } else {
          toast.error(result.message || "Failed to create TrackSheets");
        }
        router.refresh();
      } catch (error) {
        toast.error("An error occurred while creating the TrackSheets");
      }
    },
    [form, router, generateFilename, initialAssociateId, initialClientId, handleInitialSelection, generatedFilenames]
  );

  const addNewEntry = useCallback(() => {
    const newIndex = fields.length;
    append({
      clientId: initialClientId, 
      company: "", 
      division: "",
      invoice: "",
      masterInvoice: "",
      bol: "",
      invoiceDate: new Date().toISOString().split("T")[0],
      receivedDate: new Date().toISOString().split("T")[0],
      shipmentDate: new Date().toISOString().split("T")[0],
      carrierName: "",
      invoiceStatus: "",
      manualMatching: "",
      invoiceType: "",
      currency: "",
      qtyShipped: "",
      weightUnitName: "",
      quantityBilledText: "",
      invoiceTotal: "",
      savings: "",
      ftpFileName: "",
      ftpPage: "",
      docAvailable: [],
      notes: "",
      mistake: "",
      legrandAlias: "",
      legrandCompanyName: "",
      legrandAddress: "",
      legrandZipcode: "",
      shipperAlias: "",
      shipperAddress: "",
      shipperZipcode: "",
      consigneeAlias: "",
      consigneeAddress: "",
      consigneeZipcode: "",
      billtoAlias: "",
      billtoAddress: "",
      billtoZipcode: "",
      customFields: [],
    } as any);

    setTimeout(() => {
      handleCompanyAutoPopulation(newIndex, initialClientId);
      handleCustomFieldsFetch(newIndex, initialClientId);

      if (companyFieldRefs.current[newIndex]) {
        const inputElement =
          companyFieldRefs.current[newIndex]?.querySelector("input") ||
          companyFieldRefs.current[newIndex]?.querySelector("button") ||
          companyFieldRefs.current[newIndex]?.querySelector("select");

        if (inputElement) {
          inputElement.focus();
          try {
            inputElement.click();
          } catch (e) {
          }
        }
      }
      updateFilenames();
    }, 200);
  }, [
    append,
    fields.length,
    updateFilenames,
    initialClientId,
    handleCompanyAutoPopulation,
    handleCustomFieldsFetch,
  ]);

  const handleFormKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.ctrlKey && (e.key === "s" || e.key === "S")) {
      e.preventDefault(); 
      form.handleSubmit(onSubmit)(); 
    } else if (e.shiftKey && e.key === "Enter") {
      e.preventDefault(); 
      addNewEntry(); 
    } else if (
      e.key === "Enter" &&
      !e.ctrlKey &&
      !e.shiftKey &&
      !e.altKey
    ) {
      const activeElement = document.activeElement;
      const isSubmitButton = activeElement?.getAttribute("type") === "submit";

      if (isSubmitButton) {
        e.preventDefault();
        form.handleSubmit(onSubmit)(); 
      }
    }
  }, [form, onSubmit, addNewEntry]);
  const removeEntry = (index: number) => {
    if (fields.length > 1) {
      remove(index);
    } else {
      toast.error("You must have at least one entry");
    }
  };

  const getFilteredDivisionOptions = (company: string, entryIndex?: number) => {

    if (!company || !legrandData.length) {
      return [];
    }

    if (entryIndex !== undefined) {
      const formValues = form.getValues();
      const entry = formValues.entries?.[entryIndex] as any;
      const entryClientId =
        entry?.clientId || (entryIndex === 0 ? formValues.clientId : "");
      const entryClientName =
        clientOptions?.find((c: any) => c.value === entryClientId)?.name || "";

      if (entryClientName === "LEGRAND") {
        const shipperAlias = form.getValues(`entries.${entryIndex}.shipperAlias`);
        const consigneeAlias = form.getValues(`entries.${entryIndex}.consigneeAlias`);
        const billtoAlias = form.getValues(`entries.${entryIndex}.billtoAlias`);

        const currentAlias = shipperAlias || consigneeAlias || billtoAlias;

        if (currentAlias) {
          const selectedData = legrandData.find((data) => {
            const uniqueKey = `${data.customeCode}-${
              data.aliasShippingNames || data.legalName
            }-${data.shippingBillingAddress}`;
            return uniqueKey === currentAlias;
          });

          if (selectedData) {
            const baseAliasName =
              selectedData.aliasShippingNames &&
              selectedData.aliasShippingNames !== "NONE"
                ? selectedData.aliasShippingNames
                : selectedData.legalName;

            const sameAliasEntries = legrandData.filter((data) => {
              const dataAliasName =
                data.aliasShippingNames && data.aliasShippingNames !== "NONE"
                  ? data.aliasShippingNames
                  : data.legalName;
              return dataAliasName === baseAliasName;
            });

            const allDivisions: string[] = [];
            sameAliasEntries.forEach((entry) => {
              if (entry.customeCode) {
                if (entry.customeCode.includes("/")) {
                  const splitDivisions = entry.customeCode
                    .split("/")
                    .map((d: string) => d.trim());
                  allDivisions.push(...splitDivisions);
                } else {
                  allDivisions.push(entry.customeCode);
                }
              }
            });

            const uniqueDivisions = Array.from(
              new Set(allDivisions.filter((code) => code))
            );

            if (uniqueDivisions.length > 1) {
              const contextDivisions = uniqueDivisions.sort().map((code) => ({
                value: code,
                label: code,
              }));

              return contextDivisions;
            } else {
            }
          }
        }
      }
    }

    const allDivisions: string[] = [];
    legrandData
      .filter((data) => data.businessUnit === company && data.customeCode)
      .forEach((data) => {
        if (data.customeCode.includes("/")) {
          const splitDivisions = data.customeCode
            .split("/")
            .map((d: string) => d.trim());
          allDivisions.push(...splitDivisions);
        } else {
          allDivisions.push(data.customeCode);
        }
      });

    const divisions = Array.from(new Set(allDivisions.filter((code) => code)))
      .sort()
      .map((code) => ({
        value: code,
        label: code,
      }));

    return divisions;
  };

  return (
    <TooltipProvider>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="w-full px-2 py-3">
          {/* Remove the Associate and Client Selection Section */}
          
          {/* Form Section - Only show when both associate and client are selected */}
          {showFullForm && (
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                onKeyDown={handleFormKeyDown}
                className="space-y-3"
              >
                {fields.map((field, index) => (
                  <div key={field.id} className="relative">
                    {/* Entry Header - Ultra Compact */}
                    <div className="flex items-center justify-between mb-2 bg-gray-100 rounded-md px-3 py-2 border border-gray-200">
                      <div className="flex items-center space-x-2">
                        <div className="w-5 h-5 bg-gray-600 rounded-full flex items-center justify-center text-white font-semibold text-xs">
                          {index + 1}
                        </div>
                        <h2 className="text-sm font-semibold text-gray-900">
                          Entry #{index + 1}
                        </h2>
                      </div>
                    </div>

                    {/* Main Form Content - Compact Layout */}
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-3">
                      {/* Client Selection Row - For every entry */}
                      <div className="mb-3 pb-3 border-b border-gray-100">
                        {/* Client Selection & Company Information */}
                        <div className="bg-gray-50 rounded-md p-2 mb-3">
                          <div className="flex items-center space-x-2 mb-2">
                            <Building2 className="w-4 h-4 text-blue-600" />
                            <h3 className="text-sm font-semibold text-gray-900">
                              Client Information
                            </h3>
                          </div>
                          {/* Single Row - FTP File Name and FTP Page (Associate and Client now selected above) */}
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 mb-3">
                            <div className="">
                              <FormInput
                                className="mt-2"
                                form={form}
                                label="FTP File Name"
                                name={`entries.${index}.ftpFileName`}
                                type="text"
                                isRequired
                              />
                            </div>
                            <PageInput
                              className="mt-2"
                              form={form}
                              label="FTP Page"
                              name={`entries.${index}.ftpPage`}
                              isRequired
                            />
                            <div className=""></div>{" "}
                            {/* Empty third column to match Company row layout */}
                          </div>

                          {/* LEGRAND Details and Radio Button - Show only for LEGRAND client */}
                          {(() => {
                            const formValues = form.getValues();
                            const entry = formValues.entries?.[index] as any;
                            const entryClientId = entry?.clientId || formValues.clientId || "";
                            const entryClientName =
                              clientOptions?.find(
                                (c: any) => c.value === entryClientId
                              )?.name || "";
                            return entryClientName === "LEGRAND";
                          })() && (
                            <div className="mb-3">
                              <div className="grid grid-cols-1 lg:grid-cols-3 gap-3 mb-3">
                                <LegrandDetailsComponent
                                  form={form}
                                  entryIndex={index}
                                  onLegrandDataChange={handleLegrandDataChange}
                                  blockTitle="Shipper"
                                  fieldPrefix="shipper"
                                />
                                <LegrandDetailsComponent
                                  form={form}
                                  entryIndex={index}
                                  onLegrandDataChange={handleLegrandDataChange}
                                  blockTitle="Consignee"
                                  fieldPrefix="consignee"
                                />
                                <LegrandDetailsComponent
                                  form={form}
                                  entryIndex={index}
                                  onLegrandDataChange={handleLegrandDataChange}
                                  blockTitle="Bill-to"
                                  fieldPrefix="billto"
                                />
                              </div>
                            </div>
                          )}

                          {/* Third Row - Company, Division, Carrier */}
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                            <div
                              ref={(el) => {
                                companyFieldRefs.current[index] = el;
                              }}
                              className="flex flex-col mb-1 [&_input]:h-10"
                            >
                              <FormInput
                                form={form}
                                label="Company"
                                name={`entries.${index}.company`}
                                type="text"
                                disable={(() => {
                                  const formValues = form.getValues();
                                  const entry = formValues.entries?.[
                                    index
                                  ] as any;
                                  const entryClientId = entry?.clientId || formValues.clientId || "";
                                  const entryClientName =
                                    clientOptions?.find(
                                      (c: any) => c.value === entryClientId
                                    )?.name || "";
                                  return entryClientName === "LEGRAND";
                                })()}
                              />
                            </div>
                            <div className="flex flex-col">
                              {(() => {
                                const formValues = form.getValues();
                                const entry = formValues.entries?.[index] as any;
                                const entryClientId = entry?.clientId || formValues.clientId || "";
                                const entryClientName =
                                  clientOptions?.find(
                                    (c: any) => c.value === entryClientId
                                  )?.name || "";
                                const isLegrand = entryClientName === "LEGRAND";

                                return isLegrand ? (
                                  <SearchSelect
                                    form={form}
                                    name={`entries.${index}.division`}
                                    label="Division"
                                    placeholder="Search Division"
                                    disabled={false}
                                    options={getFilteredDivisionOptions(
                                      entries?.[index]?.company || "",
                                      index
                                    )}
                                  />
                                ) : (
                                  <FormInput
                                    form={form}
                                    label="Division"
                                    name={`entries.${index}.division`}
                                    type="text"
                                    placeholder="Enter Division"
                                  />
                                );
                              })()}
                            </div>
                            <div className="flex flex-col">
                              <SearchSelect
                                className="mt-0"
                                form={form}
                                name={`entries.${index}.carrierName`}
                                label="Select Carrier"
                                placeholder="Search Carrier"
                                isRequired
                                options={carrierOptions
                                  ?.filter((carrier: any) => {
                                    const currentEntries =
                                      form.getValues("entries") || [];
                                    const isSelectedInOtherEntries =
                                      currentEntries.some(
                                        (entry: any, entryIndex: number) =>
                                          entryIndex !== index &&
                                          entry.carrierName === carrier.value
                                      );
                                    return !isSelectedInOtherEntries;
                                  }) || []}
                                onValueChange={() => {
                                  setTimeout(() => updateFilenames(), 100);
                                }}
                              />
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Document Information */}
                      <div className="mb-3 pb-3 border-b border-gray-100">
                        <div className="flex items-center space-x-2 mb-2">
                          <FileText className="w-4 h-4 text-orange-600" />
                          <h3 className="text-sm font-semibold text-gray-900">
                            Document Information
                          </h3>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                          <FormInput
                            form={form}
                            label="Master Invoice"
                            name={`entries.${index}.masterInvoice`}
                            type="text"
                            onBlur={(e) => {
                              const masterInvoiceValue = e.target.value;
                              if (masterInvoiceValue) {
                                form.setValue(
                                  `entries.${index}.invoice`,
                                  masterInvoiceValue
                                );
                              }
                            }}
                          />
                          <FormInput
                            form={form}
                            label="Invoice"
                            name={`entries.${index}.invoice`}
                            type="text"
                            isRequired
                          />
                          <FormInput
                            form={form}
                            label="BOL"
                            name={`entries.${index}.bol`}
                            type="text"
                            isRequired
                          />
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                          <FormInput
                            form={form}
                            label="Received Date"
                            name={`entries.${index}.receivedDate`}
                            type="date"
                            isRequired
                          />
                          <FormInput
                            form={form}
                            label="Invoice Date"
                            name={`entries.${index}.invoiceDate`}
                            type="date"
                            isRequired
                          />
                          <FormInput
                            form={form}
                            label="Shipment Date"
                            name={`entries.${index}.shipmentDate`}
                            type="date"
                            isRequired
                          />
                        </div>
                      </div>

                      {/* Financial & Shipment Information */}
                      <div className="mb-4 pb-4 border-b border-gray-100">
                        <div className="flex items-center space-x-2 mb-3">
                          <DollarSign className="w-4 h-4 text-green-600" />
                          <h3 className="text-sm font-semibold text-gray-900">
                            Financial & Shipment
                          </h3>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
                          <FormInput
                            form={form}
                            label="Invoice Total"
                            name={`entries.${index}.invoiceTotal`}
                            type="number"
                            isRequired
                          />
                          <SearchSelect
                            form={form}
                            name={`entries.${index}.currency`}
                            label="Currency"
                            placeholder="Search currency"
                            isRequired
                            options={[
                              { value: "USD", label: "USD" },
                              { value: "CAD", label: "CAD" },
                              { value: "EUR", label: "EUR" },
                            ]}
                          />
                          <FormInput
                            form={form}
                            label="Quantity Shipped"
                            name={`entries.${index}.qtyShipped`}
                            type="number"
                            isRequired
                          />
                          <FormInput
                            form={form}
                            label="Weight Unit"
                            name={`entries.${index}.weightUnitName`}
                            type="text"
                            isRequired
                          />
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 mt-2">
                          <FormInput
                            form={form}
                            label="Savings"
                            name={`entries.${index}.savings`}
                            type="text"
                          />
                          <FormInput
                            form={form}
                            label="Invoice Type"
                            name={`entries.${index}.invoiceType`}
                            type="text"
                            isRequired
                          />
                          <FormInput
                            form={form}
                            label="Quantity Billed Text"
                            name={`entries.${index}.quantityBilledText`}
                            type="text"
                          />
                          <FormInput
                            form={form}
                            label="Invoice Status"
                            name={`entries.${index}.invoiceStatus`}
                            type="text"
                            isRequired
                          />
                        </div>
                      </div>

                      {/* Additional Information & Documents */}
                      <div className="mb-3">
                        <div className="flex items-center space-x-2 mb-2">
                          <Info className="w-4 h-4 text-gray-600" />
                          <h3 className="text-sm font-semibold text-gray-900">
                            Additional Information
                          </h3>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2">
                          <FormInput
                            form={form}
                            label="Manual Matching"
                            name={`entries.${index}.manualMatching`}
                            type="text"
                            isRequired
                          />
                          <FormInput
                            form={form}
                            label="Notes"
                            name={`entries.${index}.notes`}
                            type="text"
                          />
                          <FormInput
                            form={form}
                            label="Mistake"
                            name={`entries.${index}.mistake`}
                            type="text"
                          />
                          <div>
                            <FormCheckboxGroup
                              form={form}
                              label="Documents Available"
                              name={`entries.${index}.docAvailable`}
                              options={[
                                { label: "Invoice", value: "Invoice" },
                                { label: "BOL", value: "Bol" },
                                { label: "POD", value: "Pod" },
                                {
                                  label: "Packages List",
                                  value: "Packages List",
                                },
                              ]}
                              className="flex-row gap-2 text-xs"
                            />
                          </div>
                        </div>
                      </div>

                      {/* Custom Fields Section */}
                      {(() => {
                        const formValues = form.getValues();
                        const entry = formValues.entries?.[index] as any;
                        const customFields = entry?.customFields || [];

                        return Array.isArray(customFields) &&
                          customFields.length > 0 ? (
                          <div
                            key={`custom-fields-${index}-${customFieldsRefresh}`}
                            className="pt-3 border-t border-gray-100"
                          >
                            <div className="flex items-center space-x-2 mb-2">
                              <Hash className="w-4 h-4 text-purple-600" />
                              <h3 className="text-sm font-semibold text-gray-900">
                                Custom Fields ({customFields.length})
                              </h3>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2">
                              {customFields.map((cf: any, cfIdx: number) => {
                                const fieldType = cf.type || "TEXT";
                                const isAutoField = fieldType === "AUTO";
                                const autoOption = cf.autoOption;

                                let inputType = "text";
                                if (
                                  fieldType === "DATE" ||
                                  (isAutoField && autoOption === "DATE")
                                ) {
                                  inputType = "date";
                                } else if (fieldType === "NUMBER") {
                                  inputType = "number";
                                }

                                const fieldLabel = isAutoField
                                  ? `${cf.name} (Auto - ${autoOption})`
                                  : cf.name;

                                return (
                                  <FormInput
                                    key={cf.id}
                                    form={form}
                                    label={fieldLabel}
                                    name={`entries.${index}.customFields.${cfIdx}.value`}
                                    type={inputType}
                                    className="w-full"
                                    disable={isAutoField} 
                                  />
                                );
                              })}
                            </div>
                          </div>
                        ) : null;
                      })()}

                      {/* Entry Actions - Moved to bottom */}
                      <div className="pt-3 border-t border-gray-100 mt-3">
                        <div className="flex items-center justify-end space-x-2">
                          {/* Filename Status Tooltip */}
                          <Tooltip>
                            <TooltipTrigger asChild tabIndex={-1}>
                              <div
                                className={`w-5 h-5 rounded-full flex items-center justify-center text-white font-bold text-xs cursor-help transition-colors duration-200 ${
                                  filenameValidation[index]
                                    ? "bg-green-500 hover:bg-green-600"
                                    : "bg-orange-500 hover:bg-orange-600"
                                }`}
                                tabIndex={-1}
                                role="button"
                                aria-label={`Entry ${index + 1} filename status`}
                              >
                                !
                              </div>
                            </TooltipTrigger>
                            <TooltipContent
                              side="top"
                              align="center"
                              className="z-[9999]"
                            >
                              <div className="text-sm max-w-md">
                                <p className="font-medium mb-1">
                                  Entry #{index + 1} Filename
                                </p>
                                {filenameValidation[index] ? (
                                  <div>
                                    <p className="font-medium text-green-600 mb-2">
                                      Filename Generated
                                    </p>
                                    <p className="text-xs font-mono break-all bg-gray-100 p-2 rounded text-black">
                                      {generatedFilenames[index]}
                                    </p>
                                  </div>
                                ) : (
                                  <div>
                                    <p className="font-medium text-orange-600 mb-1">
                                      Please fill the form to generate filename
                                    </p>
                                    <p className="text-xs text-gray-600 mb-2">
                                      Missing fields:
                                    </p>
                                    <ul className="list-disc list-inside space-y-1">
                                      {missingFields[index]?.map(
                                        (field, fieldIndex) => (
                                          <li
                                            key={fieldIndex}
                                            className="text-xs"
                                          >
                                            {field}
                                          </li>
                                        )
                                      )}
                                    </ul>
                                  </div>
                                )}
                              </div>
                            </TooltipContent>
                          </Tooltip>

                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            className="h-7 w-7 p-0 hover:bg-red-50 hover:border-red-200"
                            onClick={() => removeEntry(index)}
                            disabled={fields.length <= 1}
                            tabIndex={-1}
                          >
                            <MinusCircle className="h-3 w-3 text-red-500" />
                          </Button>
                          {index === fields.length - 1 && (
                            <Tooltip>
                              <TooltipTrigger asChild tabIndex={-1}>
                                <Button
                                  type="button"
                                  variant="outline"
                                  size="sm"
                                  className="h-7 w-7 p-0 hover:bg-green-50 hover:border-green-200"
                                  onClick={addNewEntry}
                                  tabIndex={-1}
                                >
                                  <PlusCircle className="h-3 w-3 text-green-500" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent side="top" align="center">
                                <p className="text-xs">
                                  Add New Entry (Shift+Enter)
                                </p>
                              </TooltipContent>
                            </Tooltip>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}

                {/* Submit Section - Compact */}
                <div className="mt-3">
                  <div className="flex justify-center">
                    <Button
                      type="submit"
                      className="px-6 py-2 rounded-lg font-medium transition-all duration-200 shadow-md hover:shadow-lg text-sm"
                    >
                      Save
                    </Button>
                  </div>
                </div>
              </form>
            </Form>
          )}
        </div>
      </div>
    </TooltipProvider>
  );
};

export default CreateTrackSheet;
