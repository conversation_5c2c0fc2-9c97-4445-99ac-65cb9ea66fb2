"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/trackSheets/v2/page",{

/***/ "(app-pages-browser)/./app/user/trackSheets/v2/ManageTrackSheet.tsx":
/*!******************************************************!*\
  !*** ./app/user/trackSheets/v2/ManageTrackSheet.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _createTrackSheet__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./createTrackSheet */ \"(app-pages-browser)/./app/user/trackSheets/v2/createTrackSheet.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _TrackSheetContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./TrackSheetContext */ \"(app-pages-browser)/./app/user/trackSheets/v2/TrackSheetContext.tsx\");\n/* harmony import */ var _ClientSelectPage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ClientSelectPage */ \"(app-pages-browser)/./app/user/trackSheets/v2/ClientSelectPage.tsx\");\n/* harmony import */ var _components_sidebar_Sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/sidebar/Sidebar */ \"(app-pages-browser)/./components/sidebar/Sidebar.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./components/ui/sidebar.tsx\");\n/* harmony import */ var _app_component_BreadCrumbs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/_component/BreadCrumbs */ \"(app-pages-browser)/./app/_component/BreadCrumbs.tsx\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./components/ui/form.tsx\");\n/* harmony import */ var _app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/_component/SearchSelect */ \"(app-pages-browser)/./app/_component/SearchSelect.tsx\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _barrel_optimize_names_Building2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Building2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst ManageWorkSheet = (param)=>{\n    let { permissions, client, carrier, associate, userData, actions } = param;\n    _s();\n    const [filterdata, setFilterData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [deleteData, setDeletedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [recievedFDate, setRecievedFDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [recievedTDate, setRecievedTDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [invoiceFDate, setInvoiceFDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [invoiceTDate, setInvoiceTDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [shipmentFDate, setShipmentFDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [shipmentTDate, setShipmentTDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [activeView, setActiveView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"view\"); // Default to view mode\n    const [showSelectionForm, setShowSelectionForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedAssociateId, setSelectedAssociateId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedClientId, setSelectedClientId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showFullForm, setShowFullForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const selectionForm = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_11__.useForm)({\n        defaultValues: {\n            associateId: \"\",\n            clientId: \"\"\n        }\n    });\n    const associateOptions = associate === null || associate === void 0 ? void 0 : associate.map((a)=>{\n        var _a_id;\n        return {\n            value: (_a_id = a.id) === null || _a_id === void 0 ? void 0 : _a_id.toString(),\n            label: a.name,\n            name: a.name\n        };\n    });\n    const getFilteredClientOptions = ()=>{\n        if (!selectedAssociateId) {\n            return (client === null || client === void 0 ? void 0 : client.map((c)=>{\n                var _c_id;\n                return {\n                    value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n                    label: c.client_name,\n                    name: c.client_name\n                };\n            })) || [];\n        }\n        const filteredClients = (client === null || client === void 0 ? void 0 : client.filter((c)=>{\n            var _c_associateId;\n            return ((_c_associateId = c.associateId) === null || _c_associateId === void 0 ? void 0 : _c_associateId.toString()) === selectedAssociateId;\n        })) || [];\n        return filteredClients.map((c)=>{\n            var _c_id;\n            return {\n                value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n                label: c.client_name,\n                name: c.client_name\n            };\n        });\n    };\n    const clientOptions = getFilteredClientOptions();\n    const handleCreateTrackSheetClick = ()=>{\n        setActiveView(\"create\");\n        setShowSelectionForm(true);\n        setShowFullForm(false);\n        setSelectedAssociateId(\"\");\n        setSelectedClientId(\"\");\n        selectionForm.reset();\n    };\n    const handleAssociateChange = (value)=>{\n        setSelectedAssociateId(value);\n        setSelectedClientId(\"\");\n        selectionForm.setValue(\"clientId\", \"\");\n        setShowFullForm(false);\n    };\n    const handleClientChange = (value)=>{\n        setSelectedClientId(value);\n        if (value && selectedAssociateId) {\n            setShowFullForm(true);\n        } else {\n            setShowFullForm(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TrackSheetContext__WEBPACK_IMPORTED_MODULE_4__.TrackSheetContext.Provider, {\n        value: {\n            filterdata,\n            setFilterData,\n            deleteData,\n            setDeletedData,\n            recievedFDate,\n            recievedTDate,\n            setRecievedFDate,\n            setRecievedTDate,\n            invoiceFDate,\n            setInvoiceFDate,\n            invoiceTDate,\n            setInvoiceTDate,\n            shipmentFDate,\n            setShipmentFDate,\n            shipmentTDate,\n            setShipmentTDate\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_7__.SidebarProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex w-full min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sidebar_Sidebar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        permissions: permissions,\n                        profile: userData\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 w-full pl-3 overflow-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_BreadCrumbs__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    breadcrumblist: [\n                                        {\n                                            link: \"/user/trackSheets\",\n                                            name: \"TrackSheet\"\n                                        }\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-4 pl-3 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"default\",\n                                        onClick: ()=>setActiveView(\"view\"),\n                                        className: \"w-40 shadow-md rounded-xl text-base transition-all duration-200 \".concat(activeView === \"view\" ? \"bg-neutral-800 hover:bg-neutral-900 text-white\" : \"bg-white text-neutral-800 border border-neutral-300 hover:bg-neutral-100\"),\n                                        children: \"View TrackSheet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"default\",\n                                        onClick: handleCreateTrackSheetClick,\n                                        className: \"w-40 shadow-md rounded-xl text-base transition-all duration-200 \".concat(activeView === \"create\" ? \"bg-neutral-800 hover:bg-neutral-900 text-white\" : \"bg-white text-neutral-800 border border-neutral-300 hover:bg-neutral-100\"),\n                                        children: \"Create TrackSheet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, undefined),\n                            activeView === \"create\" && showSelectionForm && !showFullForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4 mx-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-5 h-5 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-900\",\n                                                children: \"Select Associate and Client\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_9__.Form, {\n                                        ...selectionForm,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        form: selectionForm,\n                                                        name: \"associateId\",\n                                                        label: \"Select Associate\",\n                                                        placeholder: \"Search Associate...\",\n                                                        isRequired: true,\n                                                        options: associateOptions || [],\n                                                        onValueChange: handleAssociateChange\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        form: selectionForm,\n                                                        name: \"clientId\",\n                                                        label: \"Select Client\",\n                                                        placeholder: \"Search Client...\",\n                                                        isRequired: true,\n                                                        disabled: !selectedAssociateId,\n                                                        options: clientOptions || [],\n                                                        onValueChange: handleClientChange\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full animate-in fade-in duration-500 rounded-2xl shadow-sm  dark:bg-gray-800 p-1\",\n                                children: activeView === \"create\" && showFullForm ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_createTrackSheet__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    client: client,\n                                    carrier: carrier,\n                                    associate: associate,\n                                    userData: userData,\n                                    initialAssociateId: selectedAssociateId,\n                                    initialClientId: selectedClientId\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 17\n                                }, undefined) : activeView === \"view\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClientSelectPage__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    permissions: actions,\n                                    client: client\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 17\n                                }, undefined) : null\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n                lineNumber: 120,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n            lineNumber: 119,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\v2\\\\ManageTrackSheet.tsx\",\n        lineNumber: 99,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ManageWorkSheet, \"hDFQLejj0/iVFGLLfI4LJ//ZKTo=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_11__.useForm\n    ];\n});\n_c = ManageWorkSheet;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ManageWorkSheet);\nvar _c;\n$RefreshReg$(_c, \"ManageWorkSheet\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/trackSheets/v2/ManageTrackSheet.tsx\n"));

/***/ })

});